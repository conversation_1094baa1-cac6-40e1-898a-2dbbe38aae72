<?php

declare(strict_types=1);

namespace App\Services\Tools\Built;

use App\Services\Tools\AbstractTool;

class CalendarTool extends AbstractTool
{
    protected array $requiredParams = ['title', 'date', 'time'];
    protected array $optionalParams = ['duration', 'description', 'attendees'];

    public function getName(): string
    {
        return 'calendar';
    }

    public function getDescription(): string
    {
        return 'Create calendar events and manage scheduling';
    }

    protected function initialize(): void
    {
        $this->config = array_merge([
            'default_duration' => 60, // minutes
            'timezone' => 'UTC',
            'simulate_only' => true // For demo purposes
        ], $this->config);
    }

    public function isAvailable(): bool
    {
        return true; // Always available for demo
    }

    protected function process(array $params, string $sessionId): array
    {
        $title = $params['title'];
        $date = $params['date'];
        $time = $params['time'];
        $duration = $params['duration'] ?? $this->config['default_duration'];
        $description = $params['description'] ?? '';
        $attendees = (array) ($params['attendees'] ?? []);

        // Parse and validate date/time
        try {
            $startDateTime = new \DateTime("{$date} {$time}", new \DateTimeZone($this->config['timezone']));
            $endDateTime = clone $startDateTime;
            $endDateTime->add(new \DateInterval("PT{$duration}M"));
        } catch (\Exception $e) {
            throw new \InvalidArgumentException("Invalid date/time format");
        }

        // For demo purposes, simulate calendar event creation
        if ($this->config['simulate_only']) {
            return $this->formatResponse([
                'event_id' => 'evt_' . uniqid(),
                'title' => $title,
                'start_time' => $startDateTime->format('Y-m-d H:i:s'),
                'end_time' => $endDateTime->format('Y-m-d H:i:s'),
                'duration_minutes' => $duration,
                'description' => $description,
                'attendees' => $attendees,
                'timezone' => $this->config['timezone'],
                'simulated' => true
            ], "Calendar event '{$title}' created for {$startDateTime->format('Y-m-d H:i')}");
        }

        // In a real implementation, this would integrate with calendar APIs
        // like Google Calendar, Outlook, etc.
        
        return $this->formatResponse([
            'event_id' => 'evt_' . uniqid(),
            'title' => $title,
            'start_time' => $startDateTime->format('Y-m-d H:i:s'),
            'end_time' => $endDateTime->format('Y-m-d H:i:s'),
            'duration_minutes' => $duration,
            'description' => $description,
            'attendees' => $attendees,
            'timezone' => $this->config['timezone']
        ], "Calendar event '{$title}' created successfully");
    }
}
