<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Workflow;
use App\Services\WorkflowExecutionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class WorkflowController extends Controller
{
    protected WorkflowExecutionService $executionService;

    public function __construct(WorkflowExecutionService $executionService)
    {
        $this->executionService = $executionService;
    }

    /**
     * Get all workflows
     */
    public function index(): JsonResponse
    {
        try {
            $workflows = Workflow::orderBy('created_at', 'desc')
                ->get()
                ->map(function ($workflow) {
                    return [
                        'id' => $workflow->id,
                        'name' => $workflow->name,
                        'description' => $workflow->description,
                        'status' => $workflow->status,
                        'trigger_type' => $workflow->trigger_type,
                        'execution_count' => $workflow->execution_count,
                        'last_executed_at' => $workflow->last_executed_at,
                        'created_at' => $workflow->created_at,
                        'statistics' => $workflow->getStatistics(),
                        'node_count' => count($workflow->nodes ?? [])
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $workflows
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new workflow
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'description' => 'nullable|string|max:1000',
                'trigger_type' => 'required|in:manual,webhook,schedule,event',
                'trigger_config' => 'nullable|array',
                'nodes' => 'nullable|array',
                'variables' => 'nullable|array',
                'settings' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $workflow = Workflow::create([
                'name' => $request->name,
                'description' => $request->description,
                'status' => 'draft',
                'trigger_type' => $request->trigger_type,
                'trigger_config' => $request->trigger_config ?? [],
                'nodes' => $request->nodes ?? Workflow::getDefaultNodes(),
                'variables' => $request->variables ?? [],
                'settings' => $request->settings ?? []
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $workflow->id,
                    'name' => $workflow->name,
                    'description' => $workflow->description,
                    'status' => $workflow->status,
                    'trigger_type' => $workflow->trigger_type,
                    'nodes' => $workflow->nodes
                ],
                'message' => 'Workflow created successfully'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to create workflow: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific workflow
     */
    public function show(Workflow $workflow): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $workflow->id,
                    'name' => $workflow->name,
                    'description' => $workflow->description,
                    'status' => $workflow->status,
                    'trigger_type' => $workflow->trigger_type,
                    'trigger_config' => $workflow->trigger_config,
                    'nodes' => $workflow->nodes,
                    'variables' => $workflow->variables,
                    'settings' => $workflow->settings,
                    'execution_count' => $workflow->execution_count,
                    'last_executed_at' => $workflow->last_executed_at,
                    'created_at' => $workflow->created_at,
                    'updated_at' => $workflow->updated_at,
                    'statistics' => $workflow->getStatistics(),
                    'validation_errors' => $workflow->validateStructure()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a workflow
     */
    public function update(Request $request, Workflow $workflow): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'sometimes|string|max:255',
                'description' => 'nullable|string|max:1000',
                'status' => 'sometimes|in:draft,active,inactive,archived',
                'trigger_type' => 'sometimes|in:manual,webhook,schedule,event',
                'trigger_config' => 'nullable|array',
                'nodes' => 'nullable|array',
                'variables' => 'nullable|array',
                'settings' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $workflow->update($request->only([
                'name', 'description', 'status', 'trigger_type', 
                'trigger_config', 'nodes', 'variables', 'settings'
            ]));

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $workflow->id,
                    'name' => $workflow->name,
                    'status' => $workflow->status,
                    'validation_errors' => $workflow->validateStructure()
                ],
                'message' => 'Workflow updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to update workflow: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a workflow
     */
    public function destroy(Workflow $workflow): JsonResponse
    {
        try {
            $workflow->delete();

            return response()->json([
                'success' => true,
                'message' => 'Workflow deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to delete workflow: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Execute a workflow
     */
    public function execute(Request $request, Workflow $workflow): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'input_data' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $execution = $this->executionService->execute(
                $workflow,
                $request->input_data ?? []
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'execution_id' => $execution->id,
                    'status' => $execution->status,
                    'started_at' => $execution->started_at,
                    'output_data' => $execution->output_data
                ],
                'message' => 'Workflow execution started'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to execute workflow: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get workflow executions
     */
    public function getExecutions(Workflow $workflow, Request $request): JsonResponse
    {
        try {
            $perPage = min($request->integer('per_page', 20), 100);
            $status = $request->string('status');
            
            $query = $workflow->executions()->orderBy('started_at', 'desc');
            
            if ($status) {
                $query->where('status', $status);
            }
            
            $executions = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $executions->items(),
                'pagination' => [
                    'current_page' => $executions->currentPage(),
                    'last_page' => $executions->lastPage(),
                    'per_page' => $executions->perPage(),
                    'total' => $executions->total()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clone a workflow
     */
    public function clone(Request $request, Workflow $workflow): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $clone = $workflow->clone($request->name);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $clone->id,
                    'name' => $clone->name,
                    'status' => $clone->status
                ],
                'message' => 'Workflow cloned successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to clone workflow: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export workflow
     */
    public function export(Workflow $workflow): JsonResponse
    {
        try {
            $exportData = $workflow->export();

            return response()->json([
                'success' => true,
                'data' => $exportData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to export workflow: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Import workflow
     */
    public function import(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'workflow_data' => 'required|array',
                'workflow_data.name' => 'required|string',
                'workflow_data.trigger_type' => 'required|string',
                'workflow_data.nodes' => 'required|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $workflow = Workflow::import($request->workflow_data);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $workflow->id,
                    'name' => $workflow->name,
                    'status' => $workflow->status
                ],
                'message' => 'Workflow imported successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to import workflow: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available node types
     */
    public function getNodeTypes(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => Workflow::getNodeTypes()
        ]);
    }

    /**
     * Validate workflow structure
     */
    public function validate(Workflow $workflow): JsonResponse
    {
        try {
            $errors = $workflow->validateStructure();

            return response()->json([
                'success' => true,
                'data' => [
                    'is_valid' => empty($errors),
                    'errors' => $errors
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
