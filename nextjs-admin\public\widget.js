/**
 * Axient MCP++ Widget SDK
 * Embeddable chat widget for websites
 */

(function(window, document) {
  'use strict';

  // Prevent multiple initializations
  if (window.AxientWidget) {
    return;
  }

  class AxientWidget {
    constructor() {
      this.config = {};
      this.styling = {};
      this.behavior = {};
      this.isOpen = false;
      this.sessionId = null;
      this.messageHistory = [];
      this.container = null;
      this.iframe = null;
      this.button = null;
      this.apiUrl = '';
      this.widgetId = '';
    }

    init(options = {}) {
      this.widgetId = options.widgetId;
      this.apiUrl = options.apiUrl || '';
      this.config = { ...this.getDefaultConfig(), ...options.config };
      this.styling = { ...this.getDefaultStyling(), ...options.styling };
      this.behavior = { ...this.getDefaultBehavior(), ...options.behavior };

      this.sessionId = this.generateSessionId();
      this.loadStoredData();
      this.createWidget();
      this.bindEvents();
      this.trackUsage();

      if (this.config.auto_open && this.behavior.auto_open_delay > 0) {
        setTimeout(() => this.open(), this.behavior.auto_open_delay);
      }
    }

    getDefaultConfig() {
      return {
        title: 'Chat with us',
        placeholder: 'Type your message...',
        welcome_message: 'Hello! How can I help you today?',
        theme: 'light',
        position: 'bottom-right',
        auto_open: false,
        show_branding: true,
        enable_file_upload: true,
        max_file_size: 10,
        allowed_file_types: ['pdf', 'doc', 'txt']
      };
    }

    getDefaultStyling() {
      return {
        primary_color: '#3b82f6',
        secondary_color: '#64748b',
        text_color: '#1f2937',
        background_color: '#ffffff',
        border_radius: 8,
        font_family: 'Inter, sans-serif',
        font_size: 14,
        button_style: 'rounded',
        animation: 'slide',
        shadow: true,
        width: 400,
        height: 600
      };
    }

    getDefaultBehavior() {
      return {
        auto_open_delay: 3000,
        close_on_outside_click: true,
        remember_conversation: true,
        typing_indicator: true,
        sound_notifications: false,
        keyboard_shortcuts: true,
        rate_limiting: true,
        max_messages_per_hour: 50
      };
    }

    createWidget() {
      this.createButton();
      this.createContainer();
      this.injectStyles();
    }

    createButton() {
      this.button = document.createElement('div');
      this.button.className = 'axient-widget-button';
      this.button.innerHTML = `
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2C6.48 2 2 6.48 2 12C2 13.54 2.36 14.99 3.01 16.28L2 22L7.72 20.99C9.01 21.64 10.46 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C10.74 20 9.54 19.75 8.46 19.3L6 20L6.7 17.54C6.25 16.46 6 15.26 6 14C6 8.48 8.48 6 12 6C15.52 6 18 8.48 18 12C18 15.52 15.52 18 12 18Z" fill="white"/>
        </svg>
      `;

      this.applyButtonStyles();
      document.body.appendChild(this.button);
    }

    createContainer() {
      this.container = document.createElement('div');
      this.container.className = 'axient-widget-container';
      this.container.style.display = 'none';

      const header = document.createElement('div');
      header.className = 'axient-widget-header';
      header.innerHTML = `
        <div class="axient-widget-title">${this.config.title}</div>
        <button class="axient-widget-close">×</button>
      `;

      this.iframe = document.createElement('iframe');
      this.iframe.className = 'axient-widget-iframe';
      this.iframe.src = this.buildIframeUrl();
      this.iframe.frameBorder = '0';

      const footer = document.createElement('div');
      footer.className = 'axient-widget-footer';
      if (this.config.show_branding) {
        footer.innerHTML = '<div class="axient-widget-branding">Powered by Axient MCP++</div>';
      }

      this.container.appendChild(header);
      this.container.appendChild(this.iframe);
      if (this.config.show_branding) {
        this.container.appendChild(footer);
      }

      this.applyContainerStyles();
      document.body.appendChild(this.container);
    }

    buildIframeUrl() {
      const params = new URLSearchParams({
        widgetId: this.widgetId,
        sessionId: this.sessionId,
        config: JSON.stringify(this.config),
        styling: JSON.stringify(this.styling),
        behavior: JSON.stringify(this.behavior),
        origin: window.location.origin
      });

      return `${this.apiUrl.replace('/api', '')}/widget/chat?${params.toString()}`;
    }

    applyButtonStyles() {
      const position = this.config.position.split('-');
      const vertical = position[0];
      const horizontal = position[1];

      this.button.style.cssText = `
        position: fixed;
        ${vertical}: 20px;
        ${horizontal}: 20px;
        width: 60px;
        height: 60px;
        background-color: ${this.styling.primary_color};
        border-radius: ${this.styling.button_style === 'rounded' ? '50%' : this.styling.border_radius + 'px'};
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 999999;
        transition: all 0.3s ease;
        ${this.styling.shadow ? 'box-shadow: 0 4px 12px rgba(0,0,0,0.15);' : ''}
      `;
    }

    applyContainerStyles() {
      const position = this.config.position.split('-');
      const vertical = position[0];
      const horizontal = position[1];

      this.container.style.cssText = `
        position: fixed;
        ${vertical}: 90px;
        ${horizontal}: 20px;
        width: ${this.styling.width}px;
        height: ${this.styling.height}px;
        background-color: ${this.styling.background_color};
        border-radius: ${this.styling.border_radius}px;
        z-index: 999998;
        font-family: ${this.styling.font_family};
        font-size: ${this.styling.font_size}px;
        color: ${this.styling.text_color};
        ${this.styling.shadow ? 'box-shadow: 0 8px 24px rgba(0,0,0,0.15);' : ''}
        overflow: hidden;
        transition: all 0.3s ease;
      `;

      // Header styles
      const header = this.container.querySelector('.axient-widget-header');
      if (header) {
        header.style.cssText = `
          background-color: ${this.styling.primary_color};
          color: white;
          padding: 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-weight: 600;
        `;
      }

      // Close button styles
      const closeBtn = this.container.querySelector('.axient-widget-close');
      if (closeBtn) {
        closeBtn.style.cssText = `
          background: none;
          border: none;
          color: white;
          font-size: 24px;
          cursor: pointer;
          padding: 0;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
        `;
      }

      // Iframe styles
      if (this.iframe) {
        this.iframe.style.cssText = `
          width: 100%;
          height: calc(100% - 64px);
          border: none;
        `;
      }

      // Footer styles
      const footer = this.container.querySelector('.axient-widget-footer');
      if (footer) {
        footer.style.cssText = `
          padding: 8px 16px;
          background-color: ${this.styling.secondary_color};
          color: white;
          font-size: 12px;
          text-align: center;
        `;
      }
    }

    injectStyles() {
      if (document.getElementById('axient-widget-styles')) {
        return;
      }

      const styles = document.createElement('style');
      styles.id = 'axient-widget-styles';
      styles.textContent = `
        .axient-widget-button:hover {
          transform: scale(1.1);
        }
        
        .axient-widget-container.axient-widget-open {
          display: block !important;
          animation: axientSlideIn 0.3s ease-out;
        }
        
        .axient-widget-container.axient-widget-closing {
          animation: axientSlideOut 0.3s ease-in;
        }
        
        @keyframes axientSlideIn {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        @keyframes axientSlideOut {
          from {
            opacity: 1;
            transform: translateY(0);
          }
          to {
            opacity: 0;
            transform: translateY(20px);
          }
        }
        
        @media (max-width: 768px) {
          .axient-widget-container {
            width: calc(100vw - 40px) !important;
            height: calc(100vh - 120px) !important;
            left: 20px !important;
            right: 20px !important;
            bottom: 20px !important;
          }
        }
      `;

      document.head.appendChild(styles);
    }

    bindEvents() {
      // Button click
      this.button.addEventListener('click', () => {
        this.toggle();
      });

      // Close button click
      const closeBtn = this.container.querySelector('.axient-widget-close');
      if (closeBtn) {
        closeBtn.addEventListener('click', () => {
          this.close();
        });
      }

      // Outside click
      if (this.behavior.close_on_outside_click) {
        document.addEventListener('click', (e) => {
          if (this.isOpen && !this.container.contains(e.target) && !this.button.contains(e.target)) {
            this.close();
          }
        });
      }

      // Keyboard shortcuts
      if (this.behavior.keyboard_shortcuts) {
        document.addEventListener('keydown', (e) => {
          if (e.key === 'Escape' && this.isOpen) {
            this.close();
          }
        });
      }

      // Message from iframe
      window.addEventListener('message', (e) => {
        if (e.origin !== window.location.origin) return;
        
        if (e.data.type === 'axient-widget-message') {
          this.handleMessage(e.data.payload);
        }
      });
    }

    handleMessage(payload) {
      switch (payload.action) {
        case 'resize':
          this.resize(payload.width, payload.height);
          break;
        case 'close':
          this.close();
          break;
        case 'notification':
          this.showNotification(payload.message);
          break;
      }
    }

    open() {
      if (this.isOpen) return;
      
      this.isOpen = true;
      this.container.style.display = 'block';
      this.container.classList.add('axient-widget-open');
      this.trackEvent('widget_opened');
    }

    close() {
      if (!this.isOpen) return;
      
      this.isOpen = false;
      this.container.classList.add('axient-widget-closing');
      
      setTimeout(() => {
        this.container.style.display = 'none';
        this.container.classList.remove('axient-widget-open', 'axient-widget-closing');
      }, 300);
      
      this.trackEvent('widget_closed');
    }

    toggle() {
      if (this.isOpen) {
        this.close();
      } else {
        this.open();
      }
    }

    resize(width, height) {
      if (width) this.container.style.width = width + 'px';
      if (height) this.container.style.height = height + 'px';
    }

    showNotification(message) {
      if (!this.behavior.sound_notifications) return;
      
      // Simple notification sound
      const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
      audio.volume = 0.3;
      audio.play().catch(() => {});
    }

    generateSessionId() {
      return 'axient_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    }

    loadStoredData() {
      if (!this.behavior.remember_conversation) return;
      
      try {
        const stored = localStorage.getItem(`axient_widget_${this.widgetId}`);
        if (stored) {
          const data = JSON.parse(stored);
          this.messageHistory = data.messageHistory || [];
          this.sessionId = data.sessionId || this.sessionId;
        }
      } catch (e) {
        console.warn('Failed to load stored widget data:', e);
      }
    }

    saveData() {
      if (!this.behavior.remember_conversation) return;
      
      try {
        const data = {
          sessionId: this.sessionId,
          messageHistory: this.messageHistory,
          lastUsed: Date.now()
        };
        localStorage.setItem(`axient_widget_${this.widgetId}`, JSON.stringify(data));
      } catch (e) {
        console.warn('Failed to save widget data:', e);
      }
    }

    trackUsage() {
      this.trackEvent('widget_loaded');
    }

    trackEvent(eventName, data = {}) {
      if (!this.apiUrl) return;
      
      fetch(`${this.apiUrl}/analytics/track-event`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          event_type: 'widget_interaction',
          event_name: eventName,
          entity_type: 'widget',
          entity_id: this.widgetId,
          properties: {
            session_id: this.sessionId,
            widget_id: this.widgetId,
            url: window.location.href,
            ...data
          }
        })
      }).catch(() => {});
    }

    destroy() {
      if (this.button) {
        this.button.remove();
      }
      if (this.container) {
        this.container.remove();
      }
      
      const styles = document.getElementById('axient-widget-styles');
      if (styles) {
        styles.remove();
      }
    }
  }

  // Global API
  window.AxientWidget = {
    init: function(options) {
      if (window.axientWidgetInstance) {
        window.axientWidgetInstance.destroy();
      }
      
      window.axientWidgetInstance = new AxientWidget();
      window.axientWidgetInstance.init(options);
      
      return window.axientWidgetInstance;
    },
    
    getInstance: function() {
      return window.axientWidgetInstance;
    }
  };

})(window, document);
