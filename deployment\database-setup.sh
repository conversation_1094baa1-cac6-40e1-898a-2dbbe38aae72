#!/bin/bash

# =============================================================================
# AXIENT MCP++ DATABASE SETUP SCRIPT
# Production Database Migration and Configuration
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_HOST="${DB_HOST:-your-production-db-host.amazonaws.com}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_DATABASE:-axient_mcp_production}"
DB_USER="${DB_USERNAME:-axient_user}"
DB_PASSWORD="${DB_PASSWORD}"
BACKUP_BUCKET="${BACKUP_S3_BUCKET:-axient-mcp-backups-prod}"

echo -e "${BLUE}==============================================================================${NC}"
echo -e "${BLUE}AXIENT MCP++ PRODUCTION DATABASE SETUP${NC}"
echo -e "${BLUE}==============================================================================${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if psql is installed
    if ! command -v psql &> /dev/null; then
        print_error "PostgreSQL client (psql) is not installed"
        exit 1
    fi
    
    # Check if AWS CLI is installed
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed"
        exit 1
    fi
    
    # Check if PHP and Artisan are available
    if ! command -v php &> /dev/null; then
        print_error "PHP is not installed"
        exit 1
    fi
    
    if [ ! -f "artisan" ]; then
        print_error "Laravel artisan command not found. Run this script from the Laravel project root."
        exit 1
    fi
    
    print_status "Prerequisites check passed"
}

# Test database connection
test_connection() {
    print_status "Testing database connection..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT version();" > /dev/null 2>&1; then
        print_status "Database connection successful"
    else
        print_error "Failed to connect to database"
        print_error "Host: $DB_HOST:$DB_PORT"
        print_error "Database: $DB_NAME"
        print_error "User: $DB_USER"
        exit 1
    fi
}

# Create database backup before migration
create_backup() {
    print_status "Creating database backup before migration..."
    
    BACKUP_FILE="axient_mcp_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    export PGPASSWORD="$DB_PASSWORD"
    
    if pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" > "$BACKUP_FILE"; then
        print_status "Database backup created: $BACKUP_FILE"
        
        # Upload to S3
        if aws s3 cp "$BACKUP_FILE" "s3://$BACKUP_BUCKET/pre-migration/"; then
            print_status "Backup uploaded to S3: s3://$BACKUP_BUCKET/pre-migration/$BACKUP_FILE"
            rm "$BACKUP_FILE"
        else
            print_warning "Failed to upload backup to S3, keeping local copy"
        fi
    else
        print_error "Failed to create database backup"
        exit 1
    fi
}

# Setup pgvector extension
setup_pgvector() {
    print_status "Setting up pgvector extension..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    # Check if pgvector is already installed
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT * FROM pg_extension WHERE extname = 'vector';" | grep -q vector; then
        print_status "pgvector extension already installed"
    else
        print_status "Installing pgvector extension..."
        psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "CREATE EXTENSION IF NOT EXISTS vector;"
        print_status "pgvector extension installed successfully"
    fi
}

# Run Laravel migrations
run_migrations() {
    print_status "Running Laravel migrations..."
    
    # Clear config cache
    php artisan config:clear
    
    # Run migrations
    if php artisan migrate --force; then
        print_status "Migrations completed successfully"
    else
        print_error "Migration failed"
        exit 1
    fi
}

# Seed initial data
seed_data() {
    print_status "Seeding initial production data..."
    
    # Run production seeders
    if php artisan db:seed --class=ProductionSeeder --force; then
        print_status "Production data seeded successfully"
    else
        print_warning "Production seeder failed or not found"
    fi
}

# Setup database indexes for performance
setup_indexes() {
    print_status "Setting up performance indexes..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    # Create custom indexes for better performance
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << EOF
-- Tenant-related indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscriptions_tenant_id ON subscriptions(tenant_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_widgets_tenant_id ON widgets(tenant_id);

-- Performance indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_documents_status ON documents(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_workflows_status ON workflows(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analytics_events_occurred_at ON analytics_events(occurred_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analytics_events_event_type ON analytics_events(event_type);

-- Vector search indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_document_chunks_embedding ON document_chunks USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Composite indexes for common queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscriptions_tenant_status ON subscriptions(tenant_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_workflow_executions_workflow_status ON workflow_executions(workflow_id, status);
EOF

    print_status "Performance indexes created"
}

# Optimize database settings
optimize_database() {
    print_status "Optimizing database settings..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    # Set optimal PostgreSQL settings for production
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << EOF
-- Optimize for vector operations
SET shared_preload_libraries = 'vector';
SET max_connections = 200;
SET shared_buffers = '256MB';
SET effective_cache_size = '1GB';
SET maintenance_work_mem = '64MB';
SET checkpoint_completion_target = 0.9;
SET wal_buffers = '16MB';
SET default_statistics_target = 100;
SET random_page_cost = 1.1;
SET effective_io_concurrency = 200;

-- Optimize for analytics queries
SET work_mem = '4MB';
SET max_worker_processes = 8;
SET max_parallel_workers_per_gather = 2;
SET max_parallel_workers = 8;
EOF

    print_status "Database optimization completed"
}

# Verify installation
verify_installation() {
    print_status "Verifying installation..."
    
    # Check if all tables exist
    export PGPASSWORD="$DB_PASSWORD"
    
    TABLES=(
        "users" "tenants" "subscriptions" "ai_providers" 
        "knowledge_bases" "documents" "document_chunks"
        "workflows" "workflow_executions" "widgets"
        "analytics_events" "agent_executions"
    )
    
    for table in "${TABLES[@]}"; do
        if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "\dt $table" | grep -q "$table"; then
            print_status "✓ Table $table exists"
        else
            print_error "✗ Table $table missing"
            exit 1
        fi
    done
    
    # Check pgvector extension
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT * FROM pg_extension WHERE extname = 'vector';" | grep -q vector; then
        print_status "✓ pgvector extension installed"
    else
        print_error "✗ pgvector extension missing"
        exit 1
    fi
    
    print_status "Database verification completed successfully"
}

# Main execution
main() {
    echo -e "${BLUE}Starting database setup...${NC}"
    
    # Confirm production deployment
    echo -e "${YELLOW}WARNING: This will set up the PRODUCTION database.${NC}"
    echo -e "${YELLOW}Database: $DB_HOST:$DB_PORT/$DB_NAME${NC}"
    read -p "Are you sure you want to continue? (yes/no): " confirm
    
    if [ "$confirm" != "yes" ]; then
        print_error "Database setup cancelled"
        exit 1
    fi
    
    check_prerequisites
    test_connection
    create_backup
    setup_pgvector
    run_migrations
    seed_data
    setup_indexes
    optimize_database
    verify_installation
    
    echo -e "${GREEN}==============================================================================${NC}"
    echo -e "${GREEN}DATABASE SETUP COMPLETED SUCCESSFULLY${NC}"
    echo -e "${GREEN}==============================================================================${NC}"
    echo -e "${GREEN}Database: $DB_HOST:$DB_PORT/$DB_NAME${NC}"
    echo -e "${GREEN}Backup: s3://$BACKUP_BUCKET/pre-migration/${NC}"
    echo -e "${GREEN}Status: Ready for production use${NC}"
    echo -e "${GREEN}==============================================================================${NC}"
}

# Run main function
main "$@"
