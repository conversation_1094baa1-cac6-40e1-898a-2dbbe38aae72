'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { apiClient, type Conversation } from '@/lib/api'
import { formatDate, truncateText } from '@/lib/utils'
import { MessageSquare, User, Clock, RefreshCw } from 'lucide-react'

export default function ConversationsPage() {
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null)

  const fetchConversations = async () => {
    setIsLoading(true)
    try {
      const response = await apiClient.getConversations()
      if (response.success && response.data) {
        setConversations(response.data)
      }
    } catch (error) {
      console.error('Failed to fetch conversations:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchConversations()
  }, [])

  // Mock data for demonstration since the API endpoints aren't implemented yet
  const mockConversations: Conversation[] = [
    {
      id: 1,
      session_id: 'sess_abc123',
      title: 'AI and Machine Learning Discussion',
      user_id: 1,
      message_count: 15,
      last_activity_at: new Date().toISOString(),
      created_at: new Date(Date.now() - 3600000).toISOString()
    },
    {
      id: 2,
      session_id: 'sess_def456',
      title: 'Weather Information Request',
      user_id: 2,
      message_count: 8,
      last_activity_at: new Date(Date.now() - 7200000).toISOString(),
      created_at: new Date(Date.now() - 10800000).toISOString()
    },
    {
      id: 3,
      session_id: 'sess_ghi789',
      title: 'Workflow Automation Help',
      user_id: 1,
      message_count: 23,
      last_activity_at: new Date(Date.now() - 14400000).toISOString(),
      created_at: new Date(Date.now() - 21600000).toISOString()
    }
  ]

  const displayConversations = conversations.length > 0 ? conversations : mockConversations

  return (
    <DashboardLayout>
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Conversations</h1>
            <p className="text-gray-600">
              View and analyze user conversations
            </p>
          </div>
          <Button onClick={fetchConversations} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid gap-6 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Conversations</CardTitle>
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{displayConversations.length}</div>
              <p className="text-xs text-muted-foreground">
                Active sessions
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Messages</CardTitle>
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {displayConversations.reduce((sum, conv) => sum + conv.message_count, 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                Across all conversations
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Users</CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {new Set(displayConversations.map(conv => conv.user_id)).size}
              </div>
              <p className="text-xs text-muted-foreground">
                Unique users
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Conversations List */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Conversations</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="animate-pulse border rounded-lg p-4">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {displayConversations.map((conversation) => (
                  <div
                    key={conversation.id}
                    className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                    onClick={() => setSelectedConversation(conversation.session_id)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900">
                          {truncateText(conversation.title, 60)}
                        </h3>
                        <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                          <div className="flex items-center gap-1">
                            <MessageSquare className="h-3 w-3" />
                            {conversation.message_count} messages
                          </div>
                          <div className="flex items-center gap-1">
                            <User className="h-3 w-3" />
                            User {conversation.user_id}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {formatDate(conversation.last_activity_at)}
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end gap-2">
                        <Badge variant="outline">
                          {conversation.session_id.slice(-8)}
                        </Badge>
                        <span className="text-xs text-gray-400">
                          Created {formatDate(conversation.created_at)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Selected Conversation Details */}
        {selectedConversation && (
          <Card>
            <CardHeader>
              <CardTitle>Conversation Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>Conversation details for session {selectedConversation}</p>
                <p className="text-sm">This feature will be implemented when the backend API is ready.</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
