<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

class AdminController extends Controller
{
    /**
     * Get admin dashboard data
     */
    public function getDashboard(): JsonResponse
    {
        try {
            $tenant = tenant();
            
            $data = [
                'overview' => [
                    'total_users' => User::count(),
                    'active_users' => User::where('is_active', true)->count(),
                    'admin_users' => User::where('role', 'admin')->count(),
                    'tenant_info' => [
                        'id' => $tenant->id ?? 'central',
                        'name' => $tenant->name ?? 'Central Admin',
                        'domain' => $tenant->domain ?? 'localhost',
                        'is_active' => $tenant->is_active ?? true,
                    ]
                ],
                'system_health' => [
                    'api_status' => 'healthy',
                    'database_status' => 'healthy',
                    'overall_health' => 'healthy'
                ],
                'recent_activity' => $this->getRecentActivity(),
                'quick_stats' => [
                    'today_logins' => User::whereDate('last_login_at', today())->count(),
                    'active_sessions' => User::whereNotNull('last_login_at')
                        ->where('last_login_at', '>=', now()->subHours(24))
                        ->count(),
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch dashboard data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all users for admin management
     */
    public function getUsers(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $search = $request->get('search');
            
            $query = User::query();
            
            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                });
            }
            
            $users = $query->orderBy('created_at', 'desc')
                          ->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $users
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch users',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new user
     */
    public function createUser(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users',
                'password' => 'required|string|min:8',
                'role' => 'required|in:admin,user',
                'is_active' => 'boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => $request->role,
                'is_active' => $request->get('is_active', true),
                'tenant_id' => tenant('id'),
                'email_verified_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'data' => $user,
                'message' => 'User created successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to create user',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update user details
     */
    public function updateUser(Request $request, User $user): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'sometimes|string|max:255',
                'email' => 'sometimes|string|email|max:255|unique:users,email,' . $user->id,
                'role' => 'sometimes|in:admin,user',
                'is_active' => 'sometimes|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $user->update($request->only(['name', 'email', 'role', 'is_active']));

            return response()->json([
                'success' => true,
                'data' => $user->fresh(),
                'message' => 'User updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to update user',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a user
     */
    public function deleteUser(User $user): JsonResponse
    {
        try {
            // Prevent admin from deleting themselves
            if ($user->id === Auth::id()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Cannot delete your own account'
                ], 403);
            }

            $user->delete();

            return response()->json([
                'success' => true,
                'message' => 'User deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to delete user',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get recent activity for dashboard
     */
    private function getRecentActivity(): array
    {
        $recentUsers = User::orderBy('created_at', 'desc')
                          ->limit(5)
                          ->get(['id', 'name', 'email', 'created_at', 'last_login_at']);

        $activity = [];
        foreach ($recentUsers as $user) {
            $activity[] = [
                'id' => $user->id,
                'type' => 'user_registration',
                'description' => "New user registered: {$user->name}",
                'timestamp' => $user->created_at->toISOString(),
                'status' => 'completed'
            ];
        }

        return $activity;
    }

    /**
     * Get system settings
     */
    public function getSettings(): JsonResponse
    {
        try {
            $tenant = tenant();
            
            $settings = [
                'tenant' => [
                    'name' => $tenant->name ?? 'Default Tenant',
                    'domain' => $tenant->domain ?? 'localhost',
                    'is_active' => $tenant->is_active ?? true,
                ],
                'system' => [
                    'debug_mode' => config('app.debug'),
                    'environment' => config('app.env'),
                    'timezone' => config('app.timezone'),
                ],
                'security' => [
                    'password_min_length' => 8,
                    'session_lifetime' => config('session.lifetime'),
                    'two_factor_enabled' => false,
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => $settings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch settings',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
