<?php

declare(strict_types=1);

namespace App\Services\Tools;

use App\Contracts\ToolInterface;
use App\Services\Tools\Built\WeatherTool;
use App\Services\Tools\Built\WebSearchTool;
use App\Services\Tools\Built\EmailTool;
use App\Services\Tools\Built\CalendarTool;

class ToolRegistry
{
    protected array $tools = [];
    protected array $builtInTools = [
        'weather' => WeatherTool::class,
        'web_search' => WebSearchTool::class,
        'email' => EmailTool::class,
        'calendar' => CalendarTool::class,
    ];

    public function __construct()
    {
        $this->loadBuiltInTools();
    }

    /**
     * Register a tool
     */
    public function registerTool(string $name, ToolInterface $tool): void
    {
        $this->tools[$name] = $tool;
    }

    /**
     * Get a tool by name
     */
    public function getTool(string $name): ?ToolInterface
    {
        return $this->tools[$name] ?? null;
    }

    /**
     * Get all registered tools
     */
    public function getAllTools(): array
    {
        return $this->tools;
    }

    /**
     * Get available tools
     */
    public function getAvailableTools(): array
    {
        return array_filter($this->tools, function (ToolInterface $tool) {
            return $tool->isAvailable();
        });
    }

    /**
     * Check if tool exists
     */
    public function hasTool(string $name): bool
    {
        return isset($this->tools[$name]);
    }

    /**
     * Remove a tool
     */
    public function removeTool(string $name): void
    {
        unset($this->tools[$name]);
    }

    /**
     * Load built-in tools
     */
    protected function loadBuiltInTools(): void
    {
        foreach ($this->builtInTools as $name => $className) {
            if (class_exists($className)) {
                try {
                    $tool = new $className();
                    if ($tool instanceof ToolInterface) {
                        $this->tools[$name] = $tool;
                    }
                } catch (\Exception $e) {
                    // Skip tools that fail to initialize
                    continue;
                }
            }
        }
    }

    /**
     * Get tool information
     */
    public function getToolInfo(string $name): ?array
    {
        $tool = $this->getTool($name);
        
        if (!$tool) {
            return null;
        }

        return [
            'name' => $tool->getName(),
            'description' => $tool->getDescription(),
            'required_params' => $tool->getRequiredParams(),
            'optional_params' => $tool->getOptionalParams(),
            'is_available' => $tool->isAvailable(),
            'config' => $tool->getConfig()
        ];
    }

    /**
     * Get all tools information
     */
    public function getAllToolsInfo(): array
    {
        $info = [];
        
        foreach ($this->tools as $name => $tool) {
            $info[$name] = $this->getToolInfo($name);
        }
        
        return $info;
    }
}
