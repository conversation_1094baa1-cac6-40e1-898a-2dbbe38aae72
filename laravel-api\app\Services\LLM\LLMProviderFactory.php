<?php

declare(strict_types=1);

namespace App\Services\LLM;

use App\Contracts\LLMProviderInterface;
use App\Services\LLM\Providers\OpenAIProvider;
use App\Services\LLM\Providers\AnthropicProvider;
use App\Services\LLM\Providers\GroqProvider;
use App\Services\LLM\Providers\GoogleProvider;
use App\Services\LLM\Providers\OpenRouterProvider;

class LLMProviderFactory
{
    protected array $providers = [
        'openai' => OpenAIProvider::class,
        'anthropic' => AnthropicProvider::class,
        'groq' => GroqProvider::class,
        'google' => GoogleProvider::class,
        'openrouter' => OpenRouterProvider::class,
    ];

    /**
     * Create an LLM provider instance
     */
    public function create(string $provider, array $config = []): LLMProviderInterface
    {
        if (!isset($this->providers[$provider])) {
            throw new \InvalidArgumentException("Unknown LLM provider: {$provider}");
        }

        $providerClass = $this->providers[$provider];
        
        if (!class_exists($providerClass)) {
            throw new \RuntimeException("Provider class not found: {$providerClass}");
        }

        $instance = new $providerClass($config);

        if (!$instance instanceof LLMProviderInterface) {
            throw new \RuntimeException("Provider must implement LLMProviderInterface");
        }

        return $instance;
    }

    /**
     * Get all available provider names
     */
    public function getAvailableProviders(): array
    {
        return array_keys($this->providers);
    }

    /**
     * Register a new provider
     */
    public function registerProvider(string $name, string $className): void
    {
        $this->providers[$name] = $className;
    }

    /**
     * Create provider with fallback
     */
    public function createWithFallback(array $preferredProviders, array $config = []): LLMProviderInterface
    {
        foreach ($preferredProviders as $provider) {
            try {
                $instance = $this->create($provider, $config);
                if ($instance->isAvailable()) {
                    return $instance;
                }
            } catch (\Exception $e) {
                // Continue to next provider
                continue;
            }
        }

        throw new \RuntimeException('No available LLM providers found');
    }
}
