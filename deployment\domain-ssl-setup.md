# Domain and SSL Configuration Guide

## 🌐 **DOMAIN AND SSL SETUP FOR AXIENT MCP++**

### **1. Domain Architecture**

#### **Primary Domains**
- **Main Application**: `app.axientmcp.com`
- **Admin Dashboard**: `admin.axientmcp.com`
- **API Endpoint**: `api.axientmcp.com`
- **Widget Service**: `widget.axientmcp.com`
- **CDN Assets**: `cdn.axientmcp.com`
- **Documentation**: `docs.axientmcp.com`

#### **Tenant Subdomains**
- **Pattern**: `{tenant}.axientmcp.com`
- **Examples**: 
  - `acme.axientmcp.com`
  - `techcorp.axientmcp.com`
  - `startup.axientmcp.com`

### **2. DNS Configuration**

#### **Route 53 Hosted Zone Setup**
```bash
# Create hosted zone
aws route53 create-hosted-zone \
  --name axientmcp.com \
  --caller-reference $(date +%s) \
  --hosted-zone-config Comment="Axient MCP++ Production Zone"
```

#### **DNS Records Configuration**
```json
{
  "Comment": "Axient MCP++ DNS Records",
  "Changes": [
    {
      "Action": "CREATE",
      "ResourceRecordSet": {
        "Name": "app.axientmcp.com",
        "Type": "A",
        "AliasTarget": {
          "DNSName": "your-alb-dns-name.us-east-1.elb.amazonaws.com",
          "EvaluateTargetHealth": true,
          "HostedZoneId": "Z35SXDOTRQ7X7K"
        }
      }
    },
    {
      "Action": "CREATE",
      "ResourceRecordSet": {
        "Name": "api.axientmcp.com",
        "Type": "A",
        "AliasTarget": {
          "DNSName": "your-api-alb-dns-name.us-east-1.elb.amazonaws.com",
          "EvaluateTargetHealth": true,
          "HostedZoneId": "Z35SXDOTRQ7X7K"
        }
      }
    },
    {
      "Action": "CREATE",
      "ResourceRecordSet": {
        "Name": "*.axientmcp.com",
        "Type": "A",
        "AliasTarget": {
          "DNSName": "your-wildcard-alb-dns-name.us-east-1.elb.amazonaws.com",
          "EvaluateTargetHealth": true,
          "HostedZoneId": "Z35SXDOTRQ7X7K"
        }
      }
    },
    {
      "Action": "CREATE",
      "ResourceRecordSet": {
        "Name": "cdn.axientmcp.com",
        "Type": "A",
        "AliasTarget": {
          "DNSName": "your-cloudfront-distribution.cloudfront.net",
          "EvaluateTargetHealth": false,
          "HostedZoneId": "Z2FDTNDATAQYW2"
        }
      }
    }
  ]
}
```

### **3. SSL Certificate Setup**

#### **AWS Certificate Manager (ACM)**
```bash
# Request wildcard certificate
aws acm request-certificate \
  --domain-name "*.axientmcp.com" \
  --subject-alternative-names "axientmcp.com" \
  --validation-method DNS \
  --region us-east-1

# Request additional certificate for CloudFront (us-east-1 required)
aws acm request-certificate \
  --domain-name "cdn.axientmcp.com" \
  --validation-method DNS \
  --region us-east-1
```

#### **Certificate Validation**
```bash
# Get certificate details
aws acm describe-certificate \
  --certificate-arn arn:aws:acm:us-east-1:ACCOUNT:certificate/CERT-ID

# Add DNS validation records to Route 53
aws route53 change-resource-record-sets \
  --hosted-zone-id Z1234567890ABC \
  --change-batch file://cert-validation-records.json
```

### **4. Load Balancer Configuration**

#### **Application Load Balancer Setup**
```yaml
# ALB Configuration (CloudFormation/Terraform)
Resources:
  ApplicationLoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Name: axient-mcp-alb
      Scheme: internet-facing
      Type: application
      SecurityGroups:
        - !Ref ALBSecurityGroup
      Subnets:
        - !Ref PublicSubnet1
        - !Ref PublicSubnet2
      Tags:
        - Key: Name
          Value: Axient MCP++ ALB

  HTTPSListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      DefaultActions:
        - Type: forward
          TargetGroupArn: !Ref DefaultTargetGroup
      LoadBalancerArn: !Ref ApplicationLoadBalancer
      Port: 443
      Protocol: HTTPS
      Certificates:
        - CertificateArn: !Ref SSLCertificate
      SslPolicy: ELBSecurityPolicy-TLS-1-2-2017-01

  HTTPListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      DefaultActions:
        - Type: redirect
          RedirectConfig:
            Protocol: HTTPS
            Port: 443
            StatusCode: HTTP_301
      LoadBalancerArn: !Ref ApplicationLoadBalancer
      Port: 80
      Protocol: HTTP
```

#### **Target Groups Configuration**
```yaml
# Frontend Target Group
FrontendTargetGroup:
  Type: AWS::ElasticLoadBalancingV2::TargetGroup
  Properties:
    Name: axient-mcp-frontend
    Port: 3000
    Protocol: HTTP
    VpcId: !Ref VPC
    HealthCheckPath: /health
    HealthCheckProtocol: HTTP
    HealthCheckIntervalSeconds: 30
    HealthyThresholdCount: 2
    UnhealthyThresholdCount: 3

# API Target Group
APITargetGroup:
  Type: AWS::ElasticLoadBalancingV2::TargetGroup
  Properties:
    Name: axient-mcp-api
    Port: 80
    Protocol: HTTP
    VpcId: !Ref VPC
    HealthCheckPath: /api/health
    HealthCheckProtocol: HTTP
    HealthCheckIntervalSeconds: 30
    HealthyThresholdCount: 2
    UnhealthyThresholdCount: 3
```

### **5. CloudFront CDN Setup**

#### **CloudFront Distribution**
```json
{
  "DistributionConfig": {
    "CallerReference": "axient-mcp-cdn-2024",
    "Comment": "Axient MCP++ CDN Distribution",
    "DefaultCacheBehavior": {
      "TargetOriginId": "S3-axient-mcp-assets",
      "ViewerProtocolPolicy": "redirect-to-https",
      "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad",
      "Compress": true
    },
    "Origins": [
      {
        "Id": "S3-axient-mcp-assets",
        "DomainName": "axient-mcp-assets.s3.amazonaws.com",
        "S3OriginConfig": {
          "OriginAccessIdentity": "origin-access-identity/cloudfront/E1234567890ABC"
        }
      }
    ],
    "Aliases": ["cdn.axientmcp.com"],
    "ViewerCertificate": {
      "AcmCertificateArn": "arn:aws:acm:us-east-1:ACCOUNT:certificate/CERT-ID",
      "SslSupportMethod": "sni-only",
      "MinimumProtocolVersion": "TLSv1.2_2021"
    },
    "Enabled": true,
    "PriceClass": "PriceClass_100"
  }
}
```

### **6. Security Headers Configuration**

#### **Nginx Security Headers**
```nginx
# /etc/nginx/conf.d/security-headers.conf
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;

# HSTS Header
add_header Strict-Transport-Security "max-age=********; includeSubDomains; preload" always;

# Content Security Policy
add_header Content-Security-Policy "
  default-src 'self';
  script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com https://cdn.axientmcp.com;
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.axientmcp.com;
  font-src 'self' https://fonts.gstatic.com https://cdn.axientmcp.com;
  img-src 'self' data: https: blob:;
  connect-src 'self' https://api.axientmcp.com https://api.stripe.com;
  frame-src 'self' https://js.stripe.com;
  object-src 'none';
  base-uri 'self';
  form-action 'self';
" always;
```

### **7. Domain Verification Scripts**

#### **SSL Certificate Verification**
```bash
#!/bin/bash
# ssl-verify.sh

DOMAINS=(
  "app.axientmcp.com"
  "admin.axientmcp.com"
  "api.axientmcp.com"
  "widget.axientmcp.com"
  "cdn.axientmcp.com"
)

echo "Verifying SSL certificates..."

for domain in "${DOMAINS[@]}"; do
  echo "Checking $domain..."
  
  # Check SSL certificate
  ssl_info=$(echo | openssl s_client -servername $domain -connect $domain:443 2>/dev/null | openssl x509 -noout -dates)
  
  if [ $? -eq 0 ]; then
    echo "✓ $domain - SSL certificate valid"
    echo "  $ssl_info"
  else
    echo "✗ $domain - SSL certificate invalid or unreachable"
  fi
  
  echo ""
done
```

#### **DNS Propagation Check**
```bash
#!/bin/bash
# dns-check.sh

DOMAINS=(
  "app.axientmcp.com"
  "admin.axientmcp.com"
  "api.axientmcp.com"
  "widget.axientmcp.com"
)

DNS_SERVERS=(
  "*******"
  "*******"
  "**************"
)

echo "Checking DNS propagation..."

for domain in "${DOMAINS[@]}"; do
  echo "Checking $domain..."
  
  for dns in "${DNS_SERVERS[@]}"; do
    result=$(dig @$dns $domain A +short)
    if [ -n "$result" ]; then
      echo "  $dns: $result"
    else
      echo "  $dns: No response"
    fi
  done
  
  echo ""
done
```

### **8. Health Check Endpoints**

#### **Laravel Health Check Route**
```php
// routes/web.php
Route::get('/health', function () {
    return response()->json([
        'status' => 'healthy',
        'timestamp' => now()->toISOString(),
        'version' => config('app.version'),
        'environment' => config('app.env')
    ]);
});

Route::get('/api/health', function () {
    // Check database connection
    try {
        DB::connection()->getPdo();
        $db_status = 'connected';
    } catch (Exception $e) {
        $db_status = 'disconnected';
    }
    
    // Check Redis connection
    try {
        Redis::ping();
        $redis_status = 'connected';
    } catch (Exception $e) {
        $redis_status = 'disconnected';
    }
    
    return response()->json([
        'status' => 'healthy',
        'services' => [
            'database' => $db_status,
            'redis' => $redis_status,
            'storage' => Storage::disk('s3')->exists('health-check.txt') ? 'connected' : 'disconnected'
        ],
        'timestamp' => now()->toISOString()
    ]);
});
```

#### **Next.js Health Check**
```javascript
// pages/api/health.js
export default function handler(req, res) {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.NEXT_PUBLIC_APP_VERSION,
    environment: process.env.NODE_ENV
  });
}
```

### **9. Monitoring and Alerts**

#### **CloudWatch Alarms**
```bash
# Create SSL certificate expiration alarm
aws cloudwatch put-metric-alarm \
  --alarm-name "SSL-Certificate-Expiration" \
  --alarm-description "SSL certificate expiring soon" \
  --metric-name DaysToExpiry \
  --namespace AWS/CertificateManager \
  --statistic Minimum \
  --period 86400 \
  --threshold 30 \
  --comparison-operator LessThanThreshold \
  --evaluation-periods 1

# Create domain health check
aws route53 create-health-check \
  --caller-reference $(date +%s) \
  --health-check-config Type=HTTPS,ResourcePath=/health,FullyQualifiedDomainName=app.axientmcp.com,Port=443,RequestInterval=30,FailureThreshold=3
```

### **10. Deployment Checklist**

#### **Pre-Deployment**
- [ ] Domain purchased and configured
- [ ] DNS records created in Route 53
- [ ] SSL certificates requested and validated
- [ ] Load balancer configured
- [ ] CloudFront distribution created
- [ ] Security headers configured
- [ ] Health check endpoints implemented

#### **Post-Deployment**
- [ ] SSL certificates verified
- [ ] DNS propagation confirmed
- [ ] Health checks passing
- [ ] Security headers tested
- [ ] Performance monitoring enabled
- [ ] Backup domain configured (optional)

#### **Testing Commands**
```bash
# Test all domains
./ssl-verify.sh
./dns-check.sh

# Test health endpoints
curl -s https://app.axientmcp.com/health | jq
curl -s https://api.axientmcp.com/api/health | jq

# Test security headers
curl -I https://app.axientmcp.com
```

---

## 🔒 **SECURITY CONSIDERATIONS**

1. **Use HTTPS everywhere** - No HTTP traffic allowed
2. **Implement HSTS** - Force HTTPS for all subdomains
3. **Configure CSP** - Prevent XSS attacks
4. **Enable DNSSEC** - Protect against DNS spoofing
5. **Monitor certificate expiration** - Set up alerts
6. **Use strong cipher suites** - TLS 1.2+ only
7. **Implement rate limiting** - Protect against DDoS
8. **Regular security audits** - Quarterly reviews
