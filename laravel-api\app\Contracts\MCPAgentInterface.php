<?php

declare(strict_types=1);

namespace App\Contracts;

interface MCPAgentInterface
{
    /**
     * Get the agent name
     */
    public function getName(): string;

    /**
     * Get the agent type (primary, secondary)
     */
    public function getType(): string;

    /**
     * Execute the agent with given input
     */
    public function execute(array $input, string $sessionId): array;

    /**
     * Validate input data
     */
    public function validateInput(array $input): bool;

    /**
     * Get agent configuration
     */
    public function getConfig(): array;

    /**
     * Set agent configuration
     */
    public function setConfig(array $config): void;

    /**
     * Check if agent is active
     */
    public function isActive(): bool;

    /**
     * Get execution order priority
     */
    public function getExecutionOrder(): int;
}
