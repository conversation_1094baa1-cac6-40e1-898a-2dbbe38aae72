<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\AIProvider;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;

class ProviderController extends Controller
{
    /**
     * Get all AI providers
     */
    public function index(): JsonResponse
    {
        try {
            $providers = AIProvider::orderBy('priority', 'asc')
                ->orderBy('name', 'asc')
                ->get()
                ->map(function ($provider) {
                    return [
                        'id' => $provider->id,
                        'name' => $provider->name,
                        'display_name' => $provider->display_name,
                        'description' => $provider->description,
                        'is_enabled' => $provider->is_enabled,
                        'is_primary' => $provider->is_primary,
                        'priority' => $provider->priority,
                        'capabilities' => $provider->capabilities,
                        'limits' => $provider->limits,
                        'status' => $provider->status,
                        'status_message' => $provider->status_message,
                        'last_tested_at' => $provider->last_tested_at,
                        'usage_stats' => $provider->getUsageStats(7),
                        'is_configured' => !empty($provider->configuration['api_key']),
                        'created_at' => $provider->created_at,
                        'updated_at' => $provider->updated_at
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $providers
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific provider
     */
    public function show(AIProvider $provider): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $provider->id,
                    'name' => $provider->name,
                    'display_name' => $provider->display_name,
                    'description' => $provider->description,
                    'is_enabled' => $provider->is_enabled,
                    'is_primary' => $provider->is_primary,
                    'priority' => $provider->priority,
                    'configuration' => $this->sanitizeConfiguration($provider->configuration),
                    'capabilities' => $provider->capabilities,
                    'limits' => $provider->limits,
                    'status' => $provider->status,
                    'status_message' => $provider->status_message,
                    'last_tested_at' => $provider->last_tested_at,
                    'usage_stats' => $provider->getUsageStats(30),
                    'created_at' => $provider->created_at,
                    'updated_at' => $provider->updated_at
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create or update a provider
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|in:openai,anthropic,groq,google,openrouter',
                'is_enabled' => 'boolean',
                'is_primary' => 'boolean',
                'priority' => 'integer|min:1|max:100',
                'configuration' => 'required|array',
                'configuration.api_key' => 'required|string|min:10',
                'configuration.base_url' => 'sometimes|url',
                'configuration.default_model' => 'sometimes|string',
                'configuration.max_tokens' => 'sometimes|integer|min:1|max:32000',
                'configuration.temperature' => 'sometimes|numeric|min:0|max:2',
                'limits' => 'sometimes|array',
                'limits.requests_per_minute' => 'sometimes|integer|min:1',
                'limits.tokens_per_minute' => 'sometimes|integer|min:1',
                'limits.requests_per_day' => 'sometimes|integer|min:1'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            // Get default provider data
            $defaults = AIProvider::getDefaultProviders()[$request->name] ?? [];
            
            // Find existing provider or create new one
            $provider = AIProvider::where('name', $request->name)->first();
            
            if (!$provider) {
                $provider = new AIProvider();
                $provider->name = $request->name;
                $provider->display_name = $defaults['display_name'] ?? ucfirst($request->name);
                $provider->description = $defaults['description'] ?? '';
                $provider->capabilities = $defaults['capabilities'] ?? [];
            }

            // Update provider data
            $provider->is_enabled = $request->boolean('is_enabled', false);
            $provider->is_primary = $request->boolean('is_primary', false);
            $provider->priority = $request->integer('priority', 1);
            
            // Merge configuration with defaults
            $configuration = array_merge($defaults['configuration'] ?? [], $request->configuration);
            $provider->configuration = $configuration;
            
            // Update limits if provided
            if ($request->has('limits')) {
                $provider->limits = array_merge($defaults['limits'] ?? [], $request->limits);
            } else {
                $provider->limits = $defaults['limits'] ?? [];
            }

            // If setting as primary, unset other primary providers
            if ($provider->is_primary) {
                AIProvider::where('is_primary', true)
                    ->where('id', '!=', $provider->id ?? 0)
                    ->update(['is_primary' => false]);
            }

            $provider->save();

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $provider->id,
                    'name' => $provider->name,
                    'display_name' => $provider->display_name,
                    'is_enabled' => $provider->is_enabled,
                    'is_primary' => $provider->is_primary,
                    'status' => $provider->status
                ],
                'message' => 'Provider configuration saved successfully'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to save provider: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a provider
     */
    public function update(Request $request, AIProvider $provider): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'is_enabled' => 'sometimes|boolean',
                'is_primary' => 'sometimes|boolean',
                'priority' => 'sometimes|integer|min:1|max:100',
                'configuration' => 'sometimes|array',
                'configuration.api_key' => 'sometimes|string|min:10',
                'configuration.base_url' => 'sometimes|url',
                'configuration.default_model' => 'sometimes|string',
                'configuration.max_tokens' => 'sometimes|integer|min:1|max:32000',
                'configuration.temperature' => 'sometimes|numeric|min:0|max:2',
                'limits' => 'sometimes|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            // Update fields
            if ($request->has('is_enabled')) {
                $provider->is_enabled = $request->boolean('is_enabled');
            }
            
            if ($request->has('is_primary')) {
                $provider->is_primary = $request->boolean('is_primary');
                
                // If setting as primary, unset other primary providers
                if ($provider->is_primary) {
                    AIProvider::where('is_primary', true)
                        ->where('id', '!=', $provider->id)
                        ->update(['is_primary' => false]);
                }
            }
            
            if ($request->has('priority')) {
                $provider->priority = $request->integer('priority');
            }
            
            if ($request->has('configuration')) {
                $configuration = array_merge($provider->configuration ?? [], $request->configuration);
                $provider->configuration = $configuration;
            }
            
            if ($request->has('limits')) {
                $limits = array_merge($provider->limits ?? [], $request->limits);
                $provider->limits = $limits;
            }

            $provider->save();

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $provider->id,
                    'name' => $provider->name,
                    'display_name' => $provider->display_name,
                    'is_enabled' => $provider->is_enabled,
                    'is_primary' => $provider->is_primary,
                    'status' => $provider->status
                ],
                'message' => 'Provider updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to update provider: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test provider connection
     */
    public function test(AIProvider $provider): JsonResponse
    {
        try {
            $result = $provider->testConnection();

            return response()->json([
                'success' => true,
                'data' => [
                    'test_result' => $result,
                    'status' => $provider->fresh()->status,
                    'status_message' => $provider->fresh()->status_message,
                    'last_tested_at' => $provider->fresh()->last_tested_at
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get provider usage statistics
     */
    public function usage(AIProvider $provider, Request $request): JsonResponse
    {
        try {
            $days = $request->integer('days', 7);
            $days = min(max($days, 1), 30); // Limit between 1 and 30 days
            
            $stats = $provider->getUsageStats($days);
            
            // Calculate totals
            $totals = [
                'requests' => array_sum(array_column($stats, 'requests')),
                'tokens' => array_sum(array_column($stats, 'tokens')),
                'errors' => array_sum(array_column($stats, 'errors')),
                'avg_response_time' => 0
            ];
            
            $responseTimes = array_filter(array_column($stats, 'avg_response_time'));
            if (!empty($responseTimes)) {
                $totals['avg_response_time'] = array_sum($responseTimes) / count($responseTimes);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'daily_stats' => $stats,
                    'totals' => $totals,
                    'limits' => $provider->limits,
                    'is_within_limits' => $provider->isWithinLimits()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Initialize default providers
     */
    public function initializeDefaults(): JsonResponse
    {
        try {
            $defaults = AIProvider::getDefaultProviders();
            $created = 0;
            
            foreach ($defaults as $name => $config) {
                $existing = AIProvider::where('name', $name)->first();
                
                if (!$existing) {
                    AIProvider::create([
                        'name' => $name,
                        'display_name' => $config['display_name'],
                        'description' => $config['description'],
                        'capabilities' => $config['capabilities'],
                        'configuration' => $config['configuration'],
                        'limits' => $config['limits'],
                        'is_enabled' => false,
                        'priority' => $created + 1
                    ]);
                    $created++;
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Initialized {$created} default providers",
                'data' => ['created_count' => $created]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to initialize providers: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a provider
     */
    public function destroy(AIProvider $provider): JsonResponse
    {
        try {
            $provider->delete();

            return response()->json([
                'success' => true,
                'message' => 'Provider deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to delete provider: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Sanitize configuration for API response (hide sensitive data)
     */
    protected function sanitizeConfiguration(array $configuration): array
    {
        $sanitized = $configuration;
        
        // Mask API key
        if (isset($sanitized['api_key']) && $sanitized['api_key']) {
            $key = $sanitized['api_key'];
            $sanitized['api_key'] = substr($key, 0, 8) . str_repeat('*', max(0, strlen($key) - 12)) . substr($key, -4);
        }
        
        return $sanitized;
    }
}
