<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_providers', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // openai, anthropic, groq, google, openrouter
            $table->string('display_name');
            $table->json('config'); // API keys, endpoints, etc.
            $table->boolean('is_active')->default(false);
            $table->integer('priority')->default(0); // For fallback ordering
            $table->json('models')->nullable(); // Available models for this provider
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_providers');
    }
};
