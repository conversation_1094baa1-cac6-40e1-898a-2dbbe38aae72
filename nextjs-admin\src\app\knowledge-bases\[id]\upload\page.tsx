'use client'

import { useState, useEffect, useCallback } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useApi } from '@/contexts/AuthContext'
import { 
  ArrowLeft,
  Upload,
  FileText,
  X,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Download,
  Trash2
} from 'lucide-react'
import Link from 'next/link'

interface KnowledgeBase {
  id: number
  name: string
  description: string
  settings: {
    allowed_file_types: string[]
    max_file_size: number
  }
}

interface Document {
  id: number
  title: string
  file_name: string
  file_type: string
  file_size: number
  status: 'pending' | 'processing' | 'completed' | 'failed'
  processing_error?: string
  chunk_count: number
  created_at: string
}

export default function DocumentUploadPage() {
  const params = useParams()
  const router = useRouter()
  const { apiCall } = useApi()
  const [knowledgeBase, setKnowledgeBase] = useState<KnowledgeBase | null>(null)
  const [documents, setDocuments] = useState<Document[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isUploading, setIsUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({})

  const fetchKnowledgeBase = async () => {
    try {
      const response = await apiCall(`/knowledge-bases/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setKnowledgeBase(data.data)
        }
      }
    } catch (error) {
      console.error('Failed to fetch knowledge base:', error)
    }
  }

  const fetchDocuments = async () => {
    try {
      const response = await apiCall(`/knowledge-bases/${params.id}/documents`)
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setDocuments(data.data)
        }
      }
    } catch (error) {
      console.error('Failed to fetch documents:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchKnowledgeBase()
    fetchDocuments()
  }, [params.id])

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(Array.from(e.dataTransfer.files))
    }
  }, [])

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(Array.from(e.target.files))
    }
  }

  const handleFiles = async (files: File[]) => {
    if (!knowledgeBase) return

    setIsUploading(true)
    const allowedTypes = knowledgeBase.settings.allowed_file_types || []
    const maxSize = knowledgeBase.settings.max_file_size || 10485760

    for (const file of files) {
      const fileExtension = file.name.split('.').pop()?.toLowerCase()
      
      // Validate file type
      if (!allowedTypes.includes(fileExtension || '')) {
        alert(`File type .${fileExtension} is not allowed. Allowed types: ${allowedTypes.join(', ')}`)
        continue
      }

      // Validate file size
      if (file.size > maxSize) {
        alert(`File ${file.name} is too large. Maximum size: ${formatBytes(maxSize)}`)
        continue
      }

      try {
        await uploadFile(file)
      } catch (error) {
        console.error(`Failed to upload ${file.name}:`, error)
        alert(`Failed to upload ${file.name}`)
      }
    }

    setIsUploading(false)
    fetchDocuments() // Refresh document list
  }

  const uploadFile = async (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('title', file.name.split('.')[0]) // Use filename without extension as title

    const response = await apiCall(`/knowledge-bases/${params.id}/upload`, {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      const data = await response.json()
      throw new Error(data.error || 'Upload failed')
    }

    return response.json()
  }

  const deleteDocument = async (documentId: number) => {
    if (!confirm('Are you sure you want to delete this document?')) return

    try {
      const response = await apiCall(`/documents/${documentId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setDocuments(prev => prev.filter(doc => doc.id !== documentId))
      }
    } catch (error) {
      console.error('Failed to delete document:', error)
      alert('Failed to delete document')
    }
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed': return <AlertCircle className="h-4 w-4 text-red-500" />
      case 'processing': return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />
      default: return <RefreshCw className="h-4 w-4 text-yellow-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success'
      case 'failed': return 'destructive'
      case 'processing': return 'default'
      default: return 'secondary'
    }
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="space-y-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-64 bg-gray-200 rounded mb-8"></div>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/knowledge-bases">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Knowledge Bases
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Upload Documents
              </h1>
              <p className="text-gray-600">
                {knowledgeBase?.name} - Add documents to your knowledge base
              </p>
            </div>
          </div>
          <Button variant="outline" onClick={fetchDocuments}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>

        {/* Upload Area */}
        <Card>
          <CardHeader>
            <CardTitle>Upload Documents</CardTitle>
          </CardHeader>
          <CardContent>
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragActive 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium mb-2">
                Drop files here or click to browse
              </h3>
              <p className="text-gray-500 mb-4">
                {knowledgeBase && (
                  <>
                    Supported formats: {knowledgeBase.settings.allowed_file_types?.join(', ') || 'PDF, TXT, DOCX'}<br />
                    Maximum file size: {formatBytes(knowledgeBase.settings.max_file_size || 10485760)}
                  </>
                )}
              </p>
              <input
                type="file"
                multiple
                onChange={handleFileInput}
                className="hidden"
                id="file-upload"
                accept={knowledgeBase?.settings.allowed_file_types?.map(type => `.${type}`).join(',') || '.pdf,.txt,.docx'}
              />
              <label htmlFor="file-upload">
                <Button asChild disabled={isUploading}>
                  <span>
                    {isUploading ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Uploading...
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4 mr-2" />
                        Select Files
                      </>
                    )}
                  </span>
                </Button>
              </label>
            </div>
          </CardContent>
        </Card>

        {/* Documents List */}
        <Card>
          <CardHeader>
            <CardTitle>Documents ({documents.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {documents.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No documents uploaded yet</p>
                <p className="text-sm">Upload your first document to get started</p>
              </div>
            ) : (
              <div className="space-y-4">
                {documents.map((doc) => (
                  <div key={doc.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <FileText className="h-8 w-8 text-gray-400" />
                      <div>
                        <h4 className="font-medium">{doc.title}</h4>
                        <div className="text-sm text-gray-500 space-x-2">
                          <span>{doc.file_name}</span>
                          <span>•</span>
                          <span>{formatBytes(doc.file_size)}</span>
                          <span>•</span>
                          <span>{doc.chunk_count} chunks</span>
                          <span>•</span>
                          <span>{new Date(doc.created_at).toLocaleDateString()}</span>
                        </div>
                        {doc.processing_error && (
                          <div className="text-sm text-red-600 mt-1">
                            Error: {doc.processing_error}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(doc.status)}
                      <Badge variant={getStatusColor(doc.status)}>
                        {doc.status}
                      </Badge>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => deleteDocument(doc.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
