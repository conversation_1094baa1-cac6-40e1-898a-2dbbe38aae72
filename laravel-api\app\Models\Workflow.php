<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Workflow extends Model
{
    protected $fillable = [
        'name',
        'description',
        'status',
        'trigger_type',
        'trigger_config',
        'nodes',
        'variables',
        'settings',
        'execution_count',
        'last_executed_at'
    ];

    protected $casts = [
        'trigger_config' => 'array',
        'nodes' => 'array',
        'variables' => 'array',
        'settings' => 'array',
        'execution_count' => 'integer',
        'last_executed_at' => 'datetime'
    ];

    /**
     * Get all executions for this workflow
     */
    public function executions(): HasMany
    {
        return $this->hasMany(WorkflowExecution::class);
    }

    /**
     * Scope for active workflows
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get step count
     */
    public function getStepCountAttribute(): int
    {
        return count($this->steps ?? []);
    }

    /**
     * Get execution count
     */
    public function getExecutionCountAttribute(): int
    {
        return $this->executions()->count();
    }

    /**
     * Get successful execution count
     */
    public function getSuccessfulExecutionsAttribute(): int
    {
        return $this->executions()->where('status', 'completed')->count();
    }

    /**
     * Get failed execution count
     */
    public function getFailedExecutionsAttribute(): int
    {
        return $this->executions()->where('status', 'failed')->count();
    }

    /**
     * Get success rate
     */
    public function getSuccessRateAttribute(): float
    {
        $total = $this->execution_count;
        if ($total === 0) {
            return 0.0;
        }

        return ($this->successful_executions / $total) * 100;
    }
}
