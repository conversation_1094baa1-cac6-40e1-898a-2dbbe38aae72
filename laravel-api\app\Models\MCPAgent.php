<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MCPAgent extends Model
{
    protected $table = 'mcp_agents';

    protected $fillable = [
        'name',
        'type',
        'description',
        'config',
        'is_active',
        'execution_order',
    ];

    protected $casts = [
        'config' => 'array',
        'is_active' => 'boolean',
        'execution_order' => 'integer',
    ];

    /**
     * Get all executions for this agent
     */
    public function executions(): HasMany
    {
        return $this->hasMany(AgentExecution::class, 'agent_id');
    }

    /**
     * Get communications sent from this agent
     */
    public function sentCommunications(): HasMany
    {
        return $this->hasMany(AgentCommunication::class, 'from_agent_id');
    }

    /**
     * Get communications received by this agent
     */
    public function receivedCommunications(): HasMany
    {
        return $this->hasMany(AgentCommunication::class, 'to_agent_id');
    }

    /**
     * Scope for active agents
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for primary agents
     */
    public function scopePrimary($query)
    {
        return $query->where('type', 'primary');
    }

    /**
     * Scope for secondary agents
     */
    public function scopeSecondary($query)
    {
        return $query->where('type', 'secondary');
    }

    /**
     * Scope ordered by execution order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('execution_order');
    }
}
