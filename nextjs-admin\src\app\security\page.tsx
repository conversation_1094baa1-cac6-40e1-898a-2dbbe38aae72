'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { useApi } from '@/contexts/AuthContext'
import { 
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  EyeOff,
  Key,
  Lock,
  Scan,
  RefreshCw,
  Download,
  Activity,
  Clock,
  Users,
  Globe
} from 'lucide-react'

interface SecurityEvent {
  id: number
  event_name: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  ip_address: string
  occurred_at: string
}

interface SecurityMetrics {
  total_events_30d: number
  high_severity_events_30d: number
  threat_level: 'low' | 'medium' | 'high' | 'critical'
  threats_by_type: Array<{ event_name: string; count: number }>
}

interface SecurityDashboard {
  recent_events: SecurityEvent[]
  metrics: SecurityMetrics
}

export default function SecurityPage() {
  const { apiCall } = useApi()
  const [securityData, setSecurityData] = useState<SecurityDashboard | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [showPasswordTester, setShowPasswordTester] = useState(false)
  const [showContentScanner, setShowContentScanner] = useState(false)

  const fetchSecurityData = async () => {
    setIsLoading(true)
    try {
      const response = await apiCall('/security/dashboard')
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setSecurityData(data.data)
        }
      }
    } catch (error) {
      console.error('Failed to fetch security data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchSecurityData()
    
    // Set up periodic refresh
    const interval = setInterval(fetchSecurityData, 60000) // Every minute
    
    return () => clearInterval(interval)
  }, [])

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <XCircle className="h-4 w-4 text-red-600" />
      case 'high': return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'medium': return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      default: return <CheckCircle className="h-4 w-4 text-blue-500" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'destructive'
      case 'high': return 'destructive'
      case 'medium': return 'warning'
      default: return 'secondary'
    }
  }

  const getThreatLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return 'text-red-600 bg-red-50'
      case 'high': return 'text-red-500 bg-red-50'
      case 'medium': return 'text-yellow-600 bg-yellow-50'
      default: return 'text-green-600 bg-green-50'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="space-y-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Security Dashboard</h1>
            <p className="text-gray-600">
              Monitor security events and system protection status
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={fetchSecurityData}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>

        {/* Security Overview */}
        {securityData && (
          <>
            <div className="grid gap-6 md:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Threat Level</CardTitle>
                  <Shield className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className={`text-2xl font-bold px-3 py-1 rounded-full ${getThreatLevelColor(securityData.metrics.threat_level)}`}>
                    {securityData.metrics.threat_level.toUpperCase()}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Security Events (30d)</CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{securityData.metrics.total_events_30d}</div>
                  <p className="text-xs text-muted-foreground">
                    {securityData.metrics.high_severity_events_30d} high severity
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Recent Events</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{securityData.recent_events.length}</div>
                  <p className="text-xs text-muted-foreground">
                    Last 24 hours
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">System Status</CardTitle>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">SECURE</div>
                  <p className="text-xs text-muted-foreground">
                    All systems operational
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Security Tools */}
            <div className="grid gap-6 md:grid-cols-3">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Lock className="h-5 w-5" />
                    Password Strength Tester
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Test password strength and get security recommendations
                  </p>
                  <Button 
                    onClick={() => setShowPasswordTester(true)}
                    className="w-full"
                  >
                    Test Password
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Scan className="h-5 w-5" />
                    Content Scanner
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Scan content for malicious patterns and threats
                  </p>
                  <Button 
                    onClick={() => setShowContentScanner(true)}
                    className="w-full"
                  >
                    Scan Content
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Key className="h-5 w-5" />
                    API Key Generator
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Generate secure API keys for system integration
                  </p>
                  <Button className="w-full">
                    Generate Key
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Recent Security Events */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Security Events</CardTitle>
              </CardHeader>
              <CardContent>
                {securityData.recent_events.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Shield className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>No recent security events</p>
                    <p className="text-sm">Your system is secure</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {securityData.recent_events.slice(0, 10).map((event) => (
                      <div key={event.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-4">
                          {getSeverityIcon(event.severity)}
                          <div>
                            <h4 className="font-medium">{event.event_name.replace('_', ' ')}</h4>
                            <p className="text-sm text-gray-600">{event.description}</p>
                            <div className="text-xs text-gray-500 mt-1">
                              IP: {event.ip_address} • {formatDate(event.occurred_at)}
                            </div>
                          </div>
                        </div>
                        <Badge variant={getSeverityColor(event.severity)}>
                          {event.severity}
                        </Badge>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Threat Types */}
            <Card>
              <CardHeader>
                <CardTitle>Threat Types (Last 30 Days)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {securityData.metrics.threats_by_type.map((threat, index) => (
                    <div key={threat.event_name} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-red-100 text-red-600 text-xs flex items-center justify-center">
                          {index + 1}
                        </div>
                        <span className="text-sm">{threat.event_name.replace('_', ' ')}</span>
                      </div>
                      <span className="text-sm font-medium">{threat.count}</span>
                    </div>
                  ))}
                  {securityData.metrics.threats_by_type.length === 0 && (
                    <div className="text-center py-4 text-gray-500">
                      <p>No threats detected</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </>
        )}

        {/* Password Tester Modal */}
        {showPasswordTester && (
          <PasswordTesterModal onClose={() => setShowPasswordTester(false)} />
        )}

        {/* Content Scanner Modal */}
        {showContentScanner && (
          <ContentScannerModal onClose={() => setShowContentScanner(false)} />
        )}
      </div>
    </DashboardLayout>
  )
}

// Password Tester Modal Component
function PasswordTesterModal({ onClose }: { onClose: () => void }) {
  const { apiCall } = useApi()
  const [password, setPassword] = useState('')
  const [result, setResult] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  const testPassword = async () => {
    if (!password) return

    setIsLoading(true)
    try {
      const response = await apiCall('/security/validate-password', {
        method: 'POST',
        body: JSON.stringify({ password })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setResult(data.data)
        }
      }
    } catch (error) {
      console.error('Failed to test password:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStrengthColor = (strength: string) => {
    switch (strength) {
      case 'very_strong': return 'text-green-600'
      case 'strong': return 'text-green-500'
      case 'medium': return 'text-yellow-500'
      case 'weak': return 'text-red-500'
      default: return 'text-red-600'
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 className="text-lg font-semibold mb-4">Password Strength Tester</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Enter Password
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full pr-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter password to test"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
          </div>

          {result && (
            <div className="space-y-3">
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm font-medium">Strength:</span>
                  <span className={`text-sm font-bold ${getStrengthColor(result.strength)}`}>
                    {result.strength.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full transition-all duration-300 ${
                      result.score >= 6 ? 'bg-green-500' :
                      result.score >= 4 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${Math.min((result.score / 6) * 100, 100)}%` }}
                  ></div>
                </div>
              </div>

              {result.feedback.length > 0 && (
                <div>
                  <span className="text-sm font-medium">Recommendations:</span>
                  <ul className="text-sm text-gray-600 mt-1 space-y-1">
                    {result.feedback.map((feedback: string, index: number) => (
                      <li key={index} className="flex items-start gap-1">
                        <span>•</span>
                        <span>{feedback}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button onClick={testPassword} disabled={isLoading || !password}>
              {isLoading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Testing...
                </>
              ) : (
                'Test Password'
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

// Content Scanner Modal Component
function ContentScannerModal({ onClose }: { onClose: () => void }) {
  const { apiCall } = useApi()
  const [content, setContent] = useState('')
  const [contentType, setContentType] = useState('text')
  const [result, setResult] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  const scanContent = async () => {
    if (!content) return

    setIsLoading(true)
    try {
      const response = await apiCall('/security/scan-content', {
        method: 'POST',
        body: JSON.stringify({ content, type: contentType })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setResult(data.data)
        }
      }
    } catch (error) {
      console.error('Failed to scan content:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high': return 'text-red-600'
      case 'medium': return 'text-yellow-600'
      default: return 'text-green-600'
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <h3 className="text-lg font-semibold mb-4">Content Security Scanner</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Content Type
            </label>
            <select
              value={contentType}
              onChange={(e) => setContentType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="text">Text</option>
              <option value="html">HTML</option>
              <option value="sql">SQL</option>
              <option value="javascript">JavaScript</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Content to Scan
            </label>
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter content to scan for security threats"
              rows={6}
            />
          </div>

          {result && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Scan Result:</span>
                <Badge variant={result.is_malicious ? 'destructive' : 'success'}>
                  {result.is_malicious ? 'Threats Detected' : 'Clean'}
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Risk Level:</span>
                <span className={`text-sm font-bold ${getRiskColor(result.risk_level)}`}>
                  {result.risk_level.toUpperCase()}
                </span>
              </div>

              {result.threats.length > 0 && (
                <div>
                  <span className="text-sm font-medium">Detected Threats:</span>
                  <ul className="text-sm text-red-600 mt-1 space-y-1">
                    {result.threats.map((threat: string, index: number) => (
                      <li key={index} className="flex items-start gap-1">
                        <AlertTriangle className="h-4 w-4 mt-0.5" />
                        <span>{threat.replace('_', ' ').toUpperCase()}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button onClick={scanContent} disabled={isLoading || !content}>
              {isLoading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Scanning...
                </>
              ) : (
                'Scan Content'
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
