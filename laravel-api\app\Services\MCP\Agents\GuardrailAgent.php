<?php

declare(strict_types=1);

namespace App\Services\MCP\Agents;

use App\Services\MCP\AbstractMCPAgent;
use Illuminate\Support\Facades\Log;

class GuardrailAgent extends AbstractMCPAgent
{
    protected array $contentFilters = [
        'profanity' => 'checkProfanity',
        'personal_info' => 'checkPersonalInfo',
        'harmful_content' => 'checkHarmfulContent',
        'spam' => 'checkSpam',
        'inappropriate' => 'checkInappropriate'
    ];

    protected array $blockedWords = [
        'profanity' => ['damn', 'hell'], // Minimal list for demo
        'harmful' => ['violence', 'harm', 'dangerous']
    ];

    protected array $piiPatterns = [
        'email' => '/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/',
        'phone' => '/\b\d{3}-\d{3}-\d{4}\b|\b\(\d{3}\)\s*\d{3}-\d{4}\b/',
        'ssn' => '/\b\d{3}-\d{2}-\d{4}\b/',
        'credit_card' => '/\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/'
    ];

    public function getName(): string
    {
        return 'guardrail';
    }

    public function getType(): string
    {
        return 'secondary';
    }

    public function getExecutionOrder(): int
    {
        return 13; // Last in the pipeline
    }

    protected function process(array $input, string $sessionId): array
    {
        $userMessage = $input['message'] ?? '';
        $llmResponse = $input['llm_response'] ?? '';
        $formattedResponse = $input['formatted_response'] ?? [];

        Log::info('Guardrail Agent processing content', [
            'session_id' => $sessionId,
            'user_message_length' => strlen($userMessage),
            'llm_response_length' => strlen($llmResponse)
        ]);

        try {
            // Check user input
            $inputViolations = $this->checkContent($userMessage, 'user_input');
            
            // Check LLM response
            $responseViolations = $this->checkContent($llmResponse, 'llm_response');
            
            // Check formatted response content
            $formattedContent = is_array($formattedResponse) ? 
                ($formattedResponse['content'] ?? '') : $formattedResponse;
            $formattedViolations = $this->checkContent($formattedContent, 'formatted_response');

            $allViolations = array_merge($inputViolations, $responseViolations, $formattedViolations);
            
            // Apply content filtering
            $filteredResponse = $this->applyContentFiltering($input, $allViolations);
            
            // Generate safety report
            $safetyReport = $this->generateSafetyReport($allViolations, $sessionId);

            $result = array_merge($input, [
                'guardrail_report' => $safetyReport,
                'content_violations' => $allViolations,
                'filtered_response' => $filteredResponse,
                'guardrail_metadata' => [
                    'total_violations' => count($allViolations),
                    'severity_levels' => $this->getViolationSeverities($allViolations),
                    'action_taken' => $this->getActionTaken($allViolations),
                    'processing_time' => microtime(true) - ($_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true))
                ]
            ]);

            Log::info('Guardrail Agent completed processing', [
                'session_id' => $sessionId,
                'violations_found' => count($allViolations),
                'action_taken' => $this->getActionTaken($allViolations)
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Guardrail Agent failed', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);

            return array_merge($input, [
                'guardrail_report' => [
                    'status' => 'error',
                    'message' => 'Content filtering unavailable'
                ],
                'guardrail_metadata' => [
                    'error' => $e->getMessage(),
                    'fallback_used' => true
                ]
            ]);
        }
    }

    protected function checkContent(string $content, string $source): array
    {
        if (empty($content)) {
            return [];
        }

        $violations = [];
        $content = strtolower($content);

        foreach ($this->contentFilters as $filterName => $filterMethod) {
            $violation = $this->$filterMethod($content, $source);
            if ($violation) {
                $violations[] = $violation;
            }
        }

        return $violations;
    }

    protected function checkProfanity(string $content, string $source): ?array
    {
        foreach ($this->blockedWords['profanity'] as $word) {
            if (strpos($content, strtolower($word)) !== false) {
                return [
                    'type' => 'profanity',
                    'severity' => 'medium',
                    'source' => $source,
                    'detected_word' => $word,
                    'message' => 'Profanity detected in content'
                ];
            }
        }
        return null;
    }

    protected function checkPersonalInfo(string $content, string $source): ?array
    {
        foreach ($this->piiPatterns as $type => $pattern) {
            if (preg_match($pattern, $content)) {
                return [
                    'type' => 'personal_info',
                    'severity' => 'high',
                    'source' => $source,
                    'pii_type' => $type,
                    'message' => "Potential {$type} detected in content"
                ];
            }
        }
        return null;
    }

    protected function checkHarmfulContent(string $content, string $source): ?array
    {
        foreach ($this->blockedWords['harmful'] as $word) {
            if (strpos($content, strtolower($word)) !== false) {
                return [
                    'type' => 'harmful_content',
                    'severity' => 'high',
                    'source' => $source,
                    'detected_word' => $word,
                    'message' => 'Potentially harmful content detected'
                ];
            }
        }
        return null;
    }

    protected function checkSpam(string $content, string $source): ?array
    {
        // Simple spam detection
        $spamIndicators = ['buy now', 'click here', 'free money', 'urgent'];
        $spamCount = 0;
        
        foreach ($spamIndicators as $indicator) {
            if (strpos($content, $indicator) !== false) {
                $spamCount++;
            }
        }

        if ($spamCount >= 2) {
            return [
                'type' => 'spam',
                'severity' => 'medium',
                'source' => $source,
                'spam_score' => $spamCount,
                'message' => 'Content appears to be spam'
            ];
        }

        return null;
    }

    protected function checkInappropriate(string $content, string $source): ?array
    {
        // Check for inappropriate content patterns
        $inappropriatePatterns = [
            '/\b(adult|explicit|nsfw)\b/i',
            '/\b(illegal|unlawful)\b/i'
        ];

        foreach ($inappropriatePatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return [
                    'type' => 'inappropriate',
                    'severity' => 'high',
                    'source' => $source,
                    'message' => 'Inappropriate content detected'
                ];
            }
        }

        return null;
    }

    protected function applyContentFiltering(array $input, array $violations): array
    {
        $highSeverityViolations = array_filter($violations, fn($v) => $v['severity'] === 'high');
        
        if (!empty($highSeverityViolations)) {
            // Block response for high severity violations
            return [
                'action' => 'blocked',
                'reason' => 'High severity content violations detected',
                'violations' => $highSeverityViolations,
                'safe_response' => 'I cannot provide a response to this request due to content policy violations.'
            ];
        }

        $mediumSeverityViolations = array_filter($violations, fn($v) => $v['severity'] === 'medium');
        
        if (!empty($mediumSeverityViolations)) {
            // Filter/modify response for medium severity violations
            $filteredResponse = $this->sanitizeContent($input);
            return [
                'action' => 'filtered',
                'reason' => 'Medium severity violations filtered',
                'violations' => $mediumSeverityViolations,
                'filtered_response' => $filteredResponse
            ];
        }

        // No action needed
        return [
            'action' => 'allowed',
            'reason' => 'No content violations detected',
            'original_response' => $input
        ];
    }

    protected function sanitizeContent(array $input): array
    {
        $sanitized = $input;
        
        // Remove or replace problematic content
        if (isset($sanitized['llm_response'])) {
            $sanitized['llm_response'] = $this->sanitizeText($sanitized['llm_response']);
        }
        
        if (isset($sanitized['formatted_response']['content'])) {
            if (is_string($sanitized['formatted_response']['content'])) {
                $sanitized['formatted_response']['content'] = $this->sanitizeText($sanitized['formatted_response']['content']);
            }
        }

        return $sanitized;
    }

    protected function sanitizeText(string $text): string
    {
        // Replace profanity with asterisks
        foreach ($this->blockedWords['profanity'] as $word) {
            $replacement = str_repeat('*', strlen($word));
            $text = str_ireplace($word, $replacement, $text);
        }

        // Remove PII patterns
        foreach ($this->piiPatterns as $type => $pattern) {
            $text = preg_replace($pattern, '[REDACTED]', $text);
        }

        return $text;
    }

    protected function generateSafetyReport(array $violations, string $sessionId): array
    {
        $severityCounts = [
            'high' => 0,
            'medium' => 0,
            'low' => 0
        ];

        foreach ($violations as $violation) {
            $severity = $violation['severity'] ?? 'low';
            $severityCounts[$severity]++;
        }

        $overallRisk = $this->calculateOverallRisk($severityCounts);

        return [
            'session_id' => $sessionId,
            'timestamp' => now()->toISOString(),
            'total_violations' => count($violations),
            'severity_breakdown' => $severityCounts,
            'overall_risk_level' => $overallRisk,
            'violations' => $violations,
            'recommendations' => $this->generateRecommendations($violations)
        ];
    }

    protected function calculateOverallRisk(array $severityCounts): string
    {
        if ($severityCounts['high'] > 0) {
            return 'high';
        }
        
        if ($severityCounts['medium'] > 2) {
            return 'high';
        }
        
        if ($severityCounts['medium'] > 0) {
            return 'medium';
        }
        
        return 'low';
    }

    protected function generateRecommendations(array $violations): array
    {
        $recommendations = [];
        
        $violationTypes = array_unique(array_column($violations, 'type'));
        
        foreach ($violationTypes as $type) {
            switch ($type) {
                case 'personal_info':
                    $recommendations[] = 'Avoid sharing personal information in conversations';
                    break;
                case 'harmful_content':
                    $recommendations[] = 'Please keep conversations constructive and safe';
                    break;
                case 'profanity':
                    $recommendations[] = 'Consider using more professional language';
                    break;
                case 'spam':
                    $recommendations[] = 'Avoid promotional or spam-like content';
                    break;
            }
        }
        
        return $recommendations;
    }

    protected function getViolationSeverities(array $violations): array
    {
        $severities = array_column($violations, 'severity');
        return array_count_values($severities);
    }

    protected function getActionTaken(array $violations): string
    {
        $highSeverity = array_filter($violations, fn($v) => $v['severity'] === 'high');
        
        if (!empty($highSeverity)) {
            return 'blocked';
        }
        
        $mediumSeverity = array_filter($violations, fn($v) => $v['severity'] === 'medium');
        
        if (!empty($mediumSeverity)) {
            return 'filtered';
        }
        
        return 'allowed';
    }

    public function validateInput(array $input): bool
    {
        return isset($input['message']) || isset($input['llm_response']);
    }
}
