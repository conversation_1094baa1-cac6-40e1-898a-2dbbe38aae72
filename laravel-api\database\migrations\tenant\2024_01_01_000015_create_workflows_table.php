<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('workflows', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('status')->default('draft'); // draft, active, inactive, archived
            $table->string('trigger_type'); // manual, webhook, schedule, event
            $table->json('trigger_config')->nullable();
            $table->json('nodes'); // Workflow nodes and connections
            $table->json('variables')->nullable(); // Workflow variables
            $table->json('settings')->nullable(); // Execution settings
            $table->integer('execution_count')->default(0);
            $table->timestamp('last_executed_at')->nullable();
            $table->timestamps();
            
            $table->index(['status', 'trigger_type']);
            $table->index(['last_executed_at', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('workflows');
    }
};
