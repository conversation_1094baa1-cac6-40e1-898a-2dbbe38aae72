<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\Performance\PerformanceMonitor;
use Illuminate\Support\Facades\Log;

class PerformanceMiddleware
{
    public function __construct(
        private PerformanceMonitor $performanceMonitor
    ) {}

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        
        // Start monitoring the request
        $this->performanceMonitor->startTimer('request_processing');
        
        // Process the request
        $response = $next($request);
        
        // Calculate metrics
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
        $memoryUsed = $endMemory - $startMemory;
        
        // Record performance metrics
        $this->performanceMonitor->endTimer('request_processing');
        $this->performanceMonitor->recordMetric('api_response_time', $executionTime, [
            'endpoint' => $request->path(),
            'method' => $request->method(),
            'status_code' => $response->getStatusCode()
        ]);
        
        $this->performanceMonitor->recordMetric('api_memory_usage', $memoryUsed, [
            'endpoint' => $request->path()
        ]);
        
        // Monitor memory usage
        $this->performanceMonitor->monitorMemoryUsage();
        
        // Add performance headers to response
        $response->headers->set('X-Response-Time', round($executionTime, 2) . 'ms');
        $response->headers->set('X-Memory-Usage', $this->formatBytes($memoryUsed));
        $response->headers->set('X-Peak-Memory', $this->formatBytes(memory_get_peak_usage(true)));
        
        // Log slow requests
        if ($executionTime > 2000) { // Slower than 2 seconds
            Log::warning('Slow API request detected', [
                'endpoint' => $request->path(),
                'method' => $request->method(),
                'execution_time' => $executionTime,
                'memory_used' => $memoryUsed,
                'user_agent' => $request->userAgent(),
                'ip' => $request->ip()
            ]);
        }
        
        // Log high memory usage
        if ($memoryUsed > 50 * 1024 * 1024) { // More than 50MB
            Log::warning('High memory usage in API request', [
                'endpoint' => $request->path(),
                'memory_used' => $this->formatBytes($memoryUsed),
                'peak_memory' => $this->formatBytes(memory_get_peak_usage(true))
            ]);
        }
        
        return $response;
    }
    
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
