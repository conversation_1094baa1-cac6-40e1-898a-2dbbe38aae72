<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Contracts\MCPOrchestratorInterface;
use Illuminate\Support\Str;

class MCPIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected MCPOrchestratorInterface $orchestrator;

    protected function setUp(): void
    {
        parent::setUp();
        $this->orchestrator = app(MCPOrchestratorInterface::class);
    }

    /**
     * Test complete MCP pipeline execution
     */
    public function test_complete_mcp_pipeline_execution()
    {
        $sessionId = Str::uuid()->toString();
        $input = [
            'message' => 'What is artificial intelligence and how does it work?',
            'context' => [],
            'user_id' => 1,
            'timestamp' => now()->toISOString(),
        ];

        $result = $this->orchestrator->processRequest($input, $sessionId);

        // Verify all primary agents executed
        $this->assertArrayHasKey('intent', $result);
        $this->assertArrayHasKey('confidence', $result);
        $this->assertArrayHasKey('retrieved_documents', $result);
        $this->assertArrayHasKey('llm_response', $result);
        $this->assertArrayHasKey('conversation_id', $result);

        // Verify secondary agents executed
        $this->assertArrayHasKey('tool_executions', $result);
        $this->assertArrayHasKey('formatted_response', $result);
        $this->assertArrayHasKey('guardrail_report', $result);

        // Verify intent detection
        $this->assertContains($result['intent'], ['question', 'general', 'conversation']);
        $this->assertGreaterThanOrEqual(0, $result['confidence']);
        $this->assertLessThanOrEqual(1, $result['confidence']);

        // Verify guardrail processing
        $this->assertArrayHasKey('status', $result['guardrail_report']);
        $this->assertArrayHasKey('total_violations', $result['guardrail_report']);
    }

    /**
     * Test API endpoints
     */
    public function test_api_endpoints_functionality()
    {
        // Test system status endpoint
        $response = $this->getJson('/api/test/system-status');
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'system_status',
                    'agents',
                    'agent_count',
                    'primary_agents',
                    'secondary_agents'
                ]);

        // Test health check endpoint
        $response = $this->getJson('/api/health');
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'timestamp'
                ]);

        // Test MCP processing endpoint
        $response = $this->postJson('/api/mcp/process', [
            'message' => 'Hello, how are you?',
            'context' => []
        ]);
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'session_id',
                    'response'
                ]);
    }

    /**
     * Test agent individual functionality
     */
    public function test_individual_agent_execution()
    {
        $agents = ['intent', 'retriever', 'llm', 'memory', 'tool', 'workflow', 'formatter', 'guardrail'];
        
        foreach ($agents as $agentName) {
            $response = $this->getJson("/api/test/agent/{$agentName}");
            $response->assertStatus(200)
                    ->assertJsonStructure([
                        'success',
                        'agent',
                        'session_id',
                        'result'
                    ]);
        }
    }

    /**
     * Test error handling and resilience
     */
    public function test_error_handling_resilience()
    {
        // Test with invalid input
        $response = $this->postJson('/api/mcp/process', [
            'message' => '',
            'context' => []
        ]);
        $response->assertStatus(422); // Validation error

        // Test with malformed data
        $response = $this->postJson('/api/mcp/process', [
            'message' => str_repeat('a', 10000), // Very long message
            'context' => []
        ]);
        $response->assertStatus(200); // Should handle gracefully

        // Test non-existent agent
        $response = $this->getJson('/api/test/agent/nonexistent');
        $response->assertStatus(404);
    }

    /**
     * Test content filtering and guardrails
     */
    public function test_content_filtering_guardrails()
    {
        $testCases = [
            [
                'message' => 'This is a normal, safe message',
                'expected_action' => 'allowed'
            ],
            [
                'message' => 'This contains some damn profanity',
                'expected_action' => 'filtered'
            ],
            [
                'message' => 'My <NAME_EMAIL> and phone is ************',
                'expected_action' => 'filtered'
            ]
        ];

        foreach ($testCases as $testCase) {
            $sessionId = Str::uuid()->toString();
            $input = [
                'message' => $testCase['message'],
                'context' => [],
                'user_id' => 1,
                'timestamp' => now()->toISOString(),
            ];

            $result = $this->orchestrator->processRequest($input, $sessionId);
            
            $this->assertArrayHasKey('guardrail_report', $result);
            $this->assertArrayHasKey('filtered_response', $result);
            
            if ($testCase['expected_action'] === 'filtered') {
                $this->assertGreaterThan(0, $result['guardrail_report']['total_violations']);
            }
        }
    }

    /**
     * Test conversation memory and context
     */
    public function test_conversation_memory_context()
    {
        $sessionId = Str::uuid()->toString();
        
        // First message
        $input1 = [
            'message' => 'My name is John',
            'context' => [],
            'user_id' => 1,
            'timestamp' => now()->toISOString(),
        ];
        
        $result1 = $this->orchestrator->processRequest($input1, $sessionId);
        $this->assertArrayHasKey('conversation_id', $result1);
        
        // Second message referencing first
        $input2 = [
            'message' => 'What is my name?',
            'context' => $result1['conversation_context'] ?? [],
            'user_id' => 1,
            'timestamp' => now()->toISOString(),
        ];
        
        $result2 = $this->orchestrator->processRequest($input2, $sessionId);
        
        // Should maintain same conversation
        $this->assertEquals($result1['conversation_id'], $result2['conversation_id']);
        $this->assertArrayHasKey('conversation_context', $result2);
        $this->assertGreaterThan(1, count($result2['conversation_context']['messages'] ?? []));
    }

    /**
     * Test tool execution and integration
     */
    public function test_tool_execution_integration()
    {
        $sessionId = Str::uuid()->toString();
        $input = [
            'message' => 'What is the weather like today?',
            'context' => [],
            'user_id' => 1,
            'timestamp' => now()->toISOString(),
        ];

        $result = $this->orchestrator->processRequest($input, $sessionId);
        
        // Should detect weather intent and execute weather tool
        $this->assertArrayHasKey('tool_executions', $result);
        
        if (!empty($result['tool_executions'])) {
            $weatherTool = collect($result['tool_executions'])
                ->firstWhere('tool', 'weather');
            
            if ($weatherTool) {
                $this->assertEquals('weather', $weatherTool['tool']);
                $this->assertArrayHasKey('status', $weatherTool);
            }
        }
    }

    /**
     * Test performance benchmarks
     */
    public function test_performance_benchmarks()
    {
        $sessionId = Str::uuid()->toString();
        $input = [
            'message' => 'This is a performance test message',
            'context' => [],
            'user_id' => 1,
            'timestamp' => now()->toISOString(),
        ];

        $startTime = microtime(true);
        $result = $this->orchestrator->processRequest($input, $sessionId);
        $endTime = microtime(true);
        
        $executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
        
        // Performance assertions
        $this->assertLessThan(5000, $executionTime, 'Pipeline execution should complete within 5 seconds');
        
        // Check individual agent performance
        if (isset($result['memory_metadata']['processing_time'])) {
            $this->assertLessThan(1000, $result['memory_metadata']['processing_time'] * 1000);
        }
    }
}
