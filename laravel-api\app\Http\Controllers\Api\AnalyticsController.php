<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\AnalyticsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AnalyticsController extends Controller
{
    protected AnalyticsService $analyticsService;

    public function __construct(AnalyticsService $analyticsService)
    {
        $this->analyticsService = $analyticsService;
    }

    /**
     * Get dashboard analytics
     */
    public function getDashboard(Request $request): JsonResponse
    {
        try {
            $days = $request->integer('days', 30);
            $days = min(max($days, 1), 365); // Limit between 1 and 365 days
            
            $analytics = $this->analyticsService->getDashboardAnalytics($days);

            return response()->json([
                'success' => true,
                'data' => $analytics,
                'period_days' => $days
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Track an analytics event
     */
    public function trackEvent(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'event_type' => 'required|string|max:100',
                'event_name' => 'required|string|max:100',
                'entity_type' => 'nullable|string|max:100',
                'entity_id' => 'nullable|string|max:100',
                'properties' => 'nullable|array',
                'metadata' => 'nullable|array',
                'duration_ms' => 'nullable|integer|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $event = $this->analyticsService->trackEvent(
                $request->event_type,
                $request->event_name,
                $request->entity_type,
                $request->entity_id,
                $request->properties ?? [],
                $request->metadata ?? [],
                $request->duration_ms
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'event_id' => $event->id,
                    'tracked_at' => $event->occurred_at
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to track event: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate custom report
     */
    public function generateReport(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
                'event_type' => 'nullable|string',
                'entity_type' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $filters = $request->only(['start_date', 'end_date', 'event_type', 'entity_type']);
            $report = $this->analyticsService->generateCustomReport($filters);

            return response()->json([
                'success' => true,
                'data' => $report,
                'filters' => $filters
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to generate report: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user activity analytics
     */
    public function getUserActivity(Request $request): JsonResponse
    {
        try {
            $days = $request->integer('days', 7);
            $analytics = $this->analyticsService->getDashboardAnalytics($days);

            return response()->json([
                'success' => true,
                'data' => $analytics['user_activity']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get performance metrics
     */
    public function getPerformanceMetrics(Request $request): JsonResponse
    {
        try {
            $days = $request->integer('days', 7);
            $analytics = $this->analyticsService->getDashboardAnalytics($days);

            return response()->json([
                'success' => true,
                'data' => $analytics['performance_metrics']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get error analytics
     */
    public function getErrorAnalytics(Request $request): JsonResponse
    {
        try {
            $days = $request->integer('days', 7);
            $analytics = $this->analyticsService->getDashboardAnalytics($days);

            return response()->json([
                'success' => true,
                'data' => $analytics['error_metrics']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get workflow analytics
     */
    public function getWorkflowAnalytics(Request $request): JsonResponse
    {
        try {
            $days = $request->integer('days', 30);
            $analytics = $this->analyticsService->getDashboardAnalytics($days);

            return response()->json([
                'success' => true,
                'data' => $analytics['workflow_metrics']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get document analytics
     */
    public function getDocumentAnalytics(Request $request): JsonResponse
    {
        try {
            $days = $request->integer('days', 30);
            $analytics = $this->analyticsService->getDashboardAnalytics($days);

            return response()->json([
                'success' => true,
                'data' => $analytics['document_metrics']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get real-time metrics
     */
    public function getRealTimeMetrics(): JsonResponse
    {
        try {
            // Get metrics for the last hour
            $analytics = $this->analyticsService->getDashboardAnalytics(1);
            
            // Extract real-time relevant data
            $realTimeData = [
                'active_users_last_hour' => $analytics['user_activity']['daily_active_users'][now()->format('Y-m-d')] ?? 0,
                'recent_executions' => $analytics['workflow_metrics']['execution_trends'][now()->format('Y-m-d')] ?? 0,
                'recent_errors' => $analytics['error_metrics']['error_trends'][now()->format('Y-m-d')] ?? 0,
                'avg_response_time' => $analytics['performance_metrics']['avg_response_time'],
                'system_health' => $this->getSystemHealth($analytics)
            ];

            return response()->json([
                'success' => true,
                'data' => $realTimeData,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export analytics data
     */
    public function exportData(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'format' => 'required|in:json,csv',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
                'metrics' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $days = $request->start_date && $request->end_date 
                ? now()->parse($request->start_date)->diffInDays(now()->parse($request->end_date))
                : 30;

            $analytics = $this->analyticsService->getDashboardAnalytics($days);
            
            // Filter metrics if specified
            if ($request->metrics) {
                $analytics = array_intersect_key($analytics, array_flip($request->metrics));
            }

            $exportData = [
                'exported_at' => now()->toISOString(),
                'period_days' => $days,
                'analytics' => $analytics
            ];

            return response()->json([
                'success' => true,
                'data' => $exportData,
                'format' => $request->format
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to export data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get system health status
     */
    protected function getSystemHealth(array $analytics): string
    {
        $errorRate = $analytics['error_metrics']['error_rate_percent'];
        $avgResponseTime = $analytics['performance_metrics']['avg_response_time'];
        
        if ($errorRate > 5 || $avgResponseTime > 2000) {
            return 'critical';
        } elseif ($errorRate > 2 || $avgResponseTime > 1000) {
            return 'warning';
        } else {
            return 'healthy';
        }
    }
}
