# Admin Access Implementation Test Guide

## What was implemented:

### 1. Backend (Laravel API)
- **AdminMiddleware**: Created middleware to check for admin role and active status
- **AdminController**: New controller with admin-specific endpoints
- **Route Protection**: Applied admin middleware to sensitive routes
- **Middleware Registration**: Registered admin middleware in bootstrap/app.php

### 2. Frontend (Next.js)
- **Admin Authentication**: Added `isAdmin` property to AuthContext
- **Admin HOC**: Created `withAdminAuth` higher-order component
- **Unauthorized Page**: Created page for non-admin users
- **API Client**: Added admin-specific API methods
- **Dashboard Protection**: Protected main dashboard with admin auth

### 3. Route Structure
- **Public routes**: `/health`, `/auth/login`, `/auth/register`
- **User routes**: `/mcp/*` (basic MCP functionality)
- **Admin routes**: All other routes require admin access

## Testing Steps:

### 1. Test Admin Access
```bash
# 1. Start Laravel API
cd laravel-api
php artisan serve

# 2. Start Next.js frontend
cd nextjs-admin
npm run dev
```

### 2. Create Admin User
```bash
# In Laravel API directory
php artisan tinker

# Create admin user
$user = new App\Models\User();
$user->name = 'Admin User';
$user->email = '<EMAIL>';
$user->password = bcrypt('password123');
$user->role = 'admin';
$user->is_active = true;
$user->tenant_id = 1; // or appropriate tenant ID
$user->email_verified_at = now();
$user->save();
```

### 3. Create Regular User
```bash
# In Laravel API tinker
$user = new App\Models\User();
$user->name = 'Regular User';
$user->email = '<EMAIL>';
$user->password = bcrypt('password123');
$user->role = 'user';
$user->is_active = true;
$user->tenant_id = 1;
$user->email_verified_at = now();
$user->save();
```

### 4. Test Access Scenarios

#### Scenario A: Admin User
1. Login with `<EMAIL>` / `password123`
2. Should have access to all pages
3. Dashboard should load admin data
4. All navigation items should be accessible

#### Scenario B: Regular User
1. Login with `<EMAIL>` / `password123`
2. Should be redirected to `/unauthorized` page
3. Should see access denied message
4. Should have options to logout or go to dashboard

#### Scenario C: Unauthenticated User
1. Try to access any protected route directly
2. Should be redirected to login page

### 5. API Endpoint Testing

#### Test Admin Endpoints (require admin role):
```bash
# Get admin dashboard (should work for admin)
curl -H "Authorization: Bearer {admin_token}" http://localhost:8000/api/admin/dashboard

# Get admin dashboard (should fail for regular user)
curl -H "Authorization: Bearer {user_token}" http://localhost:8000/api/admin/dashboard

# Test other admin endpoints
curl -H "Authorization: Bearer {admin_token}" http://localhost:8000/api/admin/users
curl -H "Authorization: Bearer {admin_token}" http://localhost:8000/api/analytics/dashboard
curl -H "Authorization: Bearer {admin_token}" http://localhost:8000/api/monitoring/metrics
```

#### Test User Endpoints (should work for both):
```bash
# MCP endpoints (should work for both admin and user)
curl -H "Authorization: Bearer {token}" http://localhost:8000/api/mcp/agents
curl -H "Authorization: Bearer {token}" http://localhost:8000/api/auth/me
```

### 6. Expected Responses

#### Admin Access Success:
- Status: 200
- Response contains data

#### Admin Access Denied:
- Status: 403
- Response: `{"error": "Insufficient privileges", "message": "Admin access required for this resource"}`

#### Authentication Required:
- Status: 401
- Response: `{"error": "Authentication required", "message": "You must be logged in to access this resource"}`

## Routes Protected by Admin Middleware:

- `/admin/*` - All admin management routes
- `/test/*` - System testing routes
- `/monitoring/*` - System monitoring routes
- `/widgets/*` - Widget management routes
- `/providers/*` - AI provider management routes
- `/knowledge-bases/*` - Knowledge base management routes
- `/workflows/*` - Workflow management routes
- `/analytics/*` - Analytics and reporting routes
- `/security/*` - Security management routes

## Routes Available to All Authenticated Users:

- `/mcp/*` - MCP processing and agent interaction
- `/auth/logout`, `/auth/me`, `/auth/profile` - Basic auth operations
- `/health` - System health check (public)

This implementation ensures that admin functionality is properly protected while still allowing regular users to access basic MCP functionality.
