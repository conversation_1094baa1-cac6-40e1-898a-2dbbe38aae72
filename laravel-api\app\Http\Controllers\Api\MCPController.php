<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Contracts\MCPOrchestratorInterface;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

class MCPController extends Controller
{
    public function __construct(
        private MCPOrchestratorInterface $orchestrator
    ) {}

    /**
     * Process a user request through the MCP pipeline
     */
    public function processRequest(Request $request): JsonResponse
    {
        $request->validate([
            'message' => 'required|string',
            'session_id' => 'nullable|string',
            'context' => 'nullable|array',
        ]);

        $sessionId = $request->input('session_id', Str::uuid()->toString());
        
        $input = [
            'message' => $request->input('message'),
            'context' => $request->input('context', []),
            'user_id' => auth()->id(),
            'timestamp' => now()->toISOString(),
        ];

        try {
            $result = $this->orchestrator->processRequest($input, $sessionId);

            return response()->json([
                'success' => true,
                'session_id' => $sessionId,
                'response' => $result,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get execution history for a session
     */
    public function getExecutionHistory(string $sessionId): JsonResponse
    {
        try {
            $history = $this->orchestrator->getExecutionHistory($sessionId);

            return response()->json([
                'success' => true,
                'session_id' => $sessionId,
                'history' => $history,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get all registered agents
     */
    public function getAgents(): JsonResponse
    {
        try {
            $agents = collect($this->orchestrator->getAgents())
                ->map(function ($agent) {
                    return [
                        'name' => $agent->getName(),
                        'type' => $agent->getType(),
                        'is_active' => $agent->isActive(),
                        'execution_order' => $agent->getExecutionOrder(),
                        'config' => $agent->getConfig(),
                    ];
                })
                ->values();

            return response()->json([
                'success' => true,
                'agents' => $agents,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
