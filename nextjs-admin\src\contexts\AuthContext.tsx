'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

interface User {
  id: number
  name: string
  email: string
  role: string
  tenant_id: string
  is_active: boolean
  last_login_at: string | null
  created_at: string
}

interface Tenant {
  id: string
  name: string
  subdomain: string
  subscription_plan: string
  subscription_expires_at: string | null
  branding: any
  settings: any
}

interface AuthContextType {
  user: User | null
  tenant: Tenant | null
  token: string | null
  isLoading: boolean
  isAuthenticated: boolean
  isAdmin: boolean
  login: (email: string, password: string, tenantSubdomain?: string) => Promise<boolean>
  logout: () => void
  updateProfile: (data: Partial<User>) => Promise<boolean>
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [tenant, setTenant] = useState<Tenant | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  const isAuthenticated = !!user && !!token
  const isAdmin = !!user && user.role === 'admin'

  // Initialize auth state from localStorage
  useEffect(() => {
    const initAuth = async () => {
      try {
        const storedToken = localStorage.getItem('auth_token')
        const storedUser = localStorage.getItem('user_data')
        const storedTenant = localStorage.getItem('tenant_data')

        if (storedToken && storedUser) {
          setToken(storedToken)
          setUser(JSON.parse(storedUser))
          
          if (storedTenant) {
            setTenant(JSON.parse(storedTenant))
          }

          // Verify token is still valid
          await refreshUser()
        }
      } catch (error) {
        console.error('Auth initialization error:', error)
        clearAuthData()
      } finally {
        setIsLoading(false)
      }
    }

    initAuth()
  }, [])

  const login = async (email: string, password: string, tenantSubdomain?: string): Promise<boolean> => {
    try {
      const response = await fetch('http://localhost:8000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email,
          password,
          tenant_subdomain: tenantSubdomain
        })
      })

      const data = await response.json()

      if (data.success) {
        const { user: userData, tenant: tenantData, token: authToken } = data.data
        
        setUser(userData)
        setTenant(tenantData)
        setToken(authToken)
        
        // Store in localStorage
        localStorage.setItem('auth_token', authToken)
        localStorage.setItem('user_data', JSON.stringify(userData))
        if (tenantData) {
          localStorage.setItem('tenant_data', JSON.stringify(tenantData))
        }

        return true
      } else {
        throw new Error(data.error || 'Login failed')
      }
    } catch (error) {
      console.error('Login error:', error)
      return false
    }
  }

  const logout = async () => {
    try {
      if (token) {
        await fetch('http://localhost:8000/api/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      clearAuthData()
      router.push('/login')
    }
  }

  const updateProfile = async (data: Partial<User>): Promise<boolean> => {
    try {
      if (!token) return false

      const response = await fetch('http://localhost:8000/api/auth/profile', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })

      const result = await response.json()

      if (result.success) {
        const updatedUser = { ...user, ...result.data.user }
        setUser(updatedUser)
        localStorage.setItem('user_data', JSON.stringify(updatedUser))
        return true
      } else {
        throw new Error(result.error || 'Profile update failed')
      }
    } catch (error) {
      console.error('Profile update error:', error)
      return false
    }
  }

  const refreshUser = async (): Promise<void> => {
    try {
      if (!token) return

      const response = await fetch('http://localhost:8000/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      if (data.success) {
        const { user: userData, tenant: tenantData } = data.data
        setUser(userData)
        setTenant(tenantData)
        
        // Update localStorage
        localStorage.setItem('user_data', JSON.stringify(userData))
        if (tenantData) {
          localStorage.setItem('tenant_data', JSON.stringify(tenantData))
        }
      } else {
        // Token is invalid
        clearAuthData()
        router.push('/login')
      }
    } catch (error) {
      console.error('Refresh user error:', error)
      clearAuthData()
      router.push('/login')
    }
  }

  const clearAuthData = () => {
    setUser(null)
    setTenant(null)
    setToken(null)
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_data')
    localStorage.removeItem('tenant_data')
  }

  // API helper function with auth
  const apiCall = async (endpoint: string, options: RequestInit = {}) => {
    const headers = {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...(tenant && { 'X-Tenant-ID': tenant.id }),
      ...options.headers
    }

    const response = await fetch(`http://localhost:8000/api${endpoint}`, {
      ...options,
      headers
    })

    if (response.status === 401) {
      // Token expired or invalid
      clearAuthData()
      router.push('/login')
      throw new Error('Authentication required')
    }

    return response
  }

  const value: AuthContextType = {
    user,
    tenant,
    token,
    isLoading,
    isAuthenticated,
    isAdmin,
    login,
    logout,
    updateProfile,
    refreshUser
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Higher-order component for protected routes
export function withAuth<P extends object>(Component: React.ComponentType<P>) {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, isLoading } = useAuth()
    const router = useRouter()

    useEffect(() => {
      if (!isLoading && !isAuthenticated) {
        router.push('/login')
      }
    }, [isAuthenticated, isLoading, router])

    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      )
    }

    if (!isAuthenticated) {
      return null
    }

    return <Component {...props} />
  }
}

// Higher-order component for admin-only routes
export function withAdminAuth<P extends object>(Component: React.ComponentType<P>) {
  return function AdminAuthenticatedComponent(props: P) {
    const { isAuthenticated, isAdmin, isLoading } = useAuth()
    const router = useRouter()

    useEffect(() => {
      if (!isLoading) {
        if (!isAuthenticated) {
          router.push('/login')
        } else if (!isAdmin) {
          router.push('/unauthorized')
        }
      }
    }, [isAuthenticated, isAdmin, isLoading, router])

    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      )
    }

    if (!isAuthenticated || !isAdmin) {
      return null
    }

    return <Component {...props} />
  }
}

// Hook for making authenticated API calls
export function useApi() {
  const { token, tenant } = useAuth()
  const router = useRouter()

  const apiCall = async (endpoint: string, options: RequestInit = {}) => {
    const headers = {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...(tenant && { 'X-Tenant-ID': tenant.id }),
      ...options.headers
    }

    const response = await fetch(`http://localhost:8000/api${endpoint}`, {
      ...options,
      headers
    })

    if (response.status === 401) {
      router.push('/login')
      throw new Error('Authentication required')
    }

    return response
  }

  return { apiCall }
}
