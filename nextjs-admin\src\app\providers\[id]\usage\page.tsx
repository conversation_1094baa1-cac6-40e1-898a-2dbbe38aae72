'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useApi } from '@/contexts/AuthContext'
import { 
  ArrowLeft,
  Activity,
  TrendingUp,
  Clock,
  AlertTriangle,
  CheckCircle,
  RefreshCw
} from 'lucide-react'
import Link from 'next/link'

interface UsageData {
  daily_stats: Record<string, {
    requests: number
    tokens: number
    errors: number
    avg_response_time: number
  }>
  totals: {
    requests: number
    tokens: number
    errors: number
    avg_response_time: number
  }
  limits: {
    requests_per_minute?: number
    tokens_per_minute?: number
    requests_per_day?: number
  }
  is_within_limits: boolean
}

interface Provider {
  id: number
  name: string
  display_name: string
  status: string
  is_enabled: boolean
}

export default function ProviderUsagePage() {
  const params = useParams()
  const { apiCall } = useApi()
  const [provider, setProvider] = useState<Provider | null>(null)
  const [usageData, setUsageData] = useState<UsageData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [days, setDays] = useState(7)

  const fetchProviderData = async () => {
    try {
      const response = await apiCall(`/providers/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setProvider(data.data)
        }
      }
    } catch (error) {
      console.error('Failed to fetch provider:', error)
    }
  }

  const fetchUsageData = async () => {
    setIsLoading(true)
    try {
      const response = await apiCall(`/providers/${params.id}/usage?days=${days}`)
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setUsageData(data.data)
        }
      }
    } catch (error) {
      console.error('Failed to fetch usage data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchProviderData()
  }, [params.id])

  useEffect(() => {
    fetchUsageData()
  }, [params.id, days])

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num)
  }

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`
    return `${(ms / 1000).toFixed(2)}s`
  }

  const getUsagePercentage = (used: number, limit: number) => {
    if (!limit) return 0
    return Math.min((used / limit) * 100, 100)
  }

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600'
    if (percentage >= 75) return 'text-yellow-600'
    return 'text-green-600'
  }

  if (isLoading && !usageData) {
    return (
      <DashboardLayout>
        <div className="space-y-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/providers">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Providers
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {provider?.display_name} Usage Analytics
              </h1>
              <p className="text-gray-600">
                Monitor usage patterns and performance metrics
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <select
              value={days}
              onChange={(e) => setDays(parseInt(e.target.value))}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={7}>Last 7 days</option>
              <option value={14}>Last 14 days</option>
              <option value={30}>Last 30 days</option>
            </select>
            <Button variant="outline" onClick={fetchUsageData} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Provider Status */}
        {provider && (
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div>
                    <h3 className="font-medium">{provider.display_name}</h3>
                    <p className="text-sm text-gray-500">Provider: {provider.name}</p>
                  </div>
                  <Badge variant={provider.status === 'active' ? 'success' : 'secondary'}>
                    {provider.status}
                  </Badge>
                  <Badge variant={provider.is_enabled ? 'default' : 'secondary'}>
                    {provider.is_enabled ? 'Enabled' : 'Disabled'}
                  </Badge>
                </div>
                {usageData && (
                  <div className="flex items-center gap-2">
                    {usageData.is_within_limits ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-5 w-5 text-red-500" />
                    )}
                    <span className="text-sm">
                      {usageData.is_within_limits ? 'Within Limits' : 'Exceeding Limits'}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Usage Summary */}
        {usageData && (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(usageData.totals.requests)}</div>
                <p className="text-xs text-muted-foreground">
                  Last {days} days
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Tokens</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(usageData.totals.tokens)}</div>
                <p className="text-xs text-muted-foreground">
                  Processed tokens
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatDuration(usageData.totals.avg_response_time)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Average latency
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {usageData.totals.requests > 0 
                    ? ((usageData.totals.errors / usageData.totals.requests) * 100).toFixed(1)
                    : 0}%
                </div>
                <p className="text-xs text-muted-foreground">
                  {formatNumber(usageData.totals.errors)} errors
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Rate Limits */}
        {usageData?.limits && (
          <Card>
            <CardHeader>
              <CardTitle>Rate Limits</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {usageData.limits.requests_per_minute && (
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Requests per Minute</span>
                    <span>Limit: {formatNumber(usageData.limits.requests_per_minute)}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: '0%' }}
                    ></div>
                  </div>
                </div>
              )}

              {usageData.limits.tokens_per_minute && (
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Tokens per Minute</span>
                    <span>Limit: {formatNumber(usageData.limits.tokens_per_minute)}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-600 h-2 rounded-full" 
                      style={{ width: '0%' }}
                    ></div>
                  </div>
                </div>
              )}

              {usageData.limits.requests_per_day && (
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Requests per Day</span>
                    <span>Limit: {formatNumber(usageData.limits.requests_per_day)}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-yellow-600 h-2 rounded-full" 
                      style={{ width: '0%' }}
                    ></div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Daily Usage Chart */}
        {usageData && (
          <Card>
            <CardHeader>
              <CardTitle>Daily Usage Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(usageData.daily_stats).map(([date, stats]) => (
                  <div key={date} className="flex items-center justify-between p-3 border rounded">
                    <div>
                      <div className="font-medium">{new Date(date).toLocaleDateString()}</div>
                      <div className="text-sm text-gray-500">
                        {formatDuration(stats.avg_response_time)} avg response
                      </div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div className="text-center">
                        <div className="font-medium">{formatNumber(stats.requests)}</div>
                        <div className="text-gray-500">Requests</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{formatNumber(stats.tokens)}</div>
                        <div className="text-gray-500">Tokens</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium text-red-600">{stats.errors}</div>
                        <div className="text-gray-500">Errors</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
