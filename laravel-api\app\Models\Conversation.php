<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Conversation extends Model
{
    protected $fillable = [
        'session_id',
        'user_id',
        'title',
        'context',
        'last_activity_at',
    ];

    protected $casts = [
        'context' => 'array',
        'last_activity_at' => 'datetime',
    ];

    /**
     * Get the user that owns this conversation
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get all messages in this conversation
     */
    public function messages(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Message::class);
    }

    /**
     * Scope for recent conversations
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('last_activity_at', '>=', now()->subDays($days));
    }

    /**
     * Scope for conversations by user
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Get the last message in the conversation
     */
    public function getLastMessageAttribute(): ?Message
    {
        return $this->messages()->latest()->first();
    }

    /**
     * Get message count
     */
    public function getMessageCountAttribute(): int
    {
        return $this->messages()->count();
    }

    /**
     * Get conversation duration
     */
    public function getDurationAttribute(): int
    {
        $firstMessage = $this->messages()->oldest()->first();
        $lastMessage = $this->messages()->latest()->first();
        
        if (!$firstMessage || !$lastMessage) {
            return 0;
        }
        
        return $lastMessage->created_at->diffInMinutes($firstMessage->created_at);
    }

    /**
     * Update last activity timestamp
     */
    public function touch($attribute = null)
    {
        $this->last_activity_at = now();
        return parent::touch($attribute);
    }
}
