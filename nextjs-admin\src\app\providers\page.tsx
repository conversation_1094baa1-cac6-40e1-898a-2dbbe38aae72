'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { useApi } from '@/contexts/AuthContext'
import {
  Settings,
  TestTube,
  Eye,
  EyeOff,
  CheckCircle,
  XCircle,
  AlertCircle,
  Activity,
  Zap,
  Clock,
  TrendingUp,
  RefreshCw,
  Plus
} from 'lucide-react'

interface Provider {
  id: number
  name: string
  display_name: string
  description: string
  is_enabled: boolean
  is_primary: boolean
  priority: number
  capabilities: any
  limits: any
  status: 'active' | 'inactive' | 'error'
  status_message: string | null
  last_tested_at: string | null
  usage_stats: any
  is_configured: boolean
  created_at: string
  updated_at: string
}

export default function ProvidersPage() {
  const { apiCall } = useApi()
  const [providers, setProviders] = useState<Provider[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null)
  const [showConfiguration, setShowConfiguration] = useState(false)
  const [testingProvider, setTestingProvider] = useState<number | null>(null)

  const fetchProviders = async () => {
    setIsLoading(true)
    try {
      const response = await apiCall('/providers')
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setProviders(data.data)
        }
      }
    } catch (error) {
      console.error('Failed to fetch providers:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const initializeDefaults = async () => {
    try {
      const response = await apiCall('/providers/initialize-defaults', {
        method: 'POST'
      })
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          fetchProviders() // Refresh the list
        }
      }
    } catch (error) {
      console.error('Failed to initialize providers:', error)
    }
  }

  const testProvider = async (provider: Provider) => {
    setTestingProvider(provider.id)
    try {
      const response = await apiCall(`/providers/${provider.id}/test`, {
        method: 'POST'
      })
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          // Update provider status in the list
          setProviders(prev => prev.map(p =>
            p.id === provider.id
              ? { ...p, status: data.data.status, status_message: data.data.status_message, last_tested_at: data.data.last_tested_at }
              : p
          ))
        }
      }
    } catch (error) {
      console.error('Failed to test provider:', error)
    } finally {
      setTestingProvider(null)
    }
  }

  const toggleProvider = async (provider: Provider) => {
    try {
      const response = await apiCall(`/providers/${provider.id}`, {
        method: 'PUT',
        body: JSON.stringify({
          is_enabled: !provider.is_enabled
        })
      })
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setProviders(prev => prev.map(p =>
            p.id === provider.id ? { ...p, is_enabled: !p.is_enabled } : p
          ))
        }
      }
    } catch (error) {
      console.error('Failed to toggle provider:', error)
    }
  }

  const setPrimary = async (provider: Provider) => {
    try {
      const response = await apiCall(`/providers/${provider.id}`, {
        method: 'PUT',
        body: JSON.stringify({
          is_primary: true
        })
      })
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setProviders(prev => prev.map(p => ({
            ...p,
            is_primary: p.id === provider.id
          })))
        }
      }
    } catch (error) {
      console.error('Failed to set primary provider:', error)
    }
  }

  useEffect(() => {
    fetchProviders()
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error': return <XCircle className="h-4 w-4 text-red-500" />
      default: return <AlertCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success'
      case 'error': return 'destructive'
      default: return 'secondary'
    }
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never'
    return new Date(dateString).toLocaleString()
  }

  const getTotalUsage = (usageStats: any) => {
    if (!usageStats) return { requests: 0, tokens: 0, errors: 0 }

    return Object.values(usageStats).reduce((total: any, day: any) => ({
      requests: total.requests + (day.requests || 0),
      tokens: total.tokens + (day.tokens || 0),
      errors: total.errors + (day.errors || 0)
    }), { requests: 0, tokens: 0, errors: 0 })
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="space-y-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">AI Providers</h1>
            <p className="text-gray-600">
              Configure and manage your AI provider connections
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={fetchProviders}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            {providers.length === 0 && (
              <Button onClick={initializeDefaults}>
                <Plus className="h-4 w-4 mr-2" />
                Initialize Providers
              </Button>
            )}
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid gap-6 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Providers</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{providers.length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Providers</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {providers.filter(p => p.status === 'active' && p.is_enabled).length}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Configured</CardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {providers.filter(p => p.is_configured).length}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {providers.reduce((total, p) => total + getTotalUsage(p.usage_stats).requests, 0)}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Providers Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {providers.map((provider) => {
            const usage = getTotalUsage(provider.usage_stats)
            return (
              <Card key={provider.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg flex items-center gap-2">
                        {provider.display_name}
                        {provider.is_primary && (
                          <Badge variant="default" className="text-xs">Primary</Badge>
                        )}
                      </CardTitle>
                      <p className="text-sm text-gray-600 mt-1">
                        {provider.description}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(provider.status)}
                      <Badge variant={getStatusColor(provider.status)}>
                        {provider.status}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Configuration Status */}
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Configured:</span>
                    <Badge variant={provider.is_configured ? 'success' : 'secondary'}>
                      {provider.is_configured ? 'Yes' : 'No'}
                    </Badge>
                  </div>

                  {/* Usage Stats */}
                  <div className="grid grid-cols-3 gap-2 text-sm">
                    <div className="text-center">
                      <div className="font-medium">{usage.requests}</div>
                      <div className="text-gray-500 text-xs">Requests</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium">{usage.tokens.toLocaleString()}</div>
                      <div className="text-gray-500 text-xs">Tokens</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-red-600">{usage.errors}</div>
                      <div className="text-gray-500 text-xs">Errors</div>
                    </div>
                  </div>

                  {/* Last Tested */}
                  <div className="text-xs text-gray-500">
                    Last tested: {formatDate(provider.last_tested_at)}
                  </div>

                  {/* Capabilities */}
                  <div className="flex flex-wrap gap-1">
                    {Object.entries(provider.capabilities || {}).map(([key, value]) =>
                      value && (
                        <Badge key={key} variant="outline" className="text-xs">
                          {key.replace('_', ' ')}
                        </Badge>
                      )
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex flex-wrap gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        setSelectedProvider(provider)
                        setShowConfiguration(true)
                      }}
                    >
                      <Settings className="h-3 w-3 mr-1" />
                      Configure
                    </Button>

                    {provider.is_configured && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => testProvider(provider)}
                        disabled={testingProvider === provider.id}
                      >
                        {testingProvider === provider.id ? (
                          <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                        ) : (
                          <TestTube className="h-3 w-3 mr-1" />
                        )}
                        Test
                      </Button>
                    )}

                    <Button
                      size="sm"
                      variant={provider.is_enabled ? "default" : "outline"}
                      onClick={() => toggleProvider(provider)}
                    >
                      {provider.is_enabled ? 'Enabled' : 'Enable'}
                    </Button>

                    {provider.is_enabled && !provider.is_primary && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setPrimary(provider)}
                      >
                        Set Primary
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {providers.length === 0 && !isLoading && (
          <Card>
            <CardContent className="text-center py-12">
              <div className="text-gray-500 mb-4">
                <Settings className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg">No AI providers configured</p>
                <p className="text-sm">Initialize default providers to get started</p>
              </div>
              <Button onClick={initializeDefaults}>
                <Plus className="h-4 w-4 mr-2" />
                Initialize Default Providers
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Configuration Modal */}
        {showConfiguration && selectedProvider && (
          <ProviderConfigurationModal
            provider={selectedProvider}
            onClose={() => {
              setShowConfiguration(false)
              setSelectedProvider(null)
            }}
            onSave={() => {
              fetchProviders()
              setShowConfiguration(false)
              setSelectedProvider(null)
            }}
          />
        )}
      </div>
    </DashboardLayout>
  )
}

// Configuration Modal Component
function ProviderConfigurationModal({ provider, onClose, onSave }: {
  provider: Provider
  onClose: () => void
  onSave: () => void
}) {
  const { apiCall } = useApi()
  const [isLoading, setIsLoading] = useState(false)
  const [showApiKey, setShowApiKey] = useState(false)
  const [formData, setFormData] = useState({
    api_key: '',
    base_url: '',
    default_model: '',
    max_tokens: 4096,
    temperature: 0.7,
    organization_id: '',
    requests_per_minute: 0,
    tokens_per_minute: 0,
    requests_per_day: 0
  })
  const [errors, setErrors] = useState<any>({})

  useEffect(() => {
    // Fetch current configuration
    const fetchConfiguration = async () => {
      try {
        const response = await apiCall(`/providers/${provider.id}`)
        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            const config = data.data.configuration || {}
            const limits = data.data.limits || {}
            setFormData({
              api_key: config.api_key || '',
              base_url: config.base_url || '',
              default_model: config.default_model || '',
              max_tokens: config.max_tokens || 4096,
              temperature: config.temperature || 0.7,
              organization_id: config.organization_id || '',
              requests_per_minute: limits.requests_per_minute || 0,
              tokens_per_minute: limits.tokens_per_minute || 0,
              requests_per_day: limits.requests_per_day || 0
            })
          }
        }
      } catch (error) {
        console.error('Failed to fetch configuration:', error)
      }
    }

    fetchConfiguration()
  }, [provider.id])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setErrors({})

    try {
      const payload = {
        name: provider.name,
        configuration: {
          api_key: formData.api_key,
          base_url: formData.base_url,
          default_model: formData.default_model,
          max_tokens: formData.max_tokens,
          temperature: formData.temperature,
          ...(formData.organization_id && { organization_id: formData.organization_id })
        },
        limits: {
          requests_per_minute: formData.requests_per_minute,
          tokens_per_minute: formData.tokens_per_minute,
          requests_per_day: formData.requests_per_day
        }
      }

      const response = await apiCall('/providers', {
        method: 'POST',
        body: JSON.stringify(payload)
      })

      const data = await response.json()

      if (data.success) {
        onSave()
      } else {
        setErrors(data.errors || { general: data.error || 'Configuration failed' })
      }
    } catch (error) {
      setErrors({ general: 'Network error. Please try again.' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }))

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev: any) => ({ ...prev, [name]: '' }))
    }
  }

  const getModelOptions = (providerName: string) => {
    const models: Record<string, string[]> = {
      openai: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo', 'gpt-3.5-turbo-16k'],
      anthropic: ['claude-3-5-sonnet-20241022', 'claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
      groq: ['llama-3.1-70b-versatile', 'llama-3.1-8b-instant', 'mixtral-8x7b-32768', 'gemma-7b-it'],
      google: ['gemini-pro', 'gemini-pro-vision', 'gemini-1.5-pro', 'gemini-1.5-flash'],
      openrouter: ['anthropic/claude-3.5-sonnet', 'openai/gpt-4', 'meta-llama/llama-3.1-70b-instruct']
    }
    return models[providerName] || []
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <h3 className="text-lg font-semibold mb-4">Configure {provider.display_name}</h3>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* General Error */}
          {errors.general && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{errors.general}</p>
            </div>
          )}

          <div className="grid md:grid-cols-2 gap-6">
            {/* API Configuration */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">API Configuration</h4>

              {/* API Key */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  API Key *
                </label>
                <div className="relative">
                  <input
                    type={showApiKey ? 'text' : 'password'}
                    name="api_key"
                    value={formData.api_key}
                    onChange={handleInputChange}
                    className="w-full pr-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter your API key"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowApiKey(!showApiKey)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
                {errors['configuration.api_key'] && (
                  <p className="text-sm text-red-600 mt-1">{errors['configuration.api_key'][0]}</p>
                )}
              </div>

              {/* Base URL */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Base URL
                </label>
                <input
                  type="url"
                  name="base_url"
                  value={formData.base_url}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="https://api.example.com"
                />
              </div>

              {/* Organization ID (for OpenAI) */}
              {provider.name === 'openai' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Organization ID (Optional)
                  </label>
                  <input
                    type="text"
                    name="organization_id"
                    value={formData.organization_id}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="org-xxxxxxxxxxxxxxxxxxxxxxxx"
                  />
                </div>
              )}

              {/* Default Model */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Default Model
                </label>
                <select
                  name="default_model"
                  value={formData.default_model}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select a model</option>
                  {getModelOptions(provider.name).map(model => (
                    <option key={model} value={model}>{model}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Model Parameters & Limits */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">Model Parameters</h4>

              {/* Max Tokens */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Tokens
                </label>
                <input
                  type="number"
                  name="max_tokens"
                  value={formData.max_tokens}
                  onChange={handleInputChange}
                  min="1"
                  max="32000"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Temperature */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Temperature
                </label>
                <input
                  type="number"
                  name="temperature"
                  value={formData.temperature}
                  onChange={handleInputChange}
                  min="0"
                  max="2"
                  step="0.1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <h4 className="font-medium text-gray-900 mt-6">Rate Limits</h4>

              {/* Requests per minute */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Requests per Minute
                </label>
                <input
                  type="number"
                  name="requests_per_minute"
                  value={formData.requests_per_minute}
                  onChange={handleInputChange}
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Tokens per minute */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tokens per Minute
                </label>
                <input
                  type="number"
                  name="tokens_per_minute"
                  value={formData.tokens_per_minute}
                  onChange={handleInputChange}
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Requests per day */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Requests per Day
                </label>
                <input
                  type="number"
                  name="requests_per_day"
                  value={formData.requests_per_day}
                  onChange={handleInputChange}
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Capabilities Display */}
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Capabilities</h4>
            <div className="flex flex-wrap gap-2">
              {Object.entries(provider.capabilities || {}).map(([key, value]) => (
                <Badge key={key} variant={value ? 'default' : 'secondary'}>
                  {key.replace('_', ' ')} {value ? '✓' : '✗'}
                </Badge>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Configuration'
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
