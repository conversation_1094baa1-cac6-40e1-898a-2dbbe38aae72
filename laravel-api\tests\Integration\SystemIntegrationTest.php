<?php

namespace Tests\Integration;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Performance\PerformanceMonitor;
use Illuminate\Support\Facades\Artisan;

class SystemIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected PerformanceMonitor $performanceMonitor;

    protected function setUp(): void
    {
        parent::setUp();
        $this->performanceMonitor = app(PerformanceMonitor::class);
    }

    /**
     * Test complete system integration
     */
    public function test_complete_system_integration()
    {
        $this->performanceMonitor->startTimer('system_integration_test');

        // Test 1: System Health Check
        $healthResponse = $this->getJson('/api/health');
        $healthResponse->assertStatus(200);

        // Test 2: System Status
        $statusResponse = $this->getJson('/api/test/system-status');
        $statusResponse->assertStatus(200)
                      ->assertJsonStructure([
                          'success',
                          'system_status',
                          'agents'
                      ]);

        // Test 3: MCP Pipeline Processing
        $mcpResponse = $this->postJson('/api/mcp/process', [
            'message' => 'Integration test: What is artificial intelligence?',
            'context' => []
        ]);
        $mcpResponse->assertStatus(200)
                   ->assertJsonStructure([
                       'success',
                       'session_id',
                       'response'
                   ]);

        // Test 4: Individual Agent Testing
        $agents = ['intent', 'retriever', 'llm', 'memory'];
        foreach ($agents as $agent) {
            $agentResponse = $this->getJson("/api/test/agent/{$agent}");
            $agentResponse->assertStatus(200);
        }

        // Test 5: Monitoring Endpoints
        $monitoringResponse = $this->getJson('/api/monitoring/health');
        $monitoringResponse->assertStatus(200);

        $metricsResponse = $this->getJson('/api/monitoring/metrics');
        $metricsResponse->assertStatus(200);

        $this->performanceMonitor->endTimer('system_integration_test');

        $this->assertTrue(true, 'Complete system integration test passed');
    }

    /**
     * Test system under various load conditions
     */
    public function test_system_load_conditions()
    {
        $loadTests = [
            ['concurrent_requests' => 5, 'expected_success_rate' => 90],
            ['concurrent_requests' => 10, 'expected_success_rate' => 80],
            ['concurrent_requests' => 15, 'expected_success_rate' => 70]
        ];

        foreach ($loadTests as $test) {
            $results = $this->runLoadTest($test['concurrent_requests']);
            
            $this->assertGreaterThanOrEqual(
                $test['expected_success_rate'],
                $results['success_rate'],
                "Load test with {$test['concurrent_requests']} requests failed"
            );
        }
    }

    /**
     * Test error recovery and resilience
     */
    public function test_error_recovery_resilience()
    {
        // Test invalid inputs
        $invalidInputs = [
            ['message' => ''],
            ['message' => str_repeat('x', 100000)],
            ['invalid_field' => 'test']
        ];

        foreach ($invalidInputs as $input) {
            $response = $this->postJson('/api/mcp/process', $input);
            // Should either succeed with graceful handling or return proper error
            $this->assertTrue(
                $response->status() === 200 || $response->status() === 422,
                'System should handle invalid inputs gracefully'
            );
        }

        // Test system recovery after errors
        $normalResponse = $this->postJson('/api/mcp/process', [
            'message' => 'Normal message after error tests',
            'context' => []
        ]);
        $normalResponse->assertStatus(200);
    }

    /**
     * Test security and content filtering
     */
    public function test_security_content_filtering()
    {
        $securityTests = [
            [
                'message' => 'This is a normal safe message',
                'should_pass' => true
            ],
            [
                'message' => 'My <NAME_EMAIL>',
                'should_pass' => true, // Should be filtered but not blocked
                'expect_filtering' => true
            ],
            [
                'message' => '<script>alert("xss")</script>',
                'should_pass' => true, // Should be sanitized
                'expect_filtering' => true
            ]
        ];

        foreach ($securityTests as $test) {
            $response = $this->postJson('/api/mcp/process', [
                'message' => $test['message'],
                'context' => []
            ]);

            if ($test['should_pass']) {
                $response->assertStatus(200);
                
                if (isset($test['expect_filtering'])) {
                    $responseData = $response->json();
                    $this->assertArrayHasKey('response', $responseData);
                    // Verify guardrail processing occurred
                    if (isset($responseData['response']['guardrail_report'])) {
                        $this->assertIsArray($responseData['response']['guardrail_report']);
                    }
                }
            } else {
                $response->assertStatus(422);
            }
        }
    }

    /**
     * Test data persistence and consistency
     */
    public function test_data_persistence_consistency()
    {
        // Create a conversation
        $response1 = $this->postJson('/api/mcp/process', [
            'message' => 'Start of conversation test',
            'context' => []
        ]);
        $response1->assertStatus(200);
        
        $sessionId = $response1->json('session_id');
        $this->assertNotEmpty($sessionId);

        // Continue the conversation
        $response2 = $this->postJson('/api/mcp/process', [
            'message' => 'Continue conversation test',
            'session_id' => $sessionId,
            'context' => []
        ]);
        $response2->assertStatus(200);

        // Verify session consistency
        $this->assertEquals($sessionId, $response2->json('session_id'));

        // Test conversation history retrieval
        $historyResponse = $this->getJson("/api/mcp/history/{$sessionId}");
        $historyResponse->assertStatus(200);
    }

    /**
     * Test API rate limiting and throttling
     */
    public function test_api_rate_limiting()
    {
        $requestCount = 20;
        $successCount = 0;
        $rateLimitedCount = 0;

        for ($i = 0; $i < $requestCount; $i++) {
            $response = $this->postJson('/api/mcp/process', [
                'message' => "Rate limit test {$i}",
                'context' => []
            ]);

            if ($response->status() === 200) {
                $successCount++;
            } elseif ($response->status() === 429) {
                $rateLimitedCount++;
            }
        }

        // Should handle requests appropriately
        $this->assertGreaterThan(0, $successCount, 'Some requests should succeed');
        
        // If rate limiting is implemented, some requests might be limited
        $totalHandled = $successCount + $rateLimitedCount;
        $this->assertEquals($requestCount, $totalHandled, 'All requests should be handled');
    }

    /**
     * Test monitoring and metrics collection
     */
    public function test_monitoring_metrics_collection()
    {
        // Generate some activity
        for ($i = 0; $i < 5; $i++) {
            $this->postJson('/api/mcp/process', [
                'message' => "Metrics test {$i}",
                'context' => []
            ]);
        }

        // Test metrics endpoints
        $metricsResponse = $this->getJson('/api/monitoring/metrics');
        $metricsResponse->assertStatus(200)
                       ->assertJsonStructure([
                           'success',
                           'data' => [
                               'performance',
                               'health',
                               'system'
                           ]
                       ]);

        $performanceResponse = $this->getJson('/api/monitoring/performance');
        $performanceResponse->assertStatus(200);

        $healthResponse = $this->getJson('/api/monitoring/health');
        $healthResponse->assertStatus(200)
                      ->assertJsonStructure([
                          'success',
                          'data' => [
                              'score',
                              'status'
                          ]
                      ]);
    }

    protected function runLoadTest(int $concurrentRequests): array
    {
        $startTime = microtime(true);
        $successCount = 0;
        $errorCount = 0;

        for ($i = 0; $i < $concurrentRequests; $i++) {
            try {
                $response = $this->postJson('/api/mcp/process', [
                    'message' => "Load test request {$i}",
                    'context' => []
                ]);

                if ($response->status() === 200) {
                    $successCount++;
                } else {
                    $errorCount++;
                }
            } catch (\Exception $e) {
                $errorCount++;
            }
        }

        $totalTime = microtime(true) - $startTime;
        $successRate = ($successCount / $concurrentRequests) * 100;

        return [
            'concurrent_requests' => $concurrentRequests,
            'successful_requests' => $successCount,
            'failed_requests' => $errorCount,
            'success_rate' => $successRate,
            'total_time' => $totalTime,
            'requests_per_second' => $concurrentRequests / $totalTime
        ];
    }
}
