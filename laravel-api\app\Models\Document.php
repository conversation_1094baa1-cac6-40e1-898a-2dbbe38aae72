<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Document extends Model
{
    protected $fillable = [
        'knowledge_base_id',
        'title',
        'content',
        'source_type',
        'source_path',
        'mime_type',
        'chunk_count',
        'metadata',
    ];

    protected $casts = [
        'metadata' => 'array',
        'chunk_count' => 'integer',
    ];

    /**
     * Get the knowledge base this document belongs to
     */
    public function knowledgeBase(): BelongsTo
    {
        return $this->belongsTo(KnowledgeBase::class);
    }

    /**
     * Get all chunks for this document
     */
    public function chunks(): HasMany
    {
        return $this->hasMany(DocumentChunk::class);
    }

    /**
     * Scope by source type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('source_type', $type);
    }

    /**
     * Get document size in bytes
     */
    public function getSizeAttribute(): int
    {
        return strlen($this->content);
    }

    /**
     * Get formatted file size
     */
    public function getFormattedSizeAttribute(): string
    {
        $bytes = $this->size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Update chunk count
     */
    public function updateChunkCount(): void
    {
        $this->update(['chunk_count' => $this->chunks()->count()]);
    }
}
