<?php

declare(strict_types=1);

namespace App\Services\MCP;

use App\Contracts\MCPAgentInterface;
use App\Contracts\MCPOrchestratorInterface;
use App\Models\AgentCommunication;
use App\Models\AgentExecution;
use Illuminate\Support\Facades\Log;

class MCPOrchestrator implements MCPOrchestratorInterface
{
    protected array $agents = [];
    protected array $primaryAgents = [];
    protected array $secondaryAgents = [];

    public function __construct()
    {
        $this->loadAgents();
    }

    /**
     * Load and register all active agents
     */
    protected function loadAgents(): void
    {
        // This will be populated with actual agent instances
        // For now, we'll register them manually in the service provider
    }

    public function processRequest(array $input, string $sessionId): array
    {
        Log::info('MCP Orchestrator processing request', [
            'session_id' => $sessionId,
            'input_keys' => array_keys($input)
        ]);

        try {
            // Execute primary agents in sequence
            $result = $this->executeAgentPipeline($this->primaryAgents, $input, $sessionId);
            
            // Execute secondary agents if needed
            if (!empty($this->secondaryAgents)) {
                $result = $this->executeAgentPipeline($this->secondaryAgents, $result, $sessionId);
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('MCP Orchestrator processing failed', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    public function registerAgent(MCPAgentInterface $agent): void
    {
        $this->agents[$agent->getName()] = $agent;
        
        if ($agent->getType() === 'primary') {
            $this->primaryAgents[] = $agent;
        } else {
            $this->secondaryAgents[] = $agent;
        }

        // Sort agents by execution order
        usort($this->primaryAgents, fn($a, $b) => $a->getExecutionOrder() <=> $b->getExecutionOrder());
        usort($this->secondaryAgents, fn($a, $b) => $a->getExecutionOrder() <=> $b->getExecutionOrder());
    }

    public function getAgents(): array
    {
        return $this->agents;
    }

    public function getAgent(string $name): ?MCPAgentInterface
    {
        return $this->agents[$name] ?? null;
    }

    public function executeAgentPipeline(array $agents, array $input, string $sessionId): array
    {
        $currentData = $input;

        foreach ($agents as $agent) {
            if (!$agent->isActive()) {
                continue;
            }

            Log::debug("Executing agent: {$agent->getName()}", [
                'session_id' => $sessionId
            ]);

            $currentData = $agent->execute($currentData, $sessionId);
        }

        return $currentData;
    }

    public function sendMessage(string $fromAgent, string $toAgent, array $payload, string $sessionId): void
    {
        $fromAgentModel = $this->getAgent($fromAgent);
        $toAgentModel = $this->getAgent($toAgent);

        if (!$fromAgentModel || !$toAgentModel) {
            throw new \InvalidArgumentException("Invalid agent names provided");
        }

        AgentCommunication::create([
            'from_agent_id' => $fromAgentModel->getName(),
            'to_agent_id' => $toAgentModel->getName(),
            'session_id' => $sessionId,
            'message_type' => 'request',
            'payload' => $payload
        ]);

        Log::debug("Message sent between agents", [
            'from' => $fromAgent,
            'to' => $toAgent,
            'session_id' => $sessionId
        ]);
    }

    public function getExecutionHistory(string $sessionId): array
    {
        return AgentExecution::where('session_id', $sessionId)
            ->with('agent')
            ->orderBy('created_at')
            ->get()
            ->toArray();
    }
}
