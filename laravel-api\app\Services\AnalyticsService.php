<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\AnalyticsEvent;
use App\Models\User;
use App\Models\Document;
use App\Models\Workflow;
use App\Models\WorkflowExecution;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class AnalyticsService
{
    /**
     * Track an analytics event
     */
    public function trackEvent(
        string $eventType,
        string $eventName,
        ?string $entityType = null,
        ?string $entityId = null,
        array $properties = [],
        array $metadata = [],
        ?int $duration = null
    ): AnalyticsEvent {
        return AnalyticsEvent::create([
            'event_type' => $eventType,
            'event_name' => $eventName,
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'properties' => $properties,
            'metadata' => $metadata,
            'user_agent' => request()->userAgent(),
            'ip_address' => request()->ip(),
            'session_id' => session()->getId(),
            'duration_ms' => $duration,
            'occurred_at' => now()
        ]);
    }

    /**
     * Get dashboard analytics
     */
    public function getDashboardAnalytics(int $days = 30): array
    {
        $cacheKey = "dashboard_analytics_{$days}";
        
        return Cache::remember($cacheKey, 300, function () use ($days) {
            $startDate = now()->subDays($days);
            
            return [
                'overview' => $this->getOverviewMetrics($startDate),
                'user_activity' => $this->getUserActivityMetrics($startDate),
                'document_metrics' => $this->getDocumentMetrics($startDate),
                'workflow_metrics' => $this->getWorkflowMetrics($startDate),
                'performance_metrics' => $this->getPerformanceMetrics($startDate),
                'error_metrics' => $this->getErrorMetrics($startDate),
                'trends' => $this->getTrendData($startDate)
            ];
        });
    }

    /**
     * Get overview metrics
     */
    protected function getOverviewMetrics($startDate): array
    {
        $totalUsers = User::count();
        $activeUsers = User::where('last_login_at', '>=', $startDate)->count();
        $totalDocuments = Document::count();
        $totalWorkflows = Workflow::count();
        $activeWorkflows = Workflow::where('status', 'active')->count();
        
        $totalExecutions = WorkflowExecution::where('started_at', '>=', $startDate)->count();
        $successfulExecutions = WorkflowExecution::where('started_at', '>=', $startDate)
            ->where('status', 'completed')->count();
        
        return [
            'total_users' => $totalUsers,
            'active_users' => $activeUsers,
            'user_growth_rate' => $this->calculateGrowthRate('users', $startDate),
            'total_documents' => $totalDocuments,
            'document_growth_rate' => $this->calculateGrowthRate('documents', $startDate),
            'total_workflows' => $totalWorkflows,
            'active_workflows' => $activeWorkflows,
            'workflow_success_rate' => $totalExecutions > 0 ? ($successfulExecutions / $totalExecutions) * 100 : 0,
            'total_executions' => $totalExecutions,
            'avg_execution_time' => WorkflowExecution::where('started_at', '>=', $startDate)
                ->where('status', 'completed')
                ->avg('duration_ms') ?? 0
        ];
    }

    /**
     * Get user activity metrics
     */
    protected function getUserActivityMetrics($startDate): array
    {
        $dailyActiveUsers = AnalyticsEvent::where('occurred_at', '>=', $startDate)
            ->where('event_type', 'user_action')
            ->select(DB::raw('DATE(occurred_at) as date'), DB::raw('COUNT(DISTINCT session_id) as users'))
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->pluck('users', 'date')
            ->toArray();

        $topActions = AnalyticsEvent::where('occurred_at', '>=', $startDate)
            ->where('event_type', 'user_action')
            ->select('event_name', DB::raw('COUNT(*) as count'))
            ->groupBy('event_name')
            ->orderByDesc('count')
            ->limit(10)
            ->get()
            ->toArray();

        $sessionDurations = AnalyticsEvent::where('occurred_at', '>=', $startDate)
            ->whereNotNull('duration_ms')
            ->select(DB::raw('AVG(duration_ms) as avg_duration'), DB::raw('DATE(occurred_at) as date'))
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->pluck('avg_duration', 'date')
            ->toArray();

        return [
            'daily_active_users' => $dailyActiveUsers,
            'top_actions' => $topActions,
            'avg_session_duration' => array_sum($sessionDurations) / max(count($sessionDurations), 1),
            'session_trends' => $sessionDurations
        ];
    }

    /**
     * Get document metrics
     */
    protected function getDocumentMetrics($startDate): array
    {
        $uploadTrends = Document::where('created_at', '>=', $startDate)
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as uploads'))
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->pluck('uploads', 'date')
            ->toArray();

        $processingStats = Document::select('status', DB::raw('COUNT(*) as count'))
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status')
            ->toArray();

        $fileTypeStats = Document::select('file_type', DB::raw('COUNT(*) as count'))
            ->groupBy('file_type')
            ->orderByDesc('count')
            ->get()
            ->toArray();

        $avgProcessingTime = Document::where('created_at', '>=', $startDate)
            ->whereNotNull('processed_at')
            ->select(DB::raw('AVG(TIMESTAMPDIFF(SECOND, created_at, processed_at)) as avg_time'))
            ->value('avg_time') ?? 0;

        return [
            'upload_trends' => $uploadTrends,
            'processing_stats' => $processingStats,
            'file_type_distribution' => $fileTypeStats,
            'avg_processing_time_seconds' => $avgProcessingTime,
            'total_storage_mb' => Document::sum('file_size') / 1048576
        ];
    }

    /**
     * Get workflow metrics
     */
    protected function getWorkflowMetrics($startDate): array
    {
        $executionTrends = WorkflowExecution::where('started_at', '>=', $startDate)
            ->select(DB::raw('DATE(started_at) as date'), DB::raw('COUNT(*) as executions'))
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->pluck('executions', 'date')
            ->toArray();

        $statusDistribution = WorkflowExecution::where('started_at', '>=', $startDate)
            ->select('status', DB::raw('COUNT(*) as count'))
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status')
            ->toArray();

        $topWorkflows = Workflow::withCount(['executions' => function ($query) use ($startDate) {
                $query->where('started_at', '>=', $startDate);
            }])
            ->orderByDesc('executions_count')
            ->limit(10)
            ->get()
            ->map(function ($workflow) {
                return [
                    'id' => $workflow->id,
                    'name' => $workflow->name,
                    'executions' => $workflow->executions_count
                ];
            })
            ->toArray();

        return [
            'execution_trends' => $executionTrends,
            'status_distribution' => $statusDistribution,
            'top_workflows' => $topWorkflows,
            'avg_execution_duration' => WorkflowExecution::where('started_at', '>=', $startDate)
                ->where('status', 'completed')
                ->avg('duration_ms') ?? 0
        ];
    }

    /**
     * Get performance metrics
     */
    protected function getPerformanceMetrics($startDate): array
    {
        $apiResponseTimes = AnalyticsEvent::where('occurred_at', '>=', $startDate)
            ->where('event_type', 'api_call')
            ->whereNotNull('duration_ms')
            ->select(DB::raw('AVG(duration_ms) as avg_time'), DB::raw('DATE(occurred_at) as date'))
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->pluck('avg_time', 'date')
            ->toArray();

        $slowestEndpoints = AnalyticsEvent::where('occurred_at', '>=', $startDate)
            ->where('event_type', 'api_call')
            ->whereNotNull('duration_ms')
            ->select('event_name', DB::raw('AVG(duration_ms) as avg_time'), DB::raw('COUNT(*) as calls'))
            ->groupBy('event_name')
            ->orderByDesc('avg_time')
            ->limit(10)
            ->get()
            ->toArray();

        return [
            'api_response_trends' => $apiResponseTimes,
            'slowest_endpoints' => $slowestEndpoints,
            'avg_response_time' => array_sum($apiResponseTimes) / max(count($apiResponseTimes), 1)
        ];
    }

    /**
     * Get error metrics
     */
    protected function getErrorMetrics($startDate): array
    {
        $errorTrends = AnalyticsEvent::where('occurred_at', '>=', $startDate)
            ->where('event_type', 'error')
            ->select(DB::raw('DATE(occurred_at) as date'), DB::raw('COUNT(*) as errors'))
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->pluck('errors', 'date')
            ->toArray();

        $errorTypes = AnalyticsEvent::where('occurred_at', '>=', $startDate)
            ->where('event_type', 'error')
            ->select('event_name', DB::raw('COUNT(*) as count'))
            ->groupBy('event_name')
            ->orderByDesc('count')
            ->limit(10)
            ->get()
            ->toArray();

        $errorRate = $this->calculateErrorRate($startDate);

        return [
            'error_trends' => $errorTrends,
            'error_types' => $errorTypes,
            'error_rate_percent' => $errorRate
        ];
    }

    /**
     * Get trend data
     */
    protected function getTrendData($startDate): array
    {
        $days = now()->diffInDays($startDate);
        $previousStartDate = $startDate->copy()->subDays($days);
        
        $currentPeriod = $this->getPeriodMetrics($startDate, now());
        $previousPeriod = $this->getPeriodMetrics($previousStartDate, $startDate);
        
        return [
            'user_growth' => $this->calculatePercentageChange(
                $previousPeriod['users'], 
                $currentPeriod['users']
            ),
            'document_growth' => $this->calculatePercentageChange(
                $previousPeriod['documents'], 
                $currentPeriod['documents']
            ),
            'execution_growth' => $this->calculatePercentageChange(
                $previousPeriod['executions'], 
                $currentPeriod['executions']
            ),
            'error_change' => $this->calculatePercentageChange(
                $previousPeriod['errors'], 
                $currentPeriod['errors']
            )
        ];
    }

    /**
     * Calculate growth rate
     */
    protected function calculateGrowthRate(string $entity, $startDate): float
    {
        $model = match($entity) {
            'users' => User::class,
            'documents' => Document::class,
            'workflows' => Workflow::class,
            default => null
        };

        if (!$model) return 0;

        $current = $model::where('created_at', '>=', $startDate)->count();
        $previous = $model::where('created_at', '<', $startDate)->count();

        return $previous > 0 ? (($current - $previous) / $previous) * 100 : 0;
    }

    /**
     * Calculate error rate
     */
    protected function calculateErrorRate($startDate): float
    {
        $totalEvents = AnalyticsEvent::where('occurred_at', '>=', $startDate)->count();
        $errorEvents = AnalyticsEvent::where('occurred_at', '>=', $startDate)
            ->where('event_type', 'error')->count();

        return $totalEvents > 0 ? ($errorEvents / $totalEvents) * 100 : 0;
    }

    /**
     * Get period metrics
     */
    protected function getPeriodMetrics($startDate, $endDate): array
    {
        return [
            'users' => User::whereBetween('created_at', [$startDate, $endDate])->count(),
            'documents' => Document::whereBetween('created_at', [$startDate, $endDate])->count(),
            'executions' => WorkflowExecution::whereBetween('started_at', [$startDate, $endDate])->count(),
            'errors' => AnalyticsEvent::whereBetween('occurred_at', [$startDate, $endDate])
                ->where('event_type', 'error')->count()
        ];
    }

    /**
     * Calculate percentage change
     */
    protected function calculatePercentageChange(int $previous, int $current): float
    {
        if ($previous === 0) {
            return $current > 0 ? 100 : 0;
        }
        
        return (($current - $previous) / $previous) * 100;
    }

    /**
     * Generate custom report
     */
    public function generateCustomReport(array $filters): array
    {
        $query = AnalyticsEvent::query();
        
        if (isset($filters['start_date'])) {
            $query->where('occurred_at', '>=', $filters['start_date']);
        }
        
        if (isset($filters['end_date'])) {
            $query->where('occurred_at', '<=', $filters['end_date']);
        }
        
        if (isset($filters['event_type'])) {
            $query->where('event_type', $filters['event_type']);
        }
        
        if (isset($filters['entity_type'])) {
            $query->where('entity_type', $filters['entity_type']);
        }
        
        return [
            'total_events' => $query->count(),
            'events_by_type' => $query->select('event_type', DB::raw('COUNT(*) as count'))
                ->groupBy('event_type')->get()->toArray(),
            'events_by_day' => $query->select(DB::raw('DATE(occurred_at) as date'), DB::raw('COUNT(*) as count'))
                ->groupBy('date')->orderBy('date')->get()->toArray(),
            'top_events' => $query->select('event_name', DB::raw('COUNT(*) as count'))
                ->groupBy('event_name')->orderByDesc('count')->limit(20)->get()->toArray()
        ];
    }
}
