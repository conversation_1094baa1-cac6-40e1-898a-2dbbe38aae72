<?php

use App\Http\Controllers\Api\MCPController;
use App\Http\Controllers\Api\TestController;
use App\Http\Controllers\Api\MonitoringController;
use App\Http\Controllers\Api\WidgetController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\ProviderController;
use App\Http\Controllers\Api\KnowledgeBaseController;
use App\Http\Controllers\Api\WorkflowController;
use App\Http\Controllers\Api\AnalyticsController;
use App\Http\Controllers\Api\SecurityController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Public authentication routes
Route::prefix('auth')->group(function () {
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/login', [AuthController::class, 'login']);
});

// Protected authentication routes
Route::middleware('auth:sanctum')->prefix('auth')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/me', [AuthController::class, 'me']);
    Route::put('/profile', [AuthController::class, 'updateProfile']);
});

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// MCP API Routes
Route::prefix('mcp')->group(function () {
    Route::post('/process', [MCPController::class, 'processRequest']);
    Route::get('/history/{sessionId}', [MCPController::class, 'getExecutionHistory']);
    Route::get('/agents', [MCPController::class, 'getAgents']);
});

// Test routes
Route::prefix('test')->group(function () {
    Route::get('/mcp-pipeline', [TestController::class, 'testMCPPipeline']);
    Route::get('/full-pipeline', [TestController::class, 'testFullPipeline']);
    Route::get('/agent/{agentName}', [TestController::class, 'testAgent']);
    Route::get('/system-status', [TestController::class, 'getSystemStatus']);
});

// Monitoring routes
Route::prefix('monitoring')->group(function () {
    Route::get('/metrics', [MonitoringController::class, 'getSystemMetrics']);
    Route::get('/performance', [MonitoringController::class, 'getPerformanceStats']);
    Route::get('/health', [MonitoringController::class, 'getHealthScore']);
    Route::get('/agents', [MonitoringController::class, 'getAgentMetrics']);
    Route::get('/errors', [MonitoringController::class, 'getErrorAnalysis']);
    Route::post('/optimize', [MonitoringController::class, 'optimizePerformance']);
});

// Widget routes
Route::prefix('widgets')->group(function () {
    Route::get('/', [WidgetController::class, 'index']);
    Route::post('/', [WidgetController::class, 'store']);
    Route::get('/{widget}', [WidgetController::class, 'show']);
    Route::put('/{widget}', [WidgetController::class, 'update']);
    Route::delete('/{widget}', [WidgetController::class, 'destroy']);
    Route::get('/{widget}/embed-code', [WidgetController::class, 'getEmbedCode']);
    Route::get('/{widget}/preview', [WidgetController::class, 'preview']);
    Route::get('/{widget}/analytics', [WidgetController::class, 'analytics']);
    Route::post('/{widget}/duplicate', [WidgetController::class, 'duplicate']);
});

// Provider routes
Route::prefix('providers')->group(function () {
    Route::get('/', [ProviderController::class, 'index']);
    Route::post('/', [ProviderController::class, 'store']);
    Route::post('/initialize-defaults', [ProviderController::class, 'initializeDefaults']);
    Route::get('/{provider}', [ProviderController::class, 'show']);
    Route::put('/{provider}', [ProviderController::class, 'update']);
    Route::delete('/{provider}', [ProviderController::class, 'destroy']);
    Route::post('/{provider}/test', [ProviderController::class, 'test']);
    Route::get('/{provider}/usage', [ProviderController::class, 'usage']);
});

// Knowledge Base routes
Route::prefix('knowledge-bases')->group(function () {
    Route::get('/', [KnowledgeBaseController::class, 'index']);
    Route::post('/', [KnowledgeBaseController::class, 'store']);
    Route::get('/supported-file-types', [KnowledgeBaseController::class, 'getSupportedFileTypes']);
    Route::post('/test-embedding', [KnowledgeBaseController::class, 'testEmbedding']);
    Route::get('/{knowledgeBase}', [KnowledgeBaseController::class, 'show']);
    Route::put('/{knowledgeBase}', [KnowledgeBaseController::class, 'update']);
    Route::delete('/{knowledgeBase}', [KnowledgeBaseController::class, 'destroy']);
    Route::post('/{knowledgeBase}/upload', [KnowledgeBaseController::class, 'uploadDocument']);
    Route::get('/{knowledgeBase}/documents', [KnowledgeBaseController::class, 'getDocuments']);
    Route::post('/{knowledgeBase}/search', [KnowledgeBaseController::class, 'search']);
    Route::post('/{knowledgeBase}/process-embeddings', [KnowledgeBaseController::class, 'processEmbeddings']);
    Route::post('/{knowledgeBase}/cleanup', [KnowledgeBaseController::class, 'cleanup']);
    Route::post('/{knowledgeBase}/reprocess', [KnowledgeBaseController::class, 'reprocess']);
});

// Workflow routes
Route::prefix('workflows')->group(function () {
    Route::get('/', [WorkflowController::class, 'index']);
    Route::post('/', [WorkflowController::class, 'store']);
    Route::get('/node-types', [WorkflowController::class, 'getNodeTypes']);
    Route::post('/import', [WorkflowController::class, 'import']);
    Route::get('/{workflow}', [WorkflowController::class, 'show']);
    Route::put('/{workflow}', [WorkflowController::class, 'update']);
    Route::delete('/{workflow}', [WorkflowController::class, 'destroy']);
    Route::post('/{workflow}/execute', [WorkflowController::class, 'execute']);
    Route::get('/{workflow}/executions', [WorkflowController::class, 'getExecutions']);
    Route::post('/{workflow}/clone', [WorkflowController::class, 'clone']);
    Route::get('/{workflow}/export', [WorkflowController::class, 'export']);
    Route::post('/{workflow}/validate', [WorkflowController::class, 'validate']);
});

// Analytics routes
Route::prefix('analytics')->group(function () {
    Route::get('/dashboard', [AnalyticsController::class, 'getDashboard']);
    Route::post('/track-event', [AnalyticsController::class, 'trackEvent']);
    Route::post('/generate-report', [AnalyticsController::class, 'generateReport']);
    Route::get('/user-activity', [AnalyticsController::class, 'getUserActivity']);
    Route::get('/performance', [AnalyticsController::class, 'getPerformanceMetrics']);
    Route::get('/errors', [AnalyticsController::class, 'getErrorAnalytics']);
    Route::get('/workflows', [AnalyticsController::class, 'getWorkflowAnalytics']);
    Route::get('/documents', [AnalyticsController::class, 'getDocumentAnalytics']);
    Route::get('/real-time', [AnalyticsController::class, 'getRealTimeMetrics']);
    Route::post('/export', [AnalyticsController::class, 'exportData']);
});

// Security routes
Route::prefix('security')->group(function () {
    Route::post('/validate-password', [SecurityController::class, 'validatePassword']);
    Route::post('/scan-content', [SecurityController::class, 'scanContent']);
    Route::post('/check-suspicious-login', [SecurityController::class, 'checkSuspiciousLogin']);
    Route::post('/generate-api-key', [SecurityController::class, 'generateApiKey']);
    Route::post('/validate-api-key', [SecurityController::class, 'validateApiKey']);
    Route::get('/dashboard', [SecurityController::class, 'getSecurityDashboard']);
    Route::post('/record-failed-attempt', [SecurityController::class, 'recordFailedAttempt']);
    Route::post('/clear-failed-attempts', [SecurityController::class, 'clearFailedAttempts']);
    Route::post('/check-rate-limit', [SecurityController::class, 'checkRateLimit']);
});

// MCP routes
Route::prefix('mcp')->group(function () {
    Route::post('/process', [MCPController::class, 'processRequest']);
    Route::get('/agents', [MCPController::class, 'getAgents']);
    Route::get('/history/{sessionId}', [MCPController::class, 'getExecutionHistory']);
});

// Health check
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now()->toISOString(),
        'tenant' => tenant('id') ?? 'central',
    ]);
});
