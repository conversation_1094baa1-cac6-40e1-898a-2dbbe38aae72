'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useApi } from '@/contexts/AuthContext'
import { formatDuration, formatBytes } from '@/lib/utils'
import {
  Activity,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Zap,
  Database,
  Cpu,
  HardDrive
} from 'lucide-react'

interface SystemMetrics {
  performance: {
    total_requests: number
    average_response_time: number
    slowest_operations: Array<{
      operation: string
      avg_time: number
      max_time: number
      count: number
    }>
    error_rate: number
    memory_usage: {
      current: number
      peak: number
      limit: string
    }
    database_performance: {
      total_queries: number
      avg_query_time: number
      slowest_query: number
    }
  }
  health: {
    score: number
    status: string
    issues: string[]
    recommendations: string[]
  }
  system: {
    uptime: number
    memory: {
      current: number
      peak: number
      limit: string
    }
    cpu: {
      load_average: number[]
    }
    disk: {
      free_space: number
      total_space: number
    }
  }
}

export default function AnalyticsPage() {
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())
  const [autoRefresh, setAutoRefresh] = useState(true)

  const fetchMetrics = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('http://localhost:8000/api/monitoring/metrics')
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setMetrics(data.data)
        }
      }
    } catch (error) {
      console.error('Failed to fetch metrics:', error)
    } finally {
      setIsLoading(false)
      setLastUpdated(new Date())
    }
  }

  const optimizeSystem = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/monitoring/optimize', {
        method: 'POST'
      })
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          // Refresh metrics after optimization
          setTimeout(fetchMetrics, 2000)
        }
      }
    } catch (error) {
      console.error('Failed to optimize system:', error)
    }
  }

  useEffect(() => {
    fetchMetrics()

    if (autoRefresh) {
      const interval = setInterval(fetchMetrics, 30000) // Refresh every 30 seconds
      return () => clearInterval(interval)
    }
  }, [autoRefresh])

  const getHealthColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 75) return 'text-blue-600'
    if (score >= 60) return 'text-yellow-600'
    if (score >= 40) return 'text-orange-600'
    return 'text-red-600'
  }

  const getHealthBadgeVariant = (status: string) => {
    switch (status) {
      case 'excellent': return 'success'
      case 'good': return 'success'
      case 'fair': return 'warning'
      case 'poor': return 'warning'
      case 'critical': return 'error'
      default: return 'secondary'
    }
  }

  if (isLoading && !metrics) {
    return (
      <DashboardLayout>
        <div className="space-y-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Analytics & Performance</h1>
            <p className="text-gray-600">
              Monitor system performance and health metrics
            </p>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-sm text-gray-500">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setAutoRefresh(!autoRefresh)}
            >
              <Activity className={`h-4 w-4 mr-2 ${autoRefresh ? 'text-green-500' : 'text-gray-400'}`} />
              Auto Refresh
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchMetrics}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              onClick={optimizeSystem}
              size="sm"
            >
              <Zap className="h-4 w-4 mr-2" />
              Optimize
            </Button>
          </div>
        </div>

        {/* Health Score */}
        {metrics?.health && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                System Health Score
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <div className={`text-4xl font-bold ${getHealthColor(metrics.health.score)}`}>
                    {metrics.health.score}/100
                  </div>
                  <Badge variant={getHealthBadgeVariant(metrics.health.status)} className="mt-2">
                    {metrics.health.status.toUpperCase()}
                  </Badge>
                </div>
                <div className="text-right">
                  {metrics.health.issues.length > 0 && (
                    <div className="space-y-1">
                      <div className="text-sm font-medium text-red-600">Issues:</div>
                      {metrics.health.issues.map((issue, index) => (
                        <div key={index} className="text-xs text-red-500 flex items-center gap-1">
                          <AlertTriangle className="h-3 w-3" />
                          {issue}
                        </div>
                      ))}
                    </div>
                  )}
                  {metrics.health.recommendations.length > 0 && (
                    <div className="space-y-1 mt-4">
                      <div className="text-sm font-medium text-blue-600">Recommendations:</div>
                      {metrics.health.recommendations.map((rec, index) => (
                        <div key={index} className="text-xs text-blue-500">
                          {rec}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Performance Metrics */}
        {metrics?.performance && (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.performance.total_requests}</div>
                <p className="text-xs text-muted-foreground">
                  {metrics.performance.error_rate.toFixed(1)}% error rate
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatDuration(metrics.performance.average_response_time)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Average across all requests
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
                <Cpu className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatBytes(metrics.performance.memory_usage.current)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Peak: {formatBytes(metrics.performance.memory_usage.peak)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Database Queries</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {metrics.performance.database_performance.total_queries}
                </div>
                <p className="text-xs text-muted-foreground">
                  Avg: {formatDuration(metrics.performance.database_performance.avg_query_time)}
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* System Resources */}
        {metrics?.system && (
          <div className="grid gap-6 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Cpu className="h-5 w-5" />
                  CPU & Memory
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm">
                    <span>Memory Usage</span>
                    <span>{formatBytes(metrics.system.memory.current)}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{
                        width: `${(metrics.system.memory.current / metrics.system.memory.peak) * 100}%`
                      }}
                    ></div>
                  </div>
                </div>
                <div>
                  <div className="text-sm">
                    <span>Load Average: </span>
                    <span className="font-mono">
                      {metrics.system.cpu.load_average?.map(load => load.toFixed(2)).join(', ') || 'N/A'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <HardDrive className="h-5 w-5" />
                  Disk Usage
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm">
                    <span>Free Space</span>
                    <span>{formatBytes(metrics.system.disk.free_space)}</span>
                  </div>
                  <div className="flex justify-between text-sm text-gray-500">
                    <span>Total Space</span>
                    <span>{formatBytes(metrics.system.disk.total_space)}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div
                      className="bg-green-600 h-2 rounded-full"
                      style={{
                        width: `${(metrics.system.disk.free_space / metrics.system.disk.total_space) * 100}%`
                      }}
                    ></div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5" />
                  System Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="text-sm">
                    <span>Uptime: </span>
                    <span className="font-mono">
                      {formatDuration(metrics.system.uptime * 1000)}
                    </span>
                  </div>
                </div>
                <div>
                  <div className="text-sm">
                    <span>Memory Limit: </span>
                    <span className="font-mono">{metrics.system.memory.limit}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Slowest Operations */}
        {metrics?.performance?.slowest_operations && (
          <Card>
            <CardHeader>
              <CardTitle>Slowest Operations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {metrics.performance.slowest_operations.slice(0, 5).map((operation, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded">
                    <div>
                      <div className="font-medium">{operation.operation}</div>
                      <div className="text-sm text-gray-500">
                        {operation.count} executions
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-mono text-sm">
                        Avg: {formatDuration(operation.avg_time)}
                      </div>
                      <div className="font-mono text-xs text-gray-500">
                        Max: {formatDuration(operation.max_time)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
