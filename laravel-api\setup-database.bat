@echo off
echo ============================================================================
echo AXIENT MCP++ DATABASE SETUP FOR WINDOWS/LARAGON
echo ============================================================================
echo.

echo Checking PostgreSQL installation...
where psql >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] PostgreSQL is not installed or not in PATH
    echo.
    echo Please install PostgreSQL:
    echo 1. Download from: https://www.postgresql.org/download/windows/
    echo 2. Or use Laragon PostgreSQL addon
    echo 3. Or install via Chocolatey: choco install postgresql
    echo.
    pause
    exit /b 1
)

echo [OK] PostgreSQL found
echo.

echo Creating database and user...
echo Please enter PostgreSQL superuser password when prompted.
echo.

psql -U postgres -h localhost -c "CREATE DATABASE axient_mcp_dev;" 2>nul
if %errorlevel% equ 0 (
    echo [OK] Database 'axient_mcp_dev' created successfully
) else (
    echo [INFO] Database might already exist, continuing...
)

psql -U postgres -h localhost -d axient_mcp_dev -c "CREATE EXTENSION IF NOT EXISTS vector;" 2>nul
if %errorlevel% equ 0 (
    echo [OK] pgvector extension enabled
) else (
    echo [WARNING] Could not enable pgvector extension
    echo You may need to install pgvector separately
)

echo.
echo Testing database connection...
php artisan config:clear
php artisan migrate:status

if %errorlevel% equ 0 (
    echo [OK] Database connection successful
    echo.
    echo Running migrations...
    php artisan migrate
    
    if %errorlevel% equ 0 (
        echo [OK] Migrations completed successfully
        echo.
        echo ============================================================================
        echo DATABASE SETUP COMPLETE!
        echo ============================================================================
        echo Database: axient_mcp_dev
        echo Host: localhost:5432
        echo User: postgres
        echo ============================================================================
    ) else (
        echo [ERROR] Migration failed
    )
) else (
    echo [ERROR] Database connection failed
    echo.
    echo Please check:
    echo 1. PostgreSQL is running
    echo 2. Database credentials in .env file
    echo 3. Database 'axient_mcp_dev' exists
)

echo.
pause
