import { useState, useEffect } from 'react'

/**
 * Custom hook to handle time display without hydration mismatch
 * Returns null on server-side and actual time on client-side
 */
export function useClientTime(initialTime?: Date) {
  const [time, setTime] = useState<Date | null>(null)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    setTime(initialTime || new Date())
  }, [initialTime])

  const updateTime = (newTime?: Date) => {
    if (isClient) {
      setTime(newTime || new Date())
    }
  }

  const formatTime = (options?: Intl.DateTimeFormatOptions) => {
    if (!isClient || !time) {
      return '--:--:--'
    }
    return time.toLocaleTimeString(undefined, options)
  }

  const formatDate = (options?: Intl.DateTimeFormatOptions) => {
    if (!isClient || !time) {
      return '--/--/----'
    }
    return time.toLocaleDateString(undefined, options)
  }

  const formatDateTime = (options?: Intl.DateTimeFormatOptions) => {
    if (!isClient || !time) {
      return '--/--/---- --:--:--'
    }
    return time.toLocaleString(undefined, options)
  }

  return {
    time,
    isClient,
    updateTime,
    formatTime,
    formatDate,
    formatDateTime
  }
}
