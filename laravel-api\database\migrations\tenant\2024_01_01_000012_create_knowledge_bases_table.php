<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('knowledge_bases', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('status')->default('active'); // active, inactive, processing
            $table->json('settings')->nullable();
            $table->json('embedding_config')->nullable();
            $table->integer('document_count')->default(0);
            $table->integer('chunk_count')->default(0);
            $table->bigInteger('total_tokens')->default(0);
            $table->timestamp('last_updated_at')->nullable();
            $table->timestamps();
            
            $table->index(['status', 'name']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('knowledge_bases');
    }
};
