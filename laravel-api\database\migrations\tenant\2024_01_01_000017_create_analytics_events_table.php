<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('analytics_events', function (Blueprint $table) {
            $table->id();
            $table->string('event_type'); // user_action, system_event, api_call, error, etc.
            $table->string('event_name'); // login, document_upload, workflow_execute, etc.
            $table->string('entity_type')->nullable(); // user, document, workflow, etc.
            $table->string('entity_id')->nullable();
            $table->json('properties')->nullable(); // Event-specific data
            $table->json('metadata')->nullable(); // Additional context
            $table->string('user_agent')->nullable();
            $table->string('ip_address')->nullable();
            $table->string('session_id')->nullable();
            $table->integer('duration_ms')->nullable();
            $table->timestamp('occurred_at');
            $table->timestamps();
            
            $table->index(['event_type', 'event_name']);
            $table->index(['entity_type', 'entity_id']);
            $table->index(['occurred_at', 'event_type']);
            $table->index(['session_id', 'occurred_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('analytics_events');
    }
};
