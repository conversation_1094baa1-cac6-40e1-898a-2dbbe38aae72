<?php

declare(strict_types=1);

namespace App\Providers;

use App\Contracts\MCPOrchestratorInterface;
use App\Services\MCP\MCPOrchestrator;
use Illuminate\Support\ServiceProvider;

class MCPServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(MCPOrchestratorInterface::class, MCPOrchestrator::class);

        $this->app->singleton('mcp.orchestrator', function ($app) {
            return $app->make(MCPOrchestratorInterface::class);
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register MCP agents when the application boots
        $this->registerMCPAgents();
    }

    /**
     * Register all MCP agents with the orchestrator
     */
    protected function registerMCPAgents(): void
    {
        $orchestrator = $this->app->make(MCPOrchestratorInterface::class);

        // Register Primary Agents
        $orchestrator->registerAgent(new \App\Services\MCP\Agents\IntentAgent());
        $orchestrator->registerAgent(new \App\Services\MCP\Agents\RetrieverAgent());
        $orchestrator->registerAgent(new \App\Services\MCP\Agents\LLMAgent());
        $orchestrator->registerAgent(new \App\Services\MCP\Agents\MemoryAgent());

        // Register Secondary Agents
        $orchestrator->registerAgent(new \App\Services\MCP\Agents\ToolAgent());
        $orchestrator->registerAgent(new \App\Services\MCP\Agents\WorkflowAgent());
        $orchestrator->registerAgent(new \App\Services\MCP\Agents\FormatterAgent());
        $orchestrator->registerAgent(new \App\Services\MCP\Agents\GuardrailAgent());
    }
}
