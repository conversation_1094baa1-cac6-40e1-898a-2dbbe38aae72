'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useApi } from '@/contexts/AuthContext'
import { 
  ArrowLeft,
  Search,
  FileText,
  Clock,
  Zap,
  Copy,
  ExternalLink,
  RefreshCw
} from 'lucide-react'
import Link from 'next/link'

interface KnowledgeBase {
  id: number
  name: string
  description: string
  document_count: number
  chunk_count: number
}

interface SearchResult {
  id: number
  content: string
  document_title: string
  document_id: number
  chunk_index: number
  relevance_score: number
  metadata?: any
}

interface SearchResponse {
  query: string
  results: SearchResult[]
  total_results: number
}

export default function KnowledgeBaseSearchPage() {
  const params = useParams()
  const { apiCall } = useApi()
  const [knowledgeBase, setKnowledgeBase] = useState<KnowledgeBase | null>(null)
  const [query, setQuery] = useState('')
  const [searchResults, setSearchResults] = useState<SearchResponse | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSearching, setIsSearching] = useState(false)
  const [searchHistory, setSearchHistory] = useState<string[]>([])

  const fetchKnowledgeBase = async () => {
    try {
      const response = await apiCall(`/knowledge-bases/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setKnowledgeBase(data.data)
        }
      }
    } catch (error) {
      console.error('Failed to fetch knowledge base:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchKnowledgeBase()
    
    // Load search history from localStorage
    const saved = localStorage.getItem(`search_history_${params.id}`)
    if (saved) {
      setSearchHistory(JSON.parse(saved))
    }
  }, [params.id])

  const handleSearch = async (searchQuery?: string) => {
    const queryToSearch = searchQuery || query
    if (!queryToSearch.trim()) return

    setIsSearching(true)
    try {
      const response = await apiCall(`/knowledge-bases/${params.id}/search`, {
        method: 'POST',
        body: JSON.stringify({
          query: queryToSearch,
          limit: 20
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setSearchResults(data.data)
          
          // Add to search history
          const newHistory = [queryToSearch, ...searchHistory.filter(h => h !== queryToSearch)].slice(0, 10)
          setSearchHistory(newHistory)
          localStorage.setItem(`search_history_${params.id}`, JSON.stringify(newHistory))
        }
      }
    } catch (error) {
      console.error('Search failed:', error)
    } finally {
      setIsSearching(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // You could add a toast notification here
  }

  const highlightText = (text: string, query: string) => {
    if (!query) return text
    
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
    const parts = text.split(regex)
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 px-1 rounded">
          {part}
        </mark>
      ) : part
    )
  }

  const getRelevanceColor = (score: number) => {
    if (score >= 0.8) return 'bg-green-500'
    if (score >= 0.6) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="space-y-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-32 bg-gray-200 rounded mb-8"></div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/knowledge-bases">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Knowledge Bases
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Search Knowledge Base
              </h1>
              <p className="text-gray-600">
                {knowledgeBase?.name} - {knowledgeBase?.document_count} documents, {knowledgeBase?.chunk_count} chunks
              </p>
            </div>
          </div>
        </div>

        {/* Search Interface */}
        <Card>
          <CardHeader>
            <CardTitle>Search Documents</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Search Input */}
            <div className="flex gap-2">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Enter your search query..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <Button onClick={() => handleSearch()} disabled={isSearching || !query.trim()}>
                {isSearching ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <Search className="h-4 w-4" />
                )}
              </Button>
            </div>

            {/* Search History */}
            {searchHistory.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Recent Searches</h4>
                <div className="flex flex-wrap gap-2">
                  {searchHistory.map((historyQuery, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        setQuery(historyQuery)
                        handleSearch(historyQuery)
                      }}
                      className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors"
                    >
                      {historyQuery}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Quick Search Examples */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Try searching for:</h4>
              <div className="flex flex-wrap gap-2">
                {['API documentation', 'user authentication', 'database setup', 'deployment guide'].map((example) => (
                  <button
                    key={example}
                    onClick={() => {
                      setQuery(example)
                      handleSearch(example)
                    }}
                    className="px-3 py-1 text-sm bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-full transition-colors"
                  >
                    {example}
                  </button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Search Results */}
        {searchResults && (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>
                  Search Results ({searchResults.total_results})
                </CardTitle>
                <div className="text-sm text-gray-500">
                  Query: "{searchResults.query}"
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {searchResults.results.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Search className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No results found for "{searchResults.query}"</p>
                  <p className="text-sm">Try different keywords or check your spelling</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {searchResults.results.map((result, index) => (
                    <div key={result.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-gray-400" />
                          <h4 className="font-medium text-blue-600 hover:text-blue-800">
                            {result.document_title}
                          </h4>
                          <Badge variant="outline" className="text-xs">
                            Chunk {result.chunk_index + 1}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1">
                            <div 
                              className={`w-2 h-2 rounded-full ${getRelevanceColor(result.relevance_score)}`}
                            ></div>
                            <span className="text-xs text-gray-500">
                              {Math.round(result.relevance_score * 100)}% match
                            </span>
                          </div>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => copyToClipboard(result.content)}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      
                      <div className="text-sm text-gray-700 leading-relaxed">
                        {highlightText(
                          result.content.length > 300 
                            ? result.content.substring(0, 300) + '...'
                            : result.content,
                          searchResults.query
                        )}
                      </div>

                      {result.metadata && (
                        <div className="mt-2 text-xs text-gray-500">
                          <span>Metadata: {JSON.stringify(result.metadata)}</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Search Tips */}
        {!searchResults && (
          <Card>
            <CardHeader>
              <CardTitle>Search Tips</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2 flex items-center gap-2">
                    <Zap className="h-4 w-4 text-yellow-500" />
                    Effective Searching
                  </h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Use specific keywords and phrases</li>
                    <li>• Try different variations of your query</li>
                    <li>• Use quotes for exact phrase matching</li>
                    <li>• Include context words for better results</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2 flex items-center gap-2">
                    <Clock className="h-4 w-4 text-blue-500" />
                    Search Features
                  </h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Semantic similarity search</li>
                    <li>• Relevance scoring</li>
                    <li>• Document chunk navigation</li>
                    <li>• Search history tracking</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
