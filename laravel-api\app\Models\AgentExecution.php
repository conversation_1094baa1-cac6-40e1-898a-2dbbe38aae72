<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AgentExecution extends Model
{
    protected $fillable = [
        'agent_id',
        'session_id',
        'input_data',
        'output_data',
        'status',
        'error_message',
        'execution_time_ms',
    ];

    protected $casts = [
        'input_data' => 'array',
        'output_data' => 'array',
        'execution_time_ms' => 'integer',
    ];

    /**
     * Get the agent that was executed
     */
    public function agent(): BelongsTo
    {
        return $this->belongsTo(MCPAgent::class, 'agent_id');
    }

    /**
     * Scope for successful executions
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for failed executions
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for a specific session
     */
    public function scopeForSession($query, string $sessionId)
    {
        return $query->where('session_id', $sessionId);
    }

    /**
     * Get execution duration in seconds
     */
    public function getDurationInSeconds(): float
    {
        return $this->execution_time_ms / 1000;
    }
}
