'use client'

import { useState } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Save, RefreshCw, Database, Shield, Zap } from 'lucide-react'

export default function SettingsPage() {
  const [isSaving, setIsSaving] = useState(false)
  const [settings, setSettings] = useState({
    system: {
      debug_mode: false,
      log_level: 'info',
      max_concurrent_requests: 100,
      request_timeout: 30
    },
    agents: {
      intent_confidence_threshold: 0.7,
      retriever_max_results: 5,
      llm_temperature: 0.7,
      memory_max_context: 10
    },
    security: {
      enable_guardrails: true,
      content_filtering: true,
      rate_limiting: true,
      max_requests_per_minute: 60
    },
    database: {
      connection_pool_size: 10,
      query_timeout: 5000,
      enable_query_logging: false
    }
  })

  const handleSave = async () => {
    setIsSaving(true)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    setIsSaving(false)
  }

  const updateSetting = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value
      }
    }))
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
            <p className="text-gray-600">
              Configure your MCP system settings
            </p>
          </div>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Changes
          </Button>
        </div>

        {/* System Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              System Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <div>
                <label className="block text-sm font-medium mb-2">Debug Mode</label>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={settings.system.debug_mode}
                    onChange={(e) => updateSetting('system', 'debug_mode', e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm text-gray-600">Enable debug logging</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Log Level</label>
                <select
                  value={settings.system.log_level}
                  onChange={(e) => updateSetting('system', 'log_level', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="debug">Debug</option>
                  <option value="info">Info</option>
                  <option value="warning">Warning</option>
                  <option value="error">Error</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Max Concurrent Requests</label>
                <input
                  type="number"
                  value={settings.system.max_concurrent_requests}
                  onChange={(e) => updateSetting('system', 'max_concurrent_requests', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Request Timeout (seconds)</label>
                <input
                  type="number"
                  value={settings.system.request_timeout}
                  onChange={(e) => updateSetting('system', 'request_timeout', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Agent Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Agent Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <div>
                <label className="block text-sm font-medium mb-2">Intent Confidence Threshold</label>
                <input
                  type="number"
                  step="0.1"
                  min="0"
                  max="1"
                  value={settings.agents.intent_confidence_threshold}
                  onChange={(e) => updateSetting('agents', 'intent_confidence_threshold', parseFloat(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Retriever Max Results</label>
                <input
                  type="number"
                  value={settings.agents.retriever_max_results}
                  onChange={(e) => updateSetting('agents', 'retriever_max_results', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">LLM Temperature</label>
                <input
                  type="number"
                  step="0.1"
                  min="0"
                  max="2"
                  value={settings.agents.llm_temperature}
                  onChange={(e) => updateSetting('agents', 'llm_temperature', parseFloat(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Memory Max Context Messages</label>
                <input
                  type="number"
                  value={settings.agents.memory_max_context}
                  onChange={(e) => updateSetting('agents', 'memory_max_context', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Security & Safety
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <div>
                <label className="block text-sm font-medium mb-2">Enable Guardrails</label>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={settings.security.enable_guardrails}
                    onChange={(e) => updateSetting('security', 'enable_guardrails', e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm text-gray-600">Content filtering and safety checks</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Content Filtering</label>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={settings.security.content_filtering}
                    onChange={(e) => updateSetting('security', 'content_filtering', e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm text-gray-600">Filter inappropriate content</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Rate Limiting</label>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={settings.security.rate_limiting}
                    onChange={(e) => updateSetting('security', 'rate_limiting', e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm text-gray-600">Limit requests per user</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Max Requests per Minute</label>
                <input
                  type="number"
                  value={settings.security.max_requests_per_minute}
                  onChange={(e) => updateSetting('security', 'max_requests_per_minute', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Current Configuration Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Configuration Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {settings.system.max_concurrent_requests}
                </div>
                <div className="text-sm text-gray-600">Max Concurrent Requests</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {(settings.agents.intent_confidence_threshold * 100).toFixed(0)}%
                </div>
                <div className="text-sm text-gray-600">Intent Confidence</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {settings.agents.retriever_max_results}
                </div>
                <div className="text-sm text-gray-600">Max Search Results</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {settings.security.max_requests_per_minute}
                </div>
                <div className="text-sm text-gray-600">Rate Limit (per min)</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
