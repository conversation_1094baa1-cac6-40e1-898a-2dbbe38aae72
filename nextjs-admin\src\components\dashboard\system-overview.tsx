'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { apiClient, type SystemStatus, type Agent } from '@/lib/api'
import { formatDate } from '@/lib/utils'
import { Bot, Activity, Zap, Shield, RefreshCw } from 'lucide-react'

export function SystemOverview() {
  const [systemData, setSystemData] = useState<SystemStatus | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchSystemData = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await apiClient.getSystemStatus()
      if (response.success && response.data) {
        setSystemData(response.data)
      } else {
        setError(response.error || 'Failed to fetch system data')
      }
    } catch (err) {
      setError('Network error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchSystemData()
  }, [])

  if (isLoading) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <Card className="border-red-200">
        <CardContent className="pt-6">
          <div className="text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchSystemData} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  const primaryAgents = systemData?.agents.filter(agent => agent.type === 'primary') || []
  const secondaryAgents = systemData?.agents.filter(agent => agent.type === 'secondary') || []
  const activeAgents = systemData?.agents.filter(agent => agent.is_active) || []

  return (
    <div className="space-y-6">
      {/* System Status Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Agents</CardTitle>
            <Bot className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemData?.agent_count || 0}</div>
            <p className="text-xs text-muted-foreground">
              {primaryAgents.length} primary, {secondaryAgents.length} secondary
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Agents</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{activeAgents.length}</div>
            <p className="text-xs text-muted-foreground">
              {((activeAgents.length / (systemData?.agent_count || 1)) * 100).toFixed(0)}% operational
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Status</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <Badge variant={systemData?.system_status === 'operational' ? 'success' : 'warning'}>
                {systemData?.system_status || 'Unknown'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              Last checked: {systemData?.timestamp ? formatDate(systemData.timestamp) : 'Never'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Security Status</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <Badge variant="success">Secure</Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              All guardrails active
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Agent Status Table */}
      <Card>
        <CardHeader>
          <CardTitle>Agent Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">Agent</th>
                  <th className="text-left py-2">Type</th>
                  <th className="text-left py-2">Status</th>
                  <th className="text-left py-2">Execution Order</th>
                  <th className="text-left py-2">Configuration</th>
                </tr>
              </thead>
              <tbody>
                {systemData?.agents.map((agent) => (
                  <tr key={agent.name} className="border-b">
                    <td className="py-2 font-medium">{agent.name}</td>
                    <td className="py-2">
                      <Badge variant={agent.type === 'primary' ? 'default' : 'secondary'}>
                        {agent.type}
                      </Badge>
                    </td>
                    <td className="py-2">
                      <Badge variant={agent.is_active ? 'success' : 'error'}>
                        {agent.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </td>
                    <td className="py-2">{agent.execution_order}</td>
                    <td className="py-2">
                      <span className="text-sm text-gray-500">
                        {Object.keys(agent.config || {}).length} settings
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
