<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Tenant;
use Illuminate\Support\Facades\Auth;

class TenantMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $tenant = $this->resolveTenant($request);
        
        if (!$tenant) {
            return response()->json([
                'error' => 'Tenant not found or inactive'
            ], 404);
        }

        // Set current tenant
        $tenant->makeCurrent();
        
        // Add tenant to request
        $request->merge(['tenant' => $tenant]);

        return $next($request);
    }

    /**
     * Resolve tenant from request
     */
    protected function resolveTenant(Request $request): ?Tenant
    {
        // Try to get tenant from subdomain
        $host = $request->getHost();
        $subdomain = $this->extractSubdomain($host);
        
        if ($subdomain) {
            $tenant = Tenant::findByDomain($subdomain);
            if ($tenant) {
                return $tenant;
            }
        }

        // Try to get tenant from custom domain
        $tenant = Tenant::findByDomain($host);
        if ($tenant) {
            return $tenant;
        }

        // Try to get tenant from header (for API calls)
        $tenantId = $request->header('X-Tenant-ID');
        if ($tenantId) {
            return Tenant::where('id', $tenantId)
                ->where('is_active', true)
                ->first();
        }

        // Try to get tenant from authenticated user
        if (Auth::check()) {
            $user = Auth::user();
            if ($user && $user->tenant_id) {
                return Tenant::find($user->tenant_id);
            }
        }

        // Default tenant for development
        if (app()->environment('local')) {
            return Tenant::first();
        }

        return null;
    }

    /**
     * Extract subdomain from host
     */
    protected function extractSubdomain(string $host): ?string
    {
        $parts = explode('.', $host);
        
        // If we have at least 3 parts (subdomain.domain.tld)
        if (count($parts) >= 3) {
            return $parts[0];
        }

        return null;
    }
}
