<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\DocumentChunk;
use App\Models\AIProvider;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class EmbeddingService
{
    /**
     * Process embedding for a document chunk
     */
    public function processChunk(DocumentChunk $chunk): void
    {
        try {
            $chunk->update(['embedding_status' => 'processing']);

            // Get embedding provider
            $provider = $this->getEmbeddingProvider($chunk->knowledgeBase);
            
            if (!$provider) {
                throw new \Exception('No embedding provider available');
            }

            // Generate embedding
            $embedding = $this->generateEmbedding($chunk->content, $provider);

            // Store embedding
            $chunk->update([
                'embedding' => $embedding,
                'embedding_status' => 'completed',
                'embedded_at' => now()
            ]);

            // Record usage
            $provider->recordUsage([
                'requests' => 1,
                'tokens' => $chunk->token_count ?? 0,
                'errors' => 0
            ]);

            Log::info('Chunk embedding completed', [
                'chunk_id' => $chunk->id,
                'provider' => $provider->name
            ]);

        } catch (\Exception $e) {
            $chunk->update([
                'embedding_status' => 'failed',
                'embedding_error' => $e->getMessage()
            ]);

            Log::error('Chunk embedding failed', [
                'chunk_id' => $chunk->id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Process embeddings for multiple chunks in batch
     */
    public function processBatch(array $chunks): array
    {
        $results = [];
        $provider = null;

        foreach ($chunks as $chunk) {
            try {
                // Get provider for first chunk's knowledge base
                if (!$provider && $chunk->knowledgeBase) {
                    $provider = $this->getEmbeddingProvider($chunk->knowledgeBase);
                }

                if (!$provider) {
                    throw new \Exception('No embedding provider available');
                }

                // Check rate limits
                if (!$provider->isWithinLimits()) {
                    throw new \Exception('Provider rate limit exceeded');
                }

                $this->processChunk($chunk);
                $results[] = ['chunk_id' => $chunk->id, 'status' => 'success'];

            } catch (\Exception $e) {
                $results[] = [
                    'chunk_id' => $chunk->id,
                    'status' => 'failed',
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * Generate embedding for text using specified provider
     */
    protected function generateEmbedding(string $text, AIProvider $provider): array
    {
        $config = $provider->configuration;
        
        return match($provider->name) {
            'openai' => $this->generateOpenAIEmbedding($text, $config),
            'google' => $this->generateGoogleEmbedding($text, $config),
            default => throw new \Exception("Embedding not supported for provider: {$provider->name}")
        };
    }

    /**
     * Generate OpenAI embedding
     */
    protected function generateOpenAIEmbedding(string $text, array $config): array
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $config['api_key'],
            'Content-Type' => 'application/json'
        ])->timeout(30)->post($config['base_url'] . '/embeddings', [
            'model' => $config['embedding_model'] ?? 'text-embedding-3-small',
            'input' => $text,
            'encoding_format' => 'float'
        ]);

        if (!$response->successful()) {
            $error = $response->json()['error']['message'] ?? 'Unknown error';
            throw new \Exception("OpenAI embedding failed: {$error}");
        }

        $data = $response->json();
        return $data['data'][0]['embedding'] ?? [];
    }

    /**
     * Generate Google AI embedding
     */
    protected function generateGoogleEmbedding(string $text, array $config): array
    {
        $model = $config['embedding_model'] ?? 'embedding-001';
        $apiKey = $config['api_key'];
        
        $response = Http::timeout(30)->post(
            "https://generativelanguage.googleapis.com/v1/models/{$model}:embedContent?key={$apiKey}",
            [
                'content' => [
                    'parts' => [
                        ['text' => $text]
                    ]
                ]
            ]
        );

        if (!$response->successful()) {
            $error = $response->json()['error']['message'] ?? 'Unknown error';
            throw new \Exception("Google AI embedding failed: {$error}");
        }

        $data = $response->json();
        return $data['embedding']['values'] ?? [];
    }

    /**
     * Search for similar chunks using vector similarity
     */
    public function searchSimilar(string $query, int $knowledgeBaseId, int $limit = 10): array
    {
        try {
            // Get knowledge base
            $knowledgeBase = \App\Models\KnowledgeBase::find($knowledgeBaseId);
            if (!$knowledgeBase) {
                throw new \Exception('Knowledge base not found');
            }

            // Get embedding provider
            $provider = $this->getEmbeddingProvider($knowledgeBase);
            if (!$provider) {
                throw new \Exception('No embedding provider available');
            }

            // Generate query embedding
            $queryEmbedding = $this->generateEmbedding($query, $provider);

            // For now, return a simple text-based search
            // In production, you would use vector similarity search
            $chunks = DocumentChunk::where('knowledge_base_id', $knowledgeBaseId)
                ->where('embedding_status', 'completed')
                ->where('content', 'LIKE', "%{$query}%")
                ->with('document')
                ->limit($limit)
                ->get();

            return $chunks->map(function ($chunk) use ($query) {
                return [
                    'id' => $chunk->id,
                    'content' => $chunk->content,
                    'document_title' => $chunk->document->title ?? 'Unknown',
                    'document_id' => $chunk->document_id,
                    'chunk_index' => $chunk->chunk_index,
                    'relevance_score' => $this->calculateTextSimilarity($query, $chunk->content),
                    'metadata' => $chunk->metadata
                ];
            })->sortByDesc('relevance_score')->values()->toArray();

        } catch (\Exception $e) {
            Log::error('Similarity search failed', [
                'query' => $query,
                'knowledge_base_id' => $knowledgeBaseId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Calculate text similarity (simple implementation)
     */
    protected function calculateTextSimilarity(string $query, string $text): float
    {
        $queryWords = array_filter(explode(' ', strtolower($query)));
        $textWords = array_filter(explode(' ', strtolower($text)));
        
        if (empty($queryWords) || empty($textWords)) {
            return 0.0;
        }
        
        $intersection = array_intersect($queryWords, $textWords);
        $union = array_unique(array_merge($queryWords, $textWords));
        
        return count($intersection) / count($union);
    }

    /**
     * Get embedding provider for knowledge base
     */
    protected function getEmbeddingProvider($knowledgeBase): ?AIProvider
    {
        $embeddingConfig = $knowledgeBase->embedding_config ?? [];
        $preferredProvider = $embeddingConfig['provider'] ?? 'openai';

        // Try preferred provider first
        $provider = AIProvider::where('name', $preferredProvider)
            ->where('is_enabled', true)
            ->where('status', 'active')
            ->first();

        if ($provider && $provider->isWithinLimits()) {
            return $provider;
        }

        // Fallback to any available embedding provider
        $providers = AIProvider::where('is_enabled', true)
            ->where('status', 'active')
            ->get();

        foreach ($providers as $fallbackProvider) {
            // Check if provider supports embeddings
            if ($this->supportsEmbeddings($fallbackProvider) && $fallbackProvider->isWithinLimits()) {
                return $fallbackProvider;
            }
        }

        return null;
    }

    /**
     * Check if provider supports embeddings
     */
    protected function supportsEmbeddings(AIProvider $provider): bool
    {
        $capabilities = $provider->capabilities ?? [];
        return $capabilities['text_embedding'] ?? false;
    }

    /**
     * Get embedding statistics for knowledge base
     */
    public function getEmbeddingStats(int $knowledgeBaseId): array
    {
        $chunks = DocumentChunk::where('knowledge_base_id', $knowledgeBaseId);
        
        return [
            'total_chunks' => $chunks->count(),
            'embedded_chunks' => $chunks->where('embedding_status', 'completed')->count(),
            'pending_chunks' => $chunks->where('embedding_status', 'pending')->count(),
            'processing_chunks' => $chunks->where('embedding_status', 'processing')->count(),
            'failed_chunks' => $chunks->where('embedding_status', 'failed')->count(),
            'embedding_progress' => $chunks->count() > 0 
                ? ($chunks->where('embedding_status', 'completed')->count() / $chunks->count()) * 100 
                : 0
        ];
    }

    /**
     * Retry failed embeddings
     */
    public function retryFailed(int $knowledgeBaseId): array
    {
        $failedChunks = DocumentChunk::where('knowledge_base_id', $knowledgeBaseId)
            ->where('embedding_status', 'failed')
            ->get();

        $results = [];
        foreach ($failedChunks as $chunk) {
            try {
                // Reset status and clear error
                $chunk->update([
                    'embedding_status' => 'pending',
                    'embedding_error' => null
                ]);

                $this->processChunk($chunk);
                $results[] = ['chunk_id' => $chunk->id, 'status' => 'success'];

            } catch (\Exception $e) {
                $results[] = [
                    'chunk_id' => $chunk->id,
                    'status' => 'failed',
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * Clear all embeddings for knowledge base
     */
    public function clearEmbeddings(int $knowledgeBaseId): int
    {
        $updated = DocumentChunk::where('knowledge_base_id', $knowledgeBaseId)
            ->update([
                'embedding' => null,
                'embedding_status' => 'pending',
                'embedding_error' => null,
                'embedded_at' => null
            ]);

        return $updated;
    }

    /**
     * Test embedding generation
     */
    public function testEmbedding(string $text, string $providerName): array
    {
        $provider = AIProvider::where('name', $providerName)
            ->where('is_enabled', true)
            ->where('status', 'active')
            ->first();

        if (!$provider) {
            throw new \Exception("Provider '{$providerName}' not available");
        }

        if (!$this->supportsEmbeddings($provider)) {
            throw new \Exception("Provider '{$providerName}' does not support embeddings");
        }

        $startTime = microtime(true);
        $embedding = $this->generateEmbedding($text, $provider);
        $endTime = microtime(true);

        return [
            'provider' => $provider->name,
            'model' => $provider->configuration['embedding_model'] ?? 'unknown',
            'dimensions' => count($embedding),
            'processing_time_ms' => ($endTime - $startTime) * 1000,
            'sample_values' => array_slice($embedding, 0, 5) // First 5 values for preview
        ];
    }
}
