<?php

declare(strict_types=1);

namespace App\Contracts;

interface ToolInterface
{
    /**
     * Get the tool name
     */
    public function getName(): string;

    /**
     * Get the tool description
     */
    public function getDescription(): string;

    /**
     * Execute the tool with given parameters
     */
    public function execute(array $params, string $sessionId): array;

    /**
     * Validate tool parameters
     */
    public function validateParams(array $params): bool;

    /**
     * Get required parameters
     */
    public function getRequiredParams(): array;

    /**
     * Get optional parameters
     */
    public function getOptionalParams(): array;

    /**
     * Check if tool is available
     */
    public function isAvailable(): bool;

    /**
     * Get tool configuration
     */
    public function getConfig(): array;

    /**
     * Set tool configuration
     */
    public function setConfig(array $config): void;
}
