<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MCPAgent;

class MCPAgentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $agents = [
            [
                'name' => 'intent',
                'type' => 'primary',
                'description' => 'Analyzes user intent and classifies queries',
                'config' => [
                    'confidence_threshold' => 0.7,
                    'max_entities' => 10
                ],
                'is_active' => true,
                'execution_order' => 1
            ],
            [
                'name' => 'retriever',
                'type' => 'primary',
                'description' => 'Retrieves relevant documents using RAG',
                'config' => [
                    'max_results' => 5,
                    'similarity_threshold' => 0.7,
                    'embedding_model' => 'text-embedding-ada-002'
                ],
                'is_active' => true,
                'execution_order' => 2
            ],
            [
                'name' => 'llm',
                'type' => 'primary',
                'description' => 'Generates responses using LLM providers',
                'config' => [
                    'preferred_provider' => 'openai',
                    'max_tokens' => 2000,
                    'temperature' => 0.7
                ],
                'is_active' => true,
                'execution_order' => 3
            ],
            [
                'name' => 'memory',
                'type' => 'primary',
                'description' => 'Manages conversation context and history',
                'config' => [
                    'max_context_messages' => 10,
                    'context_window_tokens' => 4000
                ],
                'is_active' => true,
                'execution_order' => 4
            ]
        ];

        foreach ($agents as $agentData) {
            MCPAgent::updateOrCreate(
                ['name' => $agentData['name']],
                $agentData
            );
        }
    }
}
