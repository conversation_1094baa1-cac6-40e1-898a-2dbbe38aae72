<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('conversations', function (Blueprint $table) {
            $table->id();
            $table->string('session_id')->unique();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('title')->nullable();
            $table->json('context')->nullable(); // Conversation context and metadata
            $table->timestamp('last_activity_at');
            $table->timestamps();
            
            $table->index(['user_id', 'last_activity_at']);
        });

        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('conversation_id')->constrained()->onDelete('cascade');
            $table->string('role'); // user, assistant, system
            $table->text('content');
            $table->json('metadata')->nullable(); // Agent execution details, sources, etc.
            $table->timestamps();
            
            $table->index(['conversation_id', 'created_at']);
        });

        Schema::create('workflows', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->json('steps'); // Workflow definition
            $table->json('config')->nullable(); // Workflow configuration
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        Schema::create('workflow_executions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('workflow_id')->constrained()->onDelete('cascade');
            $table->string('session_id');
            $table->json('input_data');
            $table->json('output_data')->nullable();
            $table->string('status'); // pending, running, completed, failed
            $table->integer('current_step')->default(0);
            $table->text('error_message')->nullable();
            $table->timestamps();
            
            $table->index(['session_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('workflow_executions');
        Schema::dropIfExists('workflows');
        Schema::dropIfExists('messages');
        Schema::dropIfExists('conversations');
    }
};
