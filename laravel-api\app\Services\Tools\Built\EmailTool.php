<?php

declare(strict_types=1);

namespace App\Services\Tools\Built;

use App\Services\Tools\AbstractTool;
use Illuminate\Support\Facades\Mail;

class EmailTool extends AbstractTool
{
    protected array $requiredParams = ['recipients', 'subject', 'body'];
    protected array $optionalParams = ['cc', 'bcc', 'attachments'];

    public function getName(): string
    {
        return 'email';
    }

    public function getDescription(): string
    {
        return 'Send emails to specified recipients';
    }

    protected function initialize(): void
    {
        $this->config = array_merge([
            'from_email' => env('MAIL_FROM_ADDRESS'),
            'from_name' => env('MAIL_FROM_NAME', 'AI Assistant'),
            'max_recipients' => 10,
            'simulate_only' => env('APP_ENV') !== 'production'
        ], $this->config);
    }

    public function isAvailable(): bool
    {
        return !empty($this->config['from_email']);
    }

    protected function process(array $params, string $sessionId): array
    {
        $recipients = (array) $params['recipients'];
        $subject = $params['subject'];
        $body = $params['body'];
        $cc = (array) ($params['cc'] ?? []);
        $bcc = (array) ($params['bcc'] ?? []);

        // Validate recipient count
        if (count($recipients) > $this->config['max_recipients']) {
            throw new \InvalidArgumentException('Too many recipients');
        }

        // Validate email addresses
        foreach (array_merge($recipients, $cc, $bcc) as $email) {
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new \InvalidArgumentException("Invalid email address: {$email}");
            }
        }

        // In simulation mode, just return success without sending
        if ($this->config['simulate_only']) {
            return $this->formatResponse([
                'recipients' => $recipients,
                'subject' => $subject,
                'body_length' => strlen($body),
                'cc' => $cc,
                'bcc' => $bcc,
                'simulated' => true
            ], "Email simulated successfully to " . count($recipients) . " recipients");
        }

        try {
            // Send email using Laravel's Mail facade
            Mail::send([], [], function ($message) use ($recipients, $subject, $body, $cc, $bcc) {
                $message->from($this->config['from_email'], $this->config['from_name'])
                       ->to($recipients)
                       ->subject($subject)
                       ->html($body);
                
                if (!empty($cc)) {
                    $message->cc($cc);
                }
                
                if (!empty($bcc)) {
                    $message->bcc($bcc);
                }
            });

            return $this->formatResponse([
                'recipients' => $recipients,
                'subject' => $subject,
                'body_length' => strlen($body),
                'cc' => $cc,
                'bcc' => $bcc,
                'sent' => true
            ], "Email sent successfully to " . count($recipients) . " recipients");

        } catch (\Exception $e) {
            throw new \RuntimeException("Failed to send email: " . $e->getMessage());
        }
    }
}
