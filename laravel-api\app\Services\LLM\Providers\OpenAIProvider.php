<?php

declare(strict_types=1);

namespace App\Services\LLM\Providers;

use App\Services\LLM\AbstractLLMProvider;

class OpenAIProvider extends AbstractLLMProvider
{
    protected string $baseUrl = 'https://api.openai.com/v1';
    
    protected array $availableModels = [
        'gpt-4o',
        'gpt-4o-mini',
        'gpt-4-turbo',
        'gpt-4',
        'gpt-3.5-turbo',
    ];

    public function getName(): string
    {
        return 'openai';
    }

    protected function initialize(): void
    {
        $this->apiKey = $this->config['api_key'] ?? env('OPENAI_API_KEY', '');
        $this->model = $this->config['model'] ?? 'gpt-4o-mini';
        $this->baseUrl = $this->config['base_url'] ?? $this->baseUrl;
        $this->timeout = $this->config['timeout'] ?? 30;
    }

    protected function getChatEndpoint(): string
    {
        return $this->baseUrl . '/chat/completions';
    }

    protected function formatRequest(string $prompt, array $options): array
    {
        return [
            'model' => $this->model,
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'max_tokens' => $options['max_tokens'] ?? 2000,
            'temperature' => $options['temperature'] ?? 0.7,
            'top_p' => $options['top_p'] ?? 1.0,
            'frequency_penalty' => $options['frequency_penalty'] ?? 0.0,
            'presence_penalty' => $options['presence_penalty'] ?? 0.0,
        ];
    }

    protected function parseResponse(array $response): array
    {
        $choice = $response['choices'][0] ?? null;
        
        if (!$choice) {
            throw new \Exception('No choices in OpenAI response');
        }

        $content = $choice['message']['content'] ?? '';
        $usage = $response['usage'] ?? [];

        return [
            'content' => $content,
            'tokens_used' => $usage['total_tokens'] ?? 0,
            'prompt_tokens' => $usage['prompt_tokens'] ?? 0,
            'completion_tokens' => $usage['completion_tokens'] ?? 0,
            'finish_reason' => $choice['finish_reason'] ?? 'unknown',
            'model' => $response['model'] ?? $this->model,
        ];
    }

    public function getAvailableModels(): array
    {
        return $this->availableModels;
    }
}
