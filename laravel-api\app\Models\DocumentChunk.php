<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DocumentChunk extends Model
{
    protected $fillable = [
        'document_id',
        'content',
        'embedding',
        'metadata',
        'chunk_index',
    ];

    protected $casts = [
        'embedding' => 'array',
        'metadata' => 'array',
        'chunk_index' => 'integer',
    ];

    /**
     * Get the document this chunk belongs to
     */
    public function document(): BelongsTo
    {
        return $this->belongsTo(Document::class);
    }

    /**
     * Scope for chunks with embeddings
     */
    public function scopeWithEmbedding($query)
    {
        return $query->whereNotNull('embedding');
    }

    /**
     * Scope for chunks by document
     */
    public function scopeForDocument($query, int $documentId)
    {
        return $query->where('document_id', $documentId);
    }

    /**
     * Get chunk size in characters
     */
    public function getSizeAttribute(): int
    {
        return strlen($this->content);
    }

    /**
     * Check if chunk has embedding
     */
    public function hasEmbedding(): bool
    {
        return !empty($this->embedding);
    }

    /**
     * Get embedding dimension
     */
    public function getEmbeddingDimensionAttribute(): int
    {
        return $this->embedding ? count($this->embedding) : 0;
    }
}
