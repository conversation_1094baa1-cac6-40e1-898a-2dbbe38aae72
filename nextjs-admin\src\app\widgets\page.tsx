'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { 
  Plus, 
  Settings, 
  Copy, 
  Eye, 
  Trash2, 
  Code, 
  ExternalLink,
  Activity,
  Calendar,
  Users
} from 'lucide-react'

interface Widget {
  id: number
  widget_id: string
  name: string
  description: string
  status: 'active' | 'inactive' | 'draft'
  usage_count: number
  last_used_at: string | null
  created_at: string
  configuration: any
  styling: any
  behavior: any
}

export default function WidgetsPage() {
  const [widgets, setWidgets] = useState<Widget[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedWidget, setSelectedWidget] = useState<Widget | null>(null)
  const [showEmbedCode, setShowEmbedCode] = useState<string | null>(null)

  const fetchWidgets = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('http://localhost:8000/api/widgets')
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setWidgets(data.data)
        }
      }
    } catch (error) {
      console.error('Failed to fetch widgets:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const createWidget = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/widgets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: 'New AI Assistant Widget',
          description: 'A customizable AI assistant widget for your website',
          status: 'draft'
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setWidgets(prev => [data.data, ...prev])
        }
      }
    } catch (error) {
      console.error('Failed to create widget:', error)
    }
  }

  const duplicateWidget = async (widget: Widget) => {
    try {
      const response = await fetch(`http://localhost:8000/api/widgets/${widget.id}/duplicate`, {
        method: 'POST'
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          fetchWidgets() // Refresh the list
        }
      }
    } catch (error) {
      console.error('Failed to duplicate widget:', error)
    }
  }

  const deleteWidget = async (widget: Widget) => {
    if (!confirm(`Are you sure you want to delete "${widget.name}"?`)) {
      return
    }

    try {
      const response = await fetch(`http://localhost:8000/api/widgets/${widget.id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setWidgets(prev => prev.filter(w => w.id !== widget.id))
      }
    } catch (error) {
      console.error('Failed to delete widget:', error)
    }
  }

  const getEmbedCode = async (widget: Widget) => {
    try {
      const response = await fetch(`http://localhost:8000/api/widgets/${widget.id}/embed-code`)
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setShowEmbedCode(data.data.embed_code)
        }
      }
    } catch (error) {
      console.error('Failed to get embed code:', error)
    }
  }

  const copyEmbedCode = (embedCode: string) => {
    navigator.clipboard.writeText(embedCode)
    alert('Embed code copied to clipboard!')
  }

  useEffect(() => {
    fetchWidgets()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success'
      case 'inactive': return 'secondary'
      case 'draft': return 'warning'
      default: return 'secondary'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="space-y-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-48 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Embeddable Widgets</h1>
            <p className="text-gray-600">
              Create and manage AI assistant widgets for your websites
            </p>
          </div>
          <Button onClick={createWidget}>
            <Plus className="h-4 w-4 mr-2" />
            Create Widget
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid gap-6 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Widgets</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{widgets.length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Widgets</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {widgets.filter(w => w.status === 'active').length}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Usage</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {widgets.reduce((sum, w) => sum + w.usage_count, 0)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Draft Widgets</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {widgets.filter(w => w.status === 'draft').length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Widgets Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {widgets.map((widget) => (
            <Card key={widget.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{widget.name}</CardTitle>
                    <p className="text-sm text-gray-600 mt-1">
                      {widget.description || 'No description'}
                    </p>
                  </div>
                  <Badge variant={getStatusColor(widget.status)}>
                    {widget.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Usage:</span>
                    <div className="font-medium">{widget.usage_count} times</div>
                  </div>
                  <div>
                    <span className="text-gray-500">Created:</span>
                    <div className="font-medium">{formatDate(widget.created_at)}</div>
                  </div>
                </div>

                {widget.last_used_at && (
                  <div className="text-sm">
                    <span className="text-gray-500">Last used:</span>
                    <div className="font-medium">{formatDate(widget.last_used_at)}</div>
                  </div>
                )}

                <div className="flex flex-wrap gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setSelectedWidget(widget)}
                  >
                    <Settings className="h-3 w-3 mr-1" />
                    Configure
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => getEmbedCode(widget)}
                  >
                    <Code className="h-3 w-3 mr-1" />
                    Embed
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => duplicateWidget(widget)}
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    Copy
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => window.open(`http://localhost:8000/widget/preview/${widget.widget_id}`, '_blank')}
                  >
                    <Eye className="h-3 w-3 mr-1" />
                    Preview
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => deleteWidget(widget)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    Delete
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {widgets.length === 0 && !isLoading && (
          <Card>
            <CardContent className="text-center py-12">
              <div className="text-gray-500 mb-4">
                <Activity className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg">No widgets created yet</p>
                <p className="text-sm">Create your first AI assistant widget to get started</p>
              </div>
              <Button onClick={createWidget}>
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Widget
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Embed Code Modal */}
        {showEmbedCode && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
              <h3 className="text-lg font-semibold mb-4">Widget Embed Code</h3>
              <p className="text-sm text-gray-600 mb-4">
                Copy and paste this code into your website where you want the widget to appear:
              </p>
              <div className="bg-gray-100 p-4 rounded-lg mb-4">
                <pre className="text-sm overflow-x-auto whitespace-pre-wrap">
                  {showEmbedCode}
                </pre>
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => copyEmbedCode(showEmbedCode)}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Code
                </Button>
                <Button onClick={() => setShowEmbedCode(null)}>
                  Close
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Widget Configuration Modal */}
        {selectedWidget && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <h3 className="text-lg font-semibold mb-4">Configure Widget: {selectedWidget.name}</h3>
              <p className="text-sm text-gray-600 mb-6">
                Widget configuration interface will be implemented here with options for:
              </p>
              <div className="grid md:grid-cols-2 gap-6 mb-6">
                <div>
                  <h4 className="font-medium mb-2">Configuration Options:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Welcome message and title</li>
                    <li>• Enabled features (file upload, voice input)</li>
                    <li>• Rate limiting settings</li>
                    <li>• Message length limits</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Styling Options:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Colors and theme</li>
                    <li>• Position and dimensions</li>
                    <li>• Fonts and animations</li>
                    <li>• Custom branding</li>
                  </ul>
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setSelectedWidget(null)}>
                  Close
                </Button>
                <Button>
                  Save Configuration
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
