<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\User;
use App\Models\AnalyticsEvent;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Http\Request;

class SecurityService
{
    /**
     * Check for suspicious login activity
     */
    public function checkSuspiciousLogin(string $email, string $ipAddress): array
    {
        $suspiciousFactors = [];
        
        // Check for multiple failed attempts
        $failedAttempts = $this->getFailedLoginAttempts($email, $ipAddress);
        if ($failedAttempts >= 5) {
            $suspiciousFactors[] = 'multiple_failed_attempts';
        }
        
        // Check for login from new location
        if ($this->isNewLocation($email, $ipAddress)) {
            $suspiciousFactors[] = 'new_location';
        }
        
        // Check for rapid login attempts
        if ($this->hasRapidLoginAttempts($email)) {
            $suspiciousFactors[] = 'rapid_attempts';
        }
        
        // Check for known malicious IPs
        if ($this->isMaliciousIP($ipAddress)) {
            $suspiciousFactors[] = 'malicious_ip';
        }
        
        $riskLevel = $this->calculateRiskLevel($suspiciousFactors);
        
        return [
            'is_suspicious' => !empty($suspiciousFactors),
            'risk_level' => $riskLevel,
            'factors' => $suspiciousFactors,
            'recommended_action' => $this->getRecommendedAction($riskLevel)
        ];
    }

    /**
     * Implement rate limiting
     */
    public function checkRateLimit(string $key, int $maxAttempts = 60, int $decayMinutes = 1): bool
    {
        return RateLimiter::tooManyAttempts($key, $maxAttempts);
    }

    /**
     * Apply rate limiting
     */
    public function applyRateLimit(string $key, int $maxAttempts = 60, int $decayMinutes = 1): void
    {
        RateLimiter::hit($key, $decayMinutes * 60);
    }

    /**
     * Validate password strength
     */
    public function validatePasswordStrength(string $password): array
    {
        $score = 0;
        $feedback = [];
        
        // Length check
        if (strlen($password) >= 12) {
            $score += 2;
        } elseif (strlen($password) >= 8) {
            $score += 1;
        } else {
            $feedback[] = 'Password should be at least 8 characters long';
        }
        
        // Uppercase letters
        if (preg_match('/[A-Z]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Include uppercase letters';
        }
        
        // Lowercase letters
        if (preg_match('/[a-z]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Include lowercase letters';
        }
        
        // Numbers
        if (preg_match('/[0-9]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Include numbers';
        }
        
        // Special characters
        if (preg_match('/[^A-Za-z0-9]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Include special characters';
        }
        
        // Common password check
        if ($this->isCommonPassword($password)) {
            $score -= 2;
            $feedback[] = 'Avoid common passwords';
        }
        
        // Sequential characters
        if ($this->hasSequentialCharacters($password)) {
            $score -= 1;
            $feedback[] = 'Avoid sequential characters';
        }
        
        $strength = $this->getPasswordStrengthLevel($score);
        
        return [
            'score' => max(0, $score),
            'strength' => $strength,
            'is_strong' => $score >= 5,
            'feedback' => $feedback
        ];
    }

    /**
     * Scan for malicious content
     */
    public function scanContent(string $content, string $type = 'text'): array
    {
        $threats = [];
        
        // SQL injection patterns
        $sqlPatterns = [
            '/(\bUNION\b.*\bSELECT\b)/i',
            '/(\bSELECT\b.*\bFROM\b.*\bWHERE\b)/i',
            '/(\bINSERT\b.*\bINTO\b)/i',
            '/(\bDELETE\b.*\bFROM\b)/i',
            '/(\bDROP\b.*\bTABLE\b)/i'
        ];
        
        foreach ($sqlPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $threats[] = 'sql_injection';
                break;
            }
        }
        
        // XSS patterns
        $xssPatterns = [
            '/<script[^>]*>.*?<\/script>/is',
            '/javascript:/i',
            '/on\w+\s*=/i',
            '/<iframe[^>]*>/i'
        ];
        
        foreach ($xssPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $threats[] = 'xss';
                break;
            }
        }
        
        // Path traversal
        if (preg_match('/\.\.\/|\.\.\\\\/', $content)) {
            $threats[] = 'path_traversal';
        }
        
        // Command injection
        $cmdPatterns = [
            '/;\s*(rm|del|format|shutdown)/i',
            '/\|\s*(cat|type|more)/i',
            '/`[^`]*`/',
            '/\$\([^)]*\)/'
        ];
        
        foreach ($cmdPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $threats[] = 'command_injection';
                break;
            }
        }
        
        $riskLevel = empty($threats) ? 'low' : (count($threats) > 2 ? 'high' : 'medium');
        
        return [
            'is_malicious' => !empty($threats),
            'threats' => $threats,
            'risk_level' => $riskLevel,
            'scan_type' => $type
        ];
    }

    /**
     * Log security event
     */
    public function logSecurityEvent(
        string $eventType,
        string $description,
        array $context = [],
        string $severity = 'medium'
    ): void {
        $analyticsService = app(AnalyticsService::class);
        
        $analyticsService->trackEvent(
            'security_event',
            $eventType,
            'security',
            null,
            [
                'description' => $description,
                'severity' => $severity,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent()
            ],
            $context
        );
        
        Log::warning("Security Event: {$eventType}", [
            'description' => $description,
            'severity' => $severity,
            'context' => $context,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }

    /**
     * Check for brute force attacks
     */
    public function checkBruteForce(string $identifier, int $threshold = 10): bool
    {
        $key = "brute_force:{$identifier}";
        $attempts = Cache::get($key, 0);
        
        return $attempts >= $threshold;
    }

    /**
     * Record failed attempt
     */
    public function recordFailedAttempt(string $identifier): void
    {
        $key = "brute_force:{$identifier}";
        $attempts = Cache::get($key, 0) + 1;
        
        Cache::put($key, $attempts, 3600); // 1 hour
        
        if ($attempts >= 5) {
            $this->logSecurityEvent(
                'brute_force_detected',
                "Multiple failed attempts detected for {$identifier}",
                ['attempts' => $attempts],
                'high'
            );
        }
    }

    /**
     * Clear failed attempts
     */
    public function clearFailedAttempts(string $identifier): void
    {
        $key = "brute_force:{$identifier}";
        Cache::forget($key);
    }

    /**
     * Validate API key
     */
    public function validateApiKey(string $apiKey): bool
    {
        // Check format
        if (!preg_match('/^[a-zA-Z0-9]{32,64}$/', $apiKey)) {
            return false;
        }
        
        // Check if key exists and is active
        // This would typically check against a database table
        return true; // Placeholder
    }

    /**
     * Generate secure API key
     */
    public function generateApiKey(): string
    {
        return bin2hex(random_bytes(32));
    }

    /**
     * Encrypt sensitive data
     */
    public function encryptSensitiveData(string $data): string
    {
        return encrypt($data);
    }

    /**
     * Decrypt sensitive data
     */
    public function decryptSensitiveData(string $encryptedData): string
    {
        return decrypt($encryptedData);
    }

    /**
     * Get failed login attempts
     */
    protected function getFailedLoginAttempts(string $email, string $ipAddress): int
    {
        $emailKey = "failed_login:email:{$email}";
        $ipKey = "failed_login:ip:{$ipAddress}";
        
        return max(
            Cache::get($emailKey, 0),
            Cache::get($ipKey, 0)
        );
    }

    /**
     * Check if login is from new location
     */
    protected function isNewLocation(string $email, string $ipAddress): bool
    {
        $key = "known_ips:{$email}";
        $knownIps = Cache::get($key, []);
        
        return !in_array($ipAddress, $knownIps);
    }

    /**
     * Check for rapid login attempts
     */
    protected function hasRapidLoginAttempts(string $email): bool
    {
        $key = "login_attempts:{$email}";
        $attempts = Cache::get($key, []);
        
        // Check if more than 3 attempts in last 5 minutes
        $recentAttempts = array_filter($attempts, function($timestamp) {
            return $timestamp > (time() - 300);
        });
        
        return count($recentAttempts) > 3;
    }

    /**
     * Check if IP is malicious
     */
    protected function isMaliciousIP(string $ipAddress): bool
    {
        // This would typically check against threat intelligence feeds
        $maliciousIps = Cache::get('malicious_ips', []);
        
        return in_array($ipAddress, $maliciousIps);
    }

    /**
     * Calculate risk level
     */
    protected function calculateRiskLevel(array $factors): string
    {
        $count = count($factors);
        
        if ($count >= 3) return 'high';
        if ($count >= 2) return 'medium';
        if ($count >= 1) return 'low';
        
        return 'minimal';
    }

    /**
     * Get recommended action
     */
    protected function getRecommendedAction(string $riskLevel): string
    {
        return match($riskLevel) {
            'high' => 'block_and_alert',
            'medium' => 'require_2fa',
            'low' => 'monitor',
            default => 'allow'
        };
    }

    /**
     * Check if password is common
     */
    protected function isCommonPassword(string $password): bool
    {
        $commonPasswords = [
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', 'monkey'
        ];
        
        return in_array(strtolower($password), $commonPasswords);
    }

    /**
     * Check for sequential characters
     */
    protected function hasSequentialCharacters(string $password): bool
    {
        $sequences = ['123', 'abc', 'qwe', 'asd', 'zxc'];
        
        foreach ($sequences as $sequence) {
            if (stripos($password, $sequence) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Get password strength level
     */
    protected function getPasswordStrengthLevel(int $score): string
    {
        if ($score >= 6) return 'very_strong';
        if ($score >= 5) return 'strong';
        if ($score >= 3) return 'medium';
        if ($score >= 1) return 'weak';
        
        return 'very_weak';
    }
}
