const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api'

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface Agent {
  name: string
  type: 'primary' | 'secondary'
  is_active: boolean
  execution_order: number
  config: Record<string, any>
}

export interface SystemStatus {
  system_status: string
  agents: Agent[]
  agent_count: number
  primary_agents: number
  secondary_agents: number
  timestamp: string
}

export interface Conversation {
  id: number
  session_id: string
  title: string
  user_id?: number
  message_count: number
  last_activity_at: string
  created_at: string
}

export interface AdminUser {
  id: number
  name: string
  email: string
  role: 'admin' | 'user'
  is_active: boolean
  tenant_id: string
  last_login_at: string | null
  created_at: string
  updated_at: string
}

export interface AdminDashboard {
  overview: {
    total_users: number
    active_users: number
    admin_users: number
    tenant_info: {
      id: string
      name: string
      domain: string
      is_active: boolean
    }
  }
  system_health: {
    api_status: string
    database_status: string
    overall_health: string
  }
  recent_activity: Array<{
    id: number
    type: string
    description: string
    timestamp: string
    status: string
  }>
  quick_stats: {
    today_logins: number
    active_sessions: number
  }
}

export interface Message {
  id: number
  conversation_id: number
  role: 'user' | 'assistant' | 'system'
  content: string
  metadata?: Record<string, any>
  created_at: string
}

export interface MCPProcessRequest {
  message: string
  session_id?: string
  context?: Record<string, any>
}

export interface MCPProcessResponse {
  success: boolean
  session_id: string
  response: any
  agents_executed?: string[]
  pipeline_summary?: Record<string, any>
}

class ApiClient {
  private baseUrl: string

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      })

      const data = await response.json()

      if (!response.ok) {
        return {
          success: false,
          error: data.message || `HTTP ${response.status}`,
        }
      }

      return {
        success: true,
        data,
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  // System endpoints
  async getSystemStatus(): Promise<ApiResponse<SystemStatus>> {
    return this.request<SystemStatus>('/test/system-status')
  }

  async getHealthCheck(): Promise<ApiResponse<{ status: string; timestamp: string }>> {
    return this.request('/health')
  }

  // Agent endpoints
  async getAgents(): Promise<ApiResponse<{ agents: Agent[] }>> {
    return this.request('/mcp/agents')
  }

  async testAgent(agentName: string): Promise<ApiResponse<any>> {
    return this.request(`/test/agent/${agentName}`)
  }

  async testMCPPipeline(): Promise<ApiResponse<any>> {
    return this.request('/test/mcp-pipeline')
  }

  async testFullPipeline(): Promise<ApiResponse<any>> {
    return this.request('/test/full-pipeline')
  }

  // MCP processing
  async processMCPRequest(request: MCPProcessRequest): Promise<ApiResponse<MCPProcessResponse>> {
    return this.request<MCPProcessResponse>('/mcp/process', {
      method: 'POST',
      body: JSON.stringify(request),
    })
  }

  async getExecutionHistory(sessionId: string): Promise<ApiResponse<any>> {
    return this.request(`/mcp/history/${sessionId}`)
  }

  // Placeholder methods for future implementation
  async getConversations(): Promise<ApiResponse<Conversation[]>> {
    // This would be implemented when the Laravel API has conversation endpoints
    return {
      success: true,
      data: [],
    }
  }

  async getConversation(id: number): Promise<ApiResponse<Conversation & { messages: Message[] }>> {
    // This would be implemented when the Laravel API has conversation endpoints
    return {
      success: true,
      data: {
        id,
        session_id: '',
        title: '',
        message_count: 0,
        last_activity_at: '',
        created_at: '',
        messages: [],
      },
    }
  }

  async getKnowledgeBases(): Promise<ApiResponse<any[]>> {
    // This would be implemented when the Laravel API has knowledge base endpoints
    return {
      success: true,
      data: [],
    }
  }

  async getWorkflows(): Promise<ApiResponse<any[]>> {
    // This would be implemented when the Laravel API has workflow endpoints
    return {
      success: true,
      data: [],
    }
  }

  async getTools(): Promise<ApiResponse<any[]>> {
    // This would be implemented when the Laravel API has tool endpoints
    return {
      success: true,
      data: [],
    }
  }

  // Admin endpoints
  async getAdminDashboard(): Promise<ApiResponse<AdminDashboard>> {
    return this.request<AdminDashboard>('/admin/dashboard')
  }

  async getAdminUsers(params?: { per_page?: number; search?: string }): Promise<ApiResponse<any>> {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : ''
    return this.request(`/admin/users${queryString}`)
  }

  async createAdminUser(userData: {
    name: string
    email: string
    password: string
    role: 'admin' | 'user'
    is_active?: boolean
  }): Promise<ApiResponse<AdminUser>> {
    return this.request<AdminUser>('/admin/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    })
  }

  async updateAdminUser(userId: number, userData: Partial<AdminUser>): Promise<ApiResponse<AdminUser>> {
    return this.request<AdminUser>(`/admin/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    })
  }

  async deleteAdminUser(userId: number): Promise<ApiResponse<void>> {
    return this.request<void>(`/admin/users/${userId}`, {
      method: 'DELETE',
    })
  }

  async getAdminSettings(): Promise<ApiResponse<any>> {
    return this.request('/admin/settings')
  }
}

export const apiClient = new ApiClient()
export default apiClient
