<?php

declare(strict_types=1);

namespace App\Services\Tools;

use App\Contracts\ToolInterface;
use Illuminate\Support\Facades\Log;

abstract class AbstractTool implements ToolInterface
{
    protected array $config = [];
    protected array $requiredParams = [];
    protected array $optionalParams = [];

    public function __construct(array $config = [])
    {
        $this->config = $config;
        $this->initialize();
    }

    /**
     * Initialize the tool
     */
    protected function initialize(): void
    {
        // Override in child classes if needed
    }

    /**
     * Execute the tool with error handling and logging
     */
    public function execute(array $params, string $sessionId): array
    {
        $startTime = microtime(true);
        
        try {
            if (!$this->validateParams($params)) {
                throw new \InvalidArgumentException('Invalid parameters provided');
            }

            if (!$this->isAvailable()) {
                throw new \RuntimeException('Tool is not available');
            }

            $result = $this->process($params, $sessionId);
            
            $executionTime = microtime(true) - $startTime;
            
            Log::info("Tool {$this->getName()} executed successfully", [
                'session_id' => $sessionId,
                'execution_time' => $executionTime,
                'params' => $params
            ]);

            return array_merge($result, [
                'execution_time' => $executionTime,
                'tool' => $this->getName(),
                'success' => true
            ]);

        } catch (\Exception $e) {
            $executionTime = microtime(true) - $startTime;
            
            Log::error("Tool {$this->getName()} execution failed", [
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
                'execution_time' => $executionTime,
                'params' => $params
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'execution_time' => $executionTime,
                'tool' => $this->getName()
            ];
        }
    }

    /**
     * Process the tool execution - to be implemented by child classes
     */
    abstract protected function process(array $params, string $sessionId): array;

    /**
     * Validate parameters
     */
    public function validateParams(array $params): bool
    {
        // Check required parameters
        foreach ($this->requiredParams as $param) {
            if (!isset($params[$param])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get required parameters
     */
    public function getRequiredParams(): array
    {
        return $this->requiredParams;
    }

    /**
     * Get optional parameters
     */
    public function getOptionalParams(): array
    {
        return $this->optionalParams;
    }

    /**
     * Get tool configuration
     */
    public function getConfig(): array
    {
        return $this->config;
    }

    /**
     * Set tool configuration
     */
    public function setConfig(array $config): void
    {
        $this->config = $config;
        $this->initialize();
    }

    /**
     * Default availability check
     */
    public function isAvailable(): bool
    {
        return true;
    }

    /**
     * Helper method to make HTTP requests
     */
    protected function makeHttpRequest(string $url, array $options = []): array
    {
        $response = \Illuminate\Support\Facades\Http::timeout(30)
            ->retry(3, 1000)
            ->get($url, $options);

        if (!$response->successful()) {
            throw new \Exception("HTTP request failed: {$response->status()}");
        }

        return $response->json() ?? [];
    }

    /**
     * Helper method to format response
     */
    protected function formatResponse(array $data, string $message = ''): array
    {
        return [
            'data' => $data,
            'message' => $message,
            'timestamp' => now()->toISOString()
        ];
    }
}
