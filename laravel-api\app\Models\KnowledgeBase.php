<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;

class KnowledgeBase extends Model
{
    use HasFactory;
    protected $fillable = [
        'name',
        'description',
        'status',
        'settings',
        'embedding_config',
        'document_count',
        'chunk_count',
        'total_tokens',
        'last_updated_at'
    ];

    protected $casts = [
        'settings' => 'array',
        'embedding_config' => 'array',
        'document_count' => 'integer',
        'chunk_count' => 'integer',
        'total_tokens' => 'integer',
        'last_updated_at' => 'datetime'
    ];

    /**
     * Get all documents in this knowledge base
     */
    public function documents(): HasMany
    {
        return $this->hasMany(Document::class);
    }

    /**
     * Get the document chunks for this knowledge base
     */
    public function chunks(): HasMany
    {
        return $this->hasMany(DocumentChunk::class);
    }

    /**
     * Get default settings for knowledge base
     */
    public function getDefaultSettings(): array
    {
        return [
            'chunk_size' => 1000,
            'chunk_overlap' => 200,
            'embedding_model' => 'text-embedding-3-small',
            'embedding_provider' => 'openai',
            'auto_process' => true,
            'allowed_file_types' => ['pdf', 'txt', 'docx', 'md', 'html'],
            'max_file_size' => 10485760, // 10MB
            'extract_tables' => true,
            'extract_images' => false,
            'language' => 'en'
        ];
    }

    /**
     * Get default embedding configuration
     */
    public function getDefaultEmbeddingConfig(): array
    {
        return [
            'provider' => 'openai',
            'model' => 'text-embedding-3-small',
            'dimensions' => 1536,
            'batch_size' => 100,
            'rate_limit' => 3000, // requests per minute
            'retry_attempts' => 3,
            'retry_delay' => 1000 // milliseconds
        ];
    }

    /**
     * Initialize knowledge base with default settings
     */
    public function initializeDefaults(): void
    {
        if (!$this->settings) {
            $this->settings = $this->getDefaultSettings();
        }

        if (!$this->embedding_config) {
            $this->embedding_config = $this->getDefaultEmbeddingConfig();
        }

        $this->save();
    }

    /**
     * Scope for active knowledge bases
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope by type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get total document count
     */
    public function getDocumentCountAttribute(): int
    {
        return $this->documents()->count();
    }

    /**
     * Get total chunk count
     */
    public function getChunkCountAttribute(): int
    {
        return DocumentChunk::whereHas('document', function ($query) {
            $query->where('knowledge_base_id', $this->id);
        })->count();
    }

    /**
     * Check if knowledge base needs syncing
     */
    public function needsSync(): bool
    {
        if (!$this->last_synced_at) {
            return true;
        }

        // Check if it's been more than 24 hours since last sync
        return $this->last_synced_at->diffInHours(now()) > 24;
    }

    /**
     * Mark as synced
     */
    public function markAsSynced(): void
    {
        $this->update(['last_synced_at' => now()]);
    }
}
