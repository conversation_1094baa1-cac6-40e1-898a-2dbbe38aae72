<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\KnowledgeBase;
use App\Models\Document;
use App\Services\DocumentProcessingService;
use App\Services\EmbeddingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class KnowledgeBaseController extends Controller
{
    protected DocumentProcessingService $documentService;
    protected EmbeddingService $embeddingService;

    public function __construct(
        DocumentProcessingService $documentService,
        EmbeddingService $embeddingService
    ) {
        $this->documentService = $documentService;
        $this->embeddingService = $embeddingService;
    }

    /**
     * Get all knowledge bases
     */
    public function index(): JsonResponse
    {
        try {
            $knowledgeBases = KnowledgeBase::withStats()
                ->byName()
                ->get()
                ->map(function ($kb) {
                    return [
                        'id' => $kb->id,
                        'name' => $kb->name,
                        'description' => $kb->description,
                        'status' => $kb->status,
                        'document_count' => $kb->document_count,
                        'chunk_count' => $kb->chunk_count,
                        'total_tokens' => $kb->total_tokens,
                        'last_updated_at' => $kb->last_updated_at,
                        'created_at' => $kb->created_at,
                        'processing_stats' => $kb->getProcessingStats(),
                        'embedding_stats' => $this->embeddingService->getEmbeddingStats($kb->id)
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $knowledgeBases
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new knowledge base
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:knowledge_bases,name',
                'description' => 'nullable|string|max:1000',
                'settings' => 'nullable|array',
                'embedding_config' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $knowledgeBase = KnowledgeBase::create([
                'name' => $request->name,
                'description' => $request->description,
                'status' => 'active',
                'settings' => $request->settings,
                'embedding_config' => $request->embedding_config
            ]);

            // Initialize with defaults if not provided
            $knowledgeBase->initializeDefaults();

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $knowledgeBase->id,
                    'name' => $knowledgeBase->name,
                    'description' => $knowledgeBase->description,
                    'status' => $knowledgeBase->status,
                    'settings' => $knowledgeBase->settings,
                    'embedding_config' => $knowledgeBase->embedding_config
                ],
                'message' => 'Knowledge base created successfully'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to create knowledge base: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific knowledge base
     */
    public function show(KnowledgeBase $knowledgeBase): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $knowledgeBase->id,
                    'name' => $knowledgeBase->name,
                    'description' => $knowledgeBase->description,
                    'status' => $knowledgeBase->status,
                    'settings' => $knowledgeBase->settings,
                    'embedding_config' => $knowledgeBase->embedding_config,
                    'document_count' => $knowledgeBase->document_count,
                    'chunk_count' => $knowledgeBase->chunk_count,
                    'total_tokens' => $knowledgeBase->total_tokens,
                    'last_updated_at' => $knowledgeBase->last_updated_at,
                    'created_at' => $knowledgeBase->created_at,
                    'processing_stats' => $knowledgeBase->getProcessingStats(),
                    'embedding_stats' => $this->embeddingService->getEmbeddingStats($knowledgeBase->id),
                    'storage_size' => $knowledgeBase->getStorageSize()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a knowledge base
     */
    public function update(Request $request, KnowledgeBase $knowledgeBase): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'sometimes|string|max:255|unique:knowledge_bases,name,' . $knowledgeBase->id,
                'description' => 'nullable|string|max:1000',
                'status' => 'sometimes|in:active,inactive,processing',
                'settings' => 'nullable|array',
                'embedding_config' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $knowledgeBase->update($request->only([
                'name', 'description', 'status', 'settings', 'embedding_config'
            ]));

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $knowledgeBase->id,
                    'name' => $knowledgeBase->name,
                    'description' => $knowledgeBase->description,
                    'status' => $knowledgeBase->status
                ],
                'message' => 'Knowledge base updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to update knowledge base: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a knowledge base
     */
    public function destroy(KnowledgeBase $knowledgeBase): JsonResponse
    {
        try {
            // Delete all documents and their files
            foreach ($knowledgeBase->documents as $document) {
                $this->documentService->deleteDocument($document);
            }

            // Delete the knowledge base
            $knowledgeBase->delete();

            return response()->json([
                'success' => true,
                'message' => 'Knowledge base deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to delete knowledge base: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload document to knowledge base
     */
    public function uploadDocument(Request $request, KnowledgeBase $knowledgeBase): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'file' => 'required|file|max:' . (($knowledgeBase->settings['max_file_size'] ?? 10485760) / 1024),
                'title' => 'nullable|string|max:255',
                'metadata' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $file = $request->file('file');
            $metadata = $request->metadata ?? [];
            
            if ($request->title) {
                $metadata['title'] = $request->title;
            }

            $document = $this->documentService->uploadDocument($file, $knowledgeBase, $metadata);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $document->id,
                    'title' => $document->title,
                    'file_name' => $document->file_name,
                    'file_type' => $document->file_type,
                    'file_size' => $document->file_size,
                    'status' => $document->status,
                    'created_at' => $document->created_at
                ],
                'message' => 'Document uploaded successfully'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to upload document: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get documents in knowledge base
     */
    public function getDocuments(KnowledgeBase $knowledgeBase, Request $request): JsonResponse
    {
        try {
            $perPage = min($request->integer('per_page', 20), 100);
            $status = $request->string('status');
            
            $query = $knowledgeBase->documents()->orderBy('created_at', 'desc');
            
            if ($status) {
                $query->where('status', $status);
            }
            
            $documents = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $documents->items(),
                'pagination' => [
                    'current_page' => $documents->currentPage(),
                    'last_page' => $documents->lastPage(),
                    'per_page' => $documents->perPage(),
                    'total' => $documents->total()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search in knowledge base
     */
    public function search(KnowledgeBase $knowledgeBase, Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'query' => 'required|string|min:3|max:500',
                'limit' => 'nullable|integer|min:1|max:50'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $query = $request->query;
            $limit = $request->integer('limit', 10);

            // Search using embedding service
            $results = $this->embeddingService->searchSimilar($query, $knowledgeBase->id, $limit);

            return response()->json([
                'success' => true,
                'data' => [
                    'query' => $query,
                    'results' => $results,
                    'total_results' => count($results)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Search failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process embeddings for knowledge base
     */
    public function processEmbeddings(KnowledgeBase $knowledgeBase): JsonResponse
    {
        try {
            $pendingChunks = $knowledgeBase->chunks()
                ->where('embedding_status', 'pending')
                ->limit(100) // Process in batches
                ->get();

            if ($pendingChunks->isEmpty()) {
                return response()->json([
                    'success' => true,
                    'message' => 'No pending chunks to process',
                    'data' => ['processed' => 0]
                ]);
            }

            $results = $this->embeddingService->processBatch($pendingChunks->toArray());
            
            $successful = collect($results)->where('status', 'success')->count();
            $failed = collect($results)->where('status', 'failed')->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'processed' => count($results),
                    'successful' => $successful,
                    'failed' => $failed,
                    'results' => $results
                ],
                'message' => "Processed {$successful} chunks successfully, {$failed} failed"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Embedding processing failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get supported file types
     */
    public function getSupportedFileTypes(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => KnowledgeBase::getSupportedFileTypes()
        ]);
    }

    /**
     * Test embedding generation
     */
    public function testEmbedding(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'text' => 'required|string|min:10|max:1000',
                'provider' => 'required|string|in:openai,google'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $result = $this->embeddingService->testEmbedding(
                $request->text,
                $request->provider
            );

            return response()->json([
                'success' => true,
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Embedding test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cleanup failed processing
     */
    public function cleanup(KnowledgeBase $knowledgeBase): JsonResponse
    {
        try {
            $cleaned = $knowledgeBase->cleanupFailed();

            return response()->json([
                'success' => true,
                'data' => ['cleaned_items' => $cleaned],
                'message' => "Cleaned up {$cleaned} failed items"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Cleanup failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reprocess all documents
     */
    public function reprocess(KnowledgeBase $knowledgeBase): JsonResponse
    {
        try {
            $knowledgeBase->reprocessAll();

            return response()->json([
                'success' => true,
                'message' => 'Reprocessing initiated for all documents'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Reprocessing failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
