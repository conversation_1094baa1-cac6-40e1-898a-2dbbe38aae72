<?php

declare(strict_types=1);

namespace App\Services\MCP\Agents;

use App\Services\MCP\AbstractMCPAgent;
use App\Services\Tools\ToolRegistry;
use App\Models\ApiConnection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class ToolAgent extends AbstractMCPAgent
{
    protected ToolRegistry $toolRegistry;
    protected int $maxExecutionTime = 30;
    protected int $maxConcurrentTools = 3;

    public function __construct(array $config = [])
    {
        parent::__construct($config);
        $this->toolRegistry = new ToolRegistry();
    }

    public function getName(): string
    {
        return 'tool';
    }

    public function getType(): string
    {
        return 'secondary';
    }

    public function getExecutionOrder(): int
    {
        return 10; // After primary agents
    }

    protected function process(array $input, string $sessionId): array
    {
        $intent = $input['intent'] ?? 'general';
        $entities = $input['entities'] ?? [];
        $llmResponse = $input['llm_response'] ?? '';

        Log::info('Tool Agent processing request', [
            'session_id' => $sessionId,
            'intent' => $intent,
            'entities_count' => count($entities)
        ]);

        // Determine if tools are needed
        $requiredTools = $this->identifyRequiredTools($input);
        
        if (empty($requiredTools)) {
            Log::info('Tool Agent skipped - no tools required', [
                'session_id' => $sessionId
            ]);
            
            return array_merge($input, [
                'tool_executions' => [],
                'tool_metadata' => [
                    'skipped' => true,
                    'reason' => 'no_tools_required'
                ]
            ]);
        }

        try {
            // Execute tools
            $toolResults = $this->executeTools($requiredTools, $input, $sessionId);
            
            // Process tool outputs
            $processedResults = $this->processToolResults($toolResults);

            $result = array_merge($input, [
                'tool_executions' => $toolResults,
                'tool_data' => $processedResults,
                'tool_metadata' => [
                    'tools_executed' => count($toolResults),
                    'successful_executions' => count(array_filter($toolResults, fn($r) => $r['status'] === 'success')),
                    'failed_executions' => count(array_filter($toolResults, fn($r) => $r['status'] === 'error')),
                    'total_execution_time' => array_sum(array_column($toolResults, 'execution_time'))
                ]
            ]);

            Log::info('Tool Agent completed execution', [
                'session_id' => $sessionId,
                'tools_executed' => count($toolResults),
                'successful' => count(array_filter($toolResults, fn($r) => $r['status'] === 'success'))
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Tool Agent failed', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);

            return array_merge($input, [
                'tool_executions' => [],
                'tool_metadata' => [
                    'error' => $e->getMessage(),
                    'fallback_used' => true
                ]
            ]);
        }
    }

    protected function identifyRequiredTools(array $input): array
    {
        $intent = $input['intent'] ?? 'general';
        $entities = $input['entities'] ?? [];
        $message = $input['message'] ?? '';
        
        $requiredTools = [];

        // Weather tool
        if ($this->needsWeatherTool($message, $entities)) {
            $requiredTools[] = [
                'name' => 'weather',
                'params' => $this->extractWeatherParams($entities)
            ];
        }

        // Search tool
        if ($this->needsSearchTool($intent, $message)) {
            $requiredTools[] = [
                'name' => 'web_search',
                'params' => $this->extractSearchParams($message)
            ];
        }

        // Email tool
        if ($this->needsEmailTool($message, $entities)) {
            $requiredTools[] = [
                'name' => 'email',
                'params' => $this->extractEmailParams($entities)
            ];
        }

        // Calendar tool
        if ($this->needsCalendarTool($message, $entities)) {
            $requiredTools[] = [
                'name' => 'calendar',
                'params' => $this->extractCalendarParams($entities)
            ];
        }

        // Custom API tools
        $customTools = $this->identifyCustomApiTools($message, $entities);
        $requiredTools = array_merge($requiredTools, $customTools);

        return $requiredTools;
    }

    protected function executeTools(array $tools, array $input, string $sessionId): array
    {
        $results = [];
        $startTime = microtime(true);

        foreach ($tools as $toolConfig) {
            if (microtime(true) - $startTime > $this->maxExecutionTime) {
                Log::warning('Tool execution timeout reached', [
                    'session_id' => $sessionId,
                    'remaining_tools' => count($tools) - count($results)
                ]);
                break;
            }

            $toolStartTime = microtime(true);
            
            try {
                $tool = $this->toolRegistry->getTool($toolConfig['name']);
                
                if (!$tool) {
                    $results[] = [
                        'tool' => $toolConfig['name'],
                        'status' => 'error',
                        'error' => 'Tool not found',
                        'execution_time' => 0
                    ];
                    continue;
                }

                $result = $tool->execute($toolConfig['params'], $sessionId);
                
                $results[] = [
                    'tool' => $toolConfig['name'],
                    'status' => 'success',
                    'result' => $result,
                    'execution_time' => microtime(true) - $toolStartTime,
                    'params' => $toolConfig['params']
                ];

            } catch (\Exception $e) {
                $results[] = [
                    'tool' => $toolConfig['name'],
                    'status' => 'error',
                    'error' => $e->getMessage(),
                    'execution_time' => microtime(true) - $toolStartTime,
                    'params' => $toolConfig['params']
                ];
            }
        }

        return $results;
    }

    protected function processToolResults(array $toolResults): array
    {
        $processedData = [];

        foreach ($toolResults as $result) {
            if ($result['status'] === 'success') {
                $processedData[$result['tool']] = $result['result'];
            }
        }

        return $processedData;
    }

    protected function needsWeatherTool(string $message, array $entities): bool
    {
        $weatherKeywords = ['weather', 'temperature', 'forecast', 'rain', 'sunny', 'cloudy', 'storm'];
        $message = strtolower($message);
        
        foreach ($weatherKeywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return true;
            }
        }
        
        return false;
    }

    protected function needsSearchTool(string $intent, string $message): bool
    {
        return $intent === 'search' || 
               strpos(strtolower($message), 'search for') !== false ||
               strpos(strtolower($message), 'look up') !== false;
    }

    protected function needsEmailTool(string $message, array $entities): bool
    {
        return !empty($entities['emails']) || 
               strpos(strtolower($message), 'send email') !== false ||
               strpos(strtolower($message), 'email') !== false;
    }

    protected function needsCalendarTool(string $message, array $entities): bool
    {
        return !empty($entities['dates']) || 
               strpos(strtolower($message), 'schedule') !== false ||
               strpos(strtolower($message), 'calendar') !== false ||
               strpos(strtolower($message), 'meeting') !== false;
    }

    protected function extractWeatherParams(array $entities): array
    {
        return [
            'location' => $this->extractLocation($entities),
            'date' => $entities['dates'][0] ?? 'today'
        ];
    }

    protected function extractSearchParams(string $message): array
    {
        // Extract search query from message
        $query = preg_replace('/^(search for|look up|find)\s+/i', '', $message);
        return ['query' => trim($query)];
    }

    protected function extractEmailParams(array $entities): array
    {
        return [
            'recipients' => $entities['emails'] ?? [],
            'subject' => 'Generated by AI Assistant',
            'body' => ''
        ];
    }

    protected function extractCalendarParams(array $entities): array
    {
        return [
            'date' => $entities['dates'][0] ?? 'today',
            'time' => $this->extractTime($entities),
            'duration' => 60 // default 1 hour
        ];
    }

    protected function extractLocation(array $entities): string
    {
        // Simple location extraction - can be enhanced
        return 'current_location';
    }

    protected function extractTime(array $entities): string
    {
        // Simple time extraction - can be enhanced
        return '10:00';
    }

    protected function identifyCustomApiTools(string $message, array $entities): array
    {
        // Check for custom API integrations
        $customTools = [];
        
        // This would check against configured API connections
        // For now, return empty array
        
        return $customTools;
    }

    public function validateInput(array $input): bool
    {
        return isset($input['message']) && 
               isset($input['intent']) && 
               is_string($input['message']);
    }
}
