<?php

declare(strict_types=1);

namespace App\Services\Tools\Built;

use App\Services\Tools\AbstractTool;

class WeatherTool extends AbstractTool
{
    protected array $requiredParams = ['location'];
    protected array $optionalParams = ['date', 'units'];

    public function getName(): string
    {
        return 'weather';
    }

    public function getDescription(): string
    {
        return 'Get weather information for a specific location and date';
    }

    protected function initialize(): void
    {
        // Set default configuration
        $this->config = array_merge([
            'api_key' => env('WEATHER_API_KEY'),
            'base_url' => 'https://api.openweathermap.org/data/2.5',
            'units' => 'metric'
        ], $this->config);
    }

    public function isAvailable(): bool
    {
        return !empty($this->config['api_key']);
    }

    protected function process(array $params, string $sessionId): array
    {
        $location = $params['location'];
        $units = $params['units'] ?? $this->config['units'];
        
        // For demo purposes, return mock data if no API key
        if (!$this->isAvailable()) {
            return $this->getMockWeatherData($location);
        }

        try {
            $url = $this->config['base_url'] . '/weather';
            $response = $this->makeHttpRequest($url, [
                'q' => $location,
                'appid' => $this->config['api_key'],
                'units' => $units
            ]);

            return $this->formatResponse([
                'location' => $response['name'] ?? $location,
                'temperature' => $response['main']['temp'] ?? 0,
                'description' => $response['weather'][0]['description'] ?? 'Unknown',
                'humidity' => $response['main']['humidity'] ?? 0,
                'wind_speed' => $response['wind']['speed'] ?? 0,
                'units' => $units
            ], "Weather information for {$location}");

        } catch (\Exception $e) {
            return $this->getMockWeatherData($location);
        }
    }

    protected function getMockWeatherData(string $location): array
    {
        return $this->formatResponse([
            'location' => $location,
            'temperature' => 22,
            'description' => 'Partly cloudy',
            'humidity' => 65,
            'wind_speed' => 5.2,
            'units' => 'metric',
            'mock' => true
        ], "Mock weather data for {$location}");
    }
}
