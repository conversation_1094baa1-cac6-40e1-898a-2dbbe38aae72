<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('document_chunks', function (Blueprint $table) {
            $table->foreignId('knowledge_base_id')->nullable()->after('document_id')->constrained()->onDelete('cascade');
            $table->string('chunk_type')->default('text')->after('content'); // text, table, image, code
            $table->json('metadata')->nullable()->after('chunk_type');
            $table->integer('token_count')->nullable()->after('metadata');
            $table->string('embedding_status')->default('pending')->after('embedding'); // pending, processing, completed, failed
            $table->text('embedding_error')->nullable()->after('embedding_status');
            $table->timestamp('embedded_at')->nullable()->after('embedding_error');
            
            $table->index(['knowledge_base_id', 'embedding_status']);
            $table->index(['document_id', 'chunk_index']);
            $table->index(['embedding_status', 'created_at']);
        });
    }

    public function down(): void
    {
        Schema::table('document_chunks', function (Blueprint $table) {
            $table->dropForeign(['knowledge_base_id']);
            $table->dropColumn([
                'knowledge_base_id',
                'chunk_type',
                'metadata',
                'token_count',
                'embedding_status',
                'embedding_error',
                'embedded_at'
            ]);
        });
    }
};
