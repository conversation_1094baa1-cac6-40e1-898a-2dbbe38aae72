'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { useApi } from '@/contexts/AuthContext'
import { 
  Palette,
  Settings,
  Code,
  Eye,
  Save,
  ArrowLeft,
  Copy,
  Download,
  Smartphone,
  Monitor,
  Tablet
} from 'lucide-react'

interface Widget {
  id: number
  widget_id: string
  name: string
  description: string
  status: string
  embed_code: string
  configuration: any
  styling: any
  behavior: any
}

export default function WidgetCustomizePage() {
  const params = useParams()
  const router = useRouter()
  const { apiCall } = useApi()
  const [widget, setWidget] = useState<Widget | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  
  // Configuration state
  const [config, setConfig] = useState({
    title: '',
    placeholder: '',
    welcome_message: '',
    theme: 'light',
    position: 'bottom-right',
    auto_open: false,
    show_branding: true,
    enable_file_upload: true,
    max_file_size: 10,
    allowed_file_types: ['pdf', 'doc', 'txt']
  })

  // Styling state
  const [styling, setStyling] = useState({
    primary_color: '#3b82f6',
    secondary_color: '#64748b',
    text_color: '#1f2937',
    background_color: '#ffffff',
    border_radius: 8,
    font_family: 'Inter',
    font_size: 14,
    button_style: 'rounded',
    animation: 'slide',
    shadow: true,
    width: 400,
    height: 600
  })

  // Behavior state
  const [behavior, setBehavior] = useState({
    auto_open_delay: 3000,
    close_on_outside_click: true,
    remember_conversation: true,
    typing_indicator: true,
    sound_notifications: false,
    keyboard_shortcuts: true,
    rate_limiting: true,
    max_messages_per_hour: 50
  })

  const fetchWidget = async () => {
    setIsLoading(true)
    try {
      const response = await apiCall(`/widgets/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          const widgetData = data.data
          setWidget(widgetData)
          
          // Update state with existing configuration
          if (widgetData.configuration) {
            setConfig(prev => ({ ...prev, ...widgetData.configuration }))
          }
          if (widgetData.styling) {
            setStyling(prev => ({ ...prev, ...widgetData.styling }))
          }
          if (widgetData.behavior) {
            setBehavior(prev => ({ ...prev, ...widgetData.behavior }))
          }
        }
      }
    } catch (error) {
      console.error('Failed to fetch widget:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const saveWidget = async () => {
    if (!widget) return

    setIsSaving(true)
    try {
      const response = await apiCall(`/widgets/${widget.id}`, {
        method: 'PUT',
        body: JSON.stringify({
          configuration: config,
          styling: styling,
          behavior: behavior
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setWidget(data.data)
          // Show success message
        }
      }
    } catch (error) {
      console.error('Failed to save widget:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const copyEmbedCode = () => {
    if (widget?.embed_code) {
      navigator.clipboard.writeText(widget.embed_code)
      // Show success toast
    }
  }

  const generateEmbedCode = () => {
    if (!widget) return ''
    
    return `<!-- Axient MCP++ Widget -->
<div id="axient-widget-${widget.widget_id}"></div>
<script>
  (function() {
    var script = document.createElement('script');
    script.src = '${window.location.origin}/widget.js';
    script.async = true;
    script.onload = function() {
      AxientWidget.init({
        widgetId: '${widget.widget_id}',
        apiUrl: '${window.location.origin}/api',
        config: ${JSON.stringify(config, null, 2)},
        styling: ${JSON.stringify(styling, null, 2)},
        behavior: ${JSON.stringify(behavior, null, 2)}
      });
    };
    document.head.appendChild(script);
  })();
</script>`
  }

  useEffect(() => {
    fetchWidget()
  }, [params.id])

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </DashboardLayout>
    )
  }

  if (!widget) {
    return (
      <DashboardLayout>
        <div className="text-center py-8">
          <p className="text-gray-500">Widget not found</p>
          <Button onClick={() => router.push('/widgets')} className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Widgets
          </Button>
        </div>
      </DashboardLayout>
    )
  }

  const getPreviewDimensions = () => {
    switch (previewMode) {
      case 'mobile': return { width: '375px', height: '667px' }
      case 'tablet': return { width: '768px', height: '1024px' }
      default: return { width: '1200px', height: '800px' }
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => router.push('/widgets')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Customize Widget</h1>
              <p className="text-gray-600">{widget.name}</p>
            </div>
            <Badge variant={widget.status === 'active' ? 'success' : 'secondary'}>
              {widget.status}
            </Badge>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={copyEmbedCode}>
              <Copy className="h-4 w-4 mr-2" />
              Copy Embed Code
            </Button>
            <Button onClick={saveWidget} disabled={isSaving}>
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Configuration Panel */}
          <div className="space-y-6">
            <Tabs defaultValue="configuration" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="configuration">
                  <Settings className="h-4 w-4 mr-2" />
                  Config
                </TabsTrigger>
                <TabsTrigger value="styling">
                  <Palette className="h-4 w-4 mr-2" />
                  Styling
                </TabsTrigger>
                <TabsTrigger value="behavior">
                  <Settings className="h-4 w-4 mr-2" />
                  Behavior
                </TabsTrigger>
              </TabsList>

              <TabsContent value="configuration" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Basic Configuration</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="title">Widget Title</Label>
                      <Input
                        id="title"
                        value={config.title}
                        onChange={(e) => setConfig(prev => ({ ...prev, title: e.target.value }))}
                        placeholder="Chat with us"
                      />
                    </div>
                    <div>
                      <Label htmlFor="placeholder">Input Placeholder</Label>
                      <Input
                        id="placeholder"
                        value={config.placeholder}
                        onChange={(e) => setConfig(prev => ({ ...prev, placeholder: e.target.value }))}
                        placeholder="Type your message..."
                      />
                    </div>
                    <div>
                      <Label htmlFor="welcome">Welcome Message</Label>
                      <Textarea
                        id="welcome"
                        value={config.welcome_message}
                        onChange={(e) => setConfig(prev => ({ ...prev, welcome_message: e.target.value }))}
                        placeholder="Hello! How can I help you today?"
                        rows={3}
                      />
                    </div>
                    <div>
                      <Label htmlFor="theme">Theme</Label>
                      <select
                        id="theme"
                        value={config.theme}
                        onChange={(e) => setConfig(prev => ({ ...prev, theme: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="light">Light</option>
                        <option value="dark">Dark</option>
                        <option value="auto">Auto</option>
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="position">Position</Label>
                      <select
                        id="position"
                        value={config.position}
                        onChange={(e) => setConfig(prev => ({ ...prev, position: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="bottom-right">Bottom Right</option>
                        <option value="bottom-left">Bottom Left</option>
                        <option value="top-right">Top Right</option>
                        <option value="top-left">Top Left</option>
                      </select>
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="auto-open">Auto Open</Label>
                      <Switch
                        id="auto-open"
                        checked={config.auto_open}
                        onCheckedChange={(checked) => setConfig(prev => ({ ...prev, auto_open: checked }))}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="show-branding">Show Branding</Label>
                      <Switch
                        id="show-branding"
                        checked={config.show_branding}
                        onCheckedChange={(checked) => setConfig(prev => ({ ...prev, show_branding: checked }))}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="file-upload">Enable File Upload</Label>
                      <Switch
                        id="file-upload"
                        checked={config.enable_file_upload}
                        onCheckedChange={(checked) => setConfig(prev => ({ ...prev, enable_file_upload: checked }))}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="styling" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Visual Styling</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="primary-color">Primary Color</Label>
                        <div className="flex gap-2">
                          <Input
                            id="primary-color"
                            type="color"
                            value={styling.primary_color}
                            onChange={(e) => setStyling(prev => ({ ...prev, primary_color: e.target.value }))}
                            className="w-16 h-10"
                          />
                          <Input
                            value={styling.primary_color}
                            onChange={(e) => setStyling(prev => ({ ...prev, primary_color: e.target.value }))}
                            placeholder="#3b82f6"
                          />
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="secondary-color">Secondary Color</Label>
                        <div className="flex gap-2">
                          <Input
                            id="secondary-color"
                            type="color"
                            value={styling.secondary_color}
                            onChange={(e) => setStyling(prev => ({ ...prev, secondary_color: e.target.value }))}
                            className="w-16 h-10"
                          />
                          <Input
                            value={styling.secondary_color}
                            onChange={(e) => setStyling(prev => ({ ...prev, secondary_color: e.target.value }))}
                            placeholder="#64748b"
                          />
                        </div>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="width">Width (px)</Label>
                        <Input
                          id="width"
                          type="number"
                          value={styling.width}
                          onChange={(e) => setStyling(prev => ({ ...prev, width: parseInt(e.target.value) }))}
                          min="300"
                          max="800"
                        />
                      </div>
                      <div>
                        <Label htmlFor="height">Height (px)</Label>
                        <Input
                          id="height"
                          type="number"
                          value={styling.height}
                          onChange={(e) => setStyling(prev => ({ ...prev, height: parseInt(e.target.value) }))}
                          min="400"
                          max="800"
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="border-radius">Border Radius (px)</Label>
                      <Input
                        id="border-radius"
                        type="number"
                        value={styling.border_radius}
                        onChange={(e) => setStyling(prev => ({ ...prev, border_radius: parseInt(e.target.value) }))}
                        min="0"
                        max="20"
                      />
                    </div>
                    <div>
                      <Label htmlFor="font-family">Font Family</Label>
                      <select
                        id="font-family"
                        value={styling.font_family}
                        onChange={(e) => setStyling(prev => ({ ...prev, font_family: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="Inter">Inter</option>
                        <option value="Arial">Arial</option>
                        <option value="Helvetica">Helvetica</option>
                        <option value="Georgia">Georgia</option>
                        <option value="Times New Roman">Times New Roman</option>
                      </select>
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="shadow">Drop Shadow</Label>
                      <Switch
                        id="shadow"
                        checked={styling.shadow}
                        onCheckedChange={(checked) => setStyling(prev => ({ ...prev, shadow: checked }))}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="behavior" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Behavior Settings</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="auto-open-delay">Auto Open Delay (ms)</Label>
                      <Input
                        id="auto-open-delay"
                        type="number"
                        value={behavior.auto_open_delay}
                        onChange={(e) => setBehavior(prev => ({ ...prev, auto_open_delay: parseInt(e.target.value) }))}
                        min="0"
                        max="10000"
                        step="500"
                      />
                    </div>
                    <div>
                      <Label htmlFor="max-messages">Max Messages per Hour</Label>
                      <Input
                        id="max-messages"
                        type="number"
                        value={behavior.max_messages_per_hour}
                        onChange={(e) => setBehavior(prev => ({ ...prev, max_messages_per_hour: parseInt(e.target.value) }))}
                        min="1"
                        max="1000"
                      />
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="close-outside">Close on Outside Click</Label>
                        <Switch
                          id="close-outside"
                          checked={behavior.close_on_outside_click}
                          onCheckedChange={(checked) => setBehavior(prev => ({ ...prev, close_on_outside_click: checked }))}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="remember-conversation">Remember Conversation</Label>
                        <Switch
                          id="remember-conversation"
                          checked={behavior.remember_conversation}
                          onCheckedChange={(checked) => setBehavior(prev => ({ ...prev, remember_conversation: checked }))}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="typing-indicator">Typing Indicator</Label>
                        <Switch
                          id="typing-indicator"
                          checked={behavior.typing_indicator}
                          onCheckedChange={(checked) => setBehavior(prev => ({ ...prev, typing_indicator: checked }))}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="sound-notifications">Sound Notifications</Label>
                        <Switch
                          id="sound-notifications"
                          checked={behavior.sound_notifications}
                          onCheckedChange={(checked) => setBehavior(prev => ({ ...prev, sound_notifications: checked }))}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="keyboard-shortcuts">Keyboard Shortcuts</Label>
                        <Switch
                          id="keyboard-shortcuts"
                          checked={behavior.keyboard_shortcuts}
                          onCheckedChange={(checked) => setBehavior(prev => ({ ...prev, keyboard_shortcuts: checked }))}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="rate-limiting">Rate Limiting</Label>
                        <Switch
                          id="rate-limiting"
                          checked={behavior.rate_limiting}
                          onCheckedChange={(checked) => setBehavior(prev => ({ ...prev, rate_limiting: checked }))}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Preview Panel */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <Eye className="h-5 w-5" />
                    Live Preview
                  </span>
                  <div className="flex gap-1">
                    <Button
                      variant={previewMode === 'desktop' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setPreviewMode('desktop')}
                    >
                      <Monitor className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={previewMode === 'tablet' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setPreviewMode('tablet')}
                    >
                      <Tablet className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={previewMode === 'mobile' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setPreviewMode('mobile')}
                    >
                      <Smartphone className="h-4 w-4" />
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div 
                  className="border rounded-lg overflow-hidden mx-auto transition-all duration-300"
                  style={getPreviewDimensions()}
                >
                  <iframe
                    src={`/widget/preview/${widget.widget_id}?config=${encodeURIComponent(JSON.stringify(config))}&styling=${encodeURIComponent(JSON.stringify(styling))}&behavior=${encodeURIComponent(JSON.stringify(behavior))}`}
                    className="w-full h-full border-0"
                    title="Widget Preview"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Embed Code */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Code className="h-5 w-5" />
                  Embed Code
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative">
                  <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto">
                    <code>{generateEmbedCode()}</code>
                  </pre>
                  <Button
                    variant="outline"
                    size="sm"
                    className="absolute top-2 right-2"
                    onClick={copyEmbedCode}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
