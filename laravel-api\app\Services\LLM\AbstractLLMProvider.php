<?php

declare(strict_types=1);

namespace App\Services\LLM;

use App\Contracts\LLMProviderInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

abstract class Abstract<PERSON><PERSON>rovider implements LLMProviderInterface
{
    protected array $config = [];
    protected string $model = '';
    protected string $apiKey = '';
    protected string $baseUrl = '';
    protected int $timeout = 30;
    protected int $maxRetries = 3;

    public function __construct(array $config = [])
    {
        $this->config = $config;
        $this->initialize();
    }

    /**
     * Initialize the provider with configuration
     */
    abstract protected function initialize(): void;

    /**
     * Get the API endpoint for chat completions
     */
    abstract protected function getChatEndpoint(): string;

    /**
     * Format the request payload for this provider
     */
    abstract protected function formatRequest(string $prompt, array $options): array;

    /**
     * Parse the response from this provider
     */
    abstract protected function parseResponse(array $response): array;

    public function generateResponse(string $prompt, array $options = []): array
    {
        $startTime = microtime(true);
        
        try {
            $payload = $this->formatRequest($prompt, $options);
            $response = $this->makeRequest($payload);
            $parsed = $this->parseResponse($response);
            
            $parsed['processing_time'] = microtime(true) - $startTime;
            
            return $parsed;

        } catch (\Exception $e) {
            Log::error("LLM Provider {$this->getName()} failed", [
                'error' => $e->getMessage(),
                'provider' => $this->getName(),
                'model' => $this->getModel()
            ]);
            
            throw $e;
        }
    }

    protected function makeRequest(array $payload): array
    {
        $attempt = 0;
        $lastException = null;

        while ($attempt < $this->maxRetries) {
            try {
                $response = Http::withHeaders($this->getHeaders())
                    ->timeout($this->timeout)
                    ->post($this->getChatEndpoint(), $payload);

                if ($response->successful()) {
                    return $response->json();
                }

                throw new \Exception("HTTP {$response->status()}: {$response->body()}");

            } catch (\Exception $e) {
                $lastException = $e;
                $attempt++;
                
                if ($attempt < $this->maxRetries) {
                    sleep(pow(2, $attempt)); // Exponential backoff
                }
            }
        }

        throw $lastException;
    }

    protected function getHeaders(): array
    {
        return [
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json',
            'User-Agent' => 'Axient-MCP/1.0',
        ];
    }

    public function getModel(): string
    {
        return $this->model;
    }

    public function setModel(string $model): void
    {
        $this->model = $model;
    }

    public function getConfig(): array
    {
        return $this->config;
    }

    public function setConfig(array $config): void
    {
        $this->config = $config;
        $this->initialize();
    }

    public function isAvailable(): bool
    {
        return !empty($this->apiKey) && $this->validateConfig();
    }

    public function validateConfig(): bool
    {
        return !empty($this->apiKey) && !empty($this->model);
    }

    public function generateStreamingResponse(string $prompt, array $options = []): \Generator
    {
        // Default implementation - can be overridden by providers that support streaming
        $response = $this->generateResponse($prompt, $options);
        yield $response;
    }
}
