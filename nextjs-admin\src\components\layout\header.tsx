'use client'

import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { apiClient } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { useClientTime } from '@/hooks/useClientTime'
import { Bell, RefreshCw, User, LogOut, Settings } from 'lucide-react'

export function Header() {
  const { user, tenant, logout } = useAuth()
  const [systemStatus, setSystemStatus] = useState<'operational' | 'degraded' | 'down'>('operational')
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [showUserMenu, setShowUserMenu] = useState(false)
  const { formatTime, updateTime } = useClientTime()

  const checkSystemStatus = async () => {
    setIsRefreshing(true)
    try {
      const response = await apiClient.getSystemStatus()
      if (response.success && response.data) {
        setSystemStatus(response.data.system_status === 'operational' ? 'operational' : 'degraded')
      } else {
        setSystemStatus('down')
      }
    } catch (error) {
      setSystemStatus('down')
    } finally {
      setIsRefreshing(false)
      updateTime()
    }
  }

  useEffect(() => {
    checkSystemStatus()
    const interval = setInterval(checkSystemStatus, 30000) // Check every 30 seconds
    return () => clearInterval(interval)
  }, [])

  const getStatusVariant = () => {
    switch (systemStatus) {
      case 'operational':
        return 'success'
      case 'degraded':
        return 'warning'
      case 'down':
        return 'error'
      default:
        return 'secondary'
    }
  }

  return (
    <header className="border-b bg-white px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h2 className="text-2xl font-bold text-gray-900">MCP Dashboard</h2>
          <Badge variant={getStatusVariant()}>
            {systemStatus.charAt(0).toUpperCase() + systemStatus.slice(1)}
          </Badge>
        </div>

        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-500">
            Last updated: {formatTime()}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={checkSystemStatus}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>

          <Button variant="outline" size="icon">
            <Bell className="h-4 w-4" />
          </Button>

          {/* User Menu */}
          <div className="relative">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center space-x-2"
            >
              <User className="h-4 w-4" />
              <span className="hidden md:inline">{user?.name}</span>
            </Button>

            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg border border-gray-200 z-50">
                <div className="p-4 border-b border-gray-200">
                  <div className="font-medium text-gray-900">{user?.name}</div>
                  <div className="text-sm text-gray-500">{user?.email}</div>
                  {tenant && (
                    <div className="text-xs text-gray-400 mt-1">
                      {tenant.name} ({tenant.subscription_plan})
                    </div>
                  )}
                </div>
                <div className="py-2">
                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => setShowUserMenu(false)}
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Settings
                  </button>
                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                    onClick={() => {
                      setShowUserMenu(false)
                      logout()
                    }}
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Sign Out
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}
