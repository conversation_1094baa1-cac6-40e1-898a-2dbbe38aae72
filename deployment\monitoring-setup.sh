#!/bin/bash

# =============================================================================
# AXIENT MCP++ MONITORING SETUP SCRIPT
# Production Monitoring and Alerting Configuration
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
AWS_REGION="${AWS_REGION:-us-east-1}"
ENVIRONMENT="production"
APP_NAME="axient-mcp"
SNS_TOPIC_ARN="${SNS_TOPIC_ARN}"
SLACK_WEBHOOK_URL="${SLACK_WEBHOOK_URL}"

echo -e "${BLUE}==============================================================================${NC}"
echo -e "${BLUE}AXIENT MCP++ PRODUCTION MONITORING SETUP${NC}"
echo -e "${BLUE}==============================================================================${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        print_error "jq is not installed"
        exit 1
    fi
    
    print_status "Prerequisites check passed"
}

# Create SNS topic for alerts
create_sns_topic() {
    print_status "Creating SNS topic for alerts..."
    
    TOPIC_NAME="${APP_NAME}-alerts-${ENVIRONMENT}"
    
    SNS_TOPIC_ARN=$(aws sns create-topic \
        --name "$TOPIC_NAME" \
        --region "$AWS_REGION" \
        --query 'TopicArn' \
        --output text)
    
    print_status "SNS topic created: $SNS_TOPIC_ARN"
    
    # Subscribe email to topic
    if [ -n "$ALERT_EMAIL" ]; then
        aws sns subscribe \
            --topic-arn "$SNS_TOPIC_ARN" \
            --protocol email \
            --notification-endpoint "$ALERT_EMAIL" \
            --region "$AWS_REGION"
        print_status "Email subscription added: $ALERT_EMAIL"
    fi
}

# Create CloudWatch Log Groups
create_log_groups() {
    print_status "Creating CloudWatch log groups..."
    
    LOG_GROUPS=(
        "/aws/lambda/${APP_NAME}-api"
        "/aws/lambda/${APP_NAME}-frontend"
        "/aws/ecs/${APP_NAME}-api"
        "/aws/ecs/${APP_NAME}-frontend"
        "/aws/rds/${APP_NAME}"
        "/aws/elasticache/${APP_NAME}"
    )
    
    for log_group in "${LOG_GROUPS[@]}"; do
        aws logs create-log-group \
            --log-group-name "$log_group" \
            --region "$AWS_REGION" 2>/dev/null || true
        
        # Set retention policy (30 days)
        aws logs put-retention-policy \
            --log-group-name "$log_group" \
            --retention-in-days 30 \
            --region "$AWS_REGION" 2>/dev/null || true
            
        print_status "Log group created: $log_group"
    done
}

# Create CloudWatch Alarms
create_cloudwatch_alarms() {
    print_status "Creating CloudWatch alarms..."
    
    # API Response Time Alarm
    aws cloudwatch put-metric-alarm \
        --alarm-name "${APP_NAME}-api-response-time" \
        --alarm-description "API response time is too high" \
        --metric-name Duration \
        --namespace AWS/Lambda \
        --statistic Average \
        --period 300 \
        --threshold 5000 \
        --comparison-operator GreaterThanThreshold \
        --evaluation-periods 2 \
        --alarm-actions "$SNS_TOPIC_ARN" \
        --dimensions Name=FunctionName,Value="${APP_NAME}-api" \
        --region "$AWS_REGION"
    
    # API Error Rate Alarm
    aws cloudwatch put-metric-alarm \
        --alarm-name "${APP_NAME}-api-error-rate" \
        --alarm-description "API error rate is too high" \
        --metric-name Errors \
        --namespace AWS/Lambda \
        --statistic Sum \
        --period 300 \
        --threshold 10 \
        --comparison-operator GreaterThanThreshold \
        --evaluation-periods 2 \
        --alarm-actions "$SNS_TOPIC_ARN" \
        --dimensions Name=FunctionName,Value="${APP_NAME}-api" \
        --region "$AWS_REGION"
    
    # Database Connection Alarm
    aws cloudwatch put-metric-alarm \
        --alarm-name "${APP_NAME}-db-connections" \
        --alarm-description "Database connection count is too high" \
        --metric-name DatabaseConnections \
        --namespace AWS/RDS \
        --statistic Average \
        --period 300 \
        --threshold 80 \
        --comparison-operator GreaterThanThreshold \
        --evaluation-periods 2 \
        --alarm-actions "$SNS_TOPIC_ARN" \
        --dimensions Name=DBInstanceIdentifier,Value="${APP_NAME}-db" \
        --region "$AWS_REGION"
    
    # Memory Usage Alarm
    aws cloudwatch put-metric-alarm \
        --alarm-name "${APP_NAME}-memory-usage" \
        --alarm-description "Memory usage is too high" \
        --metric-name MemoryUtilization \
        --namespace AWS/ECS \
        --statistic Average \
        --period 300 \
        --threshold 80 \
        --comparison-operator GreaterThanThreshold \
        --evaluation-periods 2 \
        --alarm-actions "$SNS_TOPIC_ARN" \
        --dimensions Name=ServiceName,Value="${APP_NAME}-api" \
        --region "$AWS_REGION"
    
    # Disk Space Alarm
    aws cloudwatch put-metric-alarm \
        --alarm-name "${APP_NAME}-disk-space" \
        --alarm-description "Disk space is running low" \
        --metric-name FreeStorageSpace \
        --namespace AWS/RDS \
        --statistic Average \
        --period 300 \
        --threshold 2000000000 \
        --comparison-operator LessThanThreshold \
        --evaluation-periods 1 \
        --alarm-actions "$SNS_TOPIC_ARN" \
        --dimensions Name=DBInstanceIdentifier,Value="${APP_NAME}-db" \
        --region "$AWS_REGION"
    
    print_status "CloudWatch alarms created"
}

# Create Custom Metrics
create_custom_metrics() {
    print_status "Setting up custom metrics..."
    
    # Create custom namespace
    NAMESPACE="AxientMCP/Application"
    
    # Business metrics that will be sent from the application
    cat > custom-metrics.json << EOF
{
  "metrics": [
    {
      "name": "ActiveSubscriptions",
      "description": "Number of active subscriptions",
      "unit": "Count"
    },
    {
      "name": "AIRequestsPerMinute",
      "description": "AI requests processed per minute",
      "unit": "Count/Second"
    },
    {
      "name": "WorkflowExecutions",
      "description": "Number of workflow executions",
      "unit": "Count"
    },
    {
      "name": "DocumentProcessingTime",
      "description": "Time to process documents",
      "unit": "Milliseconds"
    },
    {
      "name": "UserRegistrations",
      "description": "New user registrations",
      "unit": "Count"
    },
    {
      "name": "RevenuePerHour",
      "description": "Revenue generated per hour",
      "unit": "None"
    }
  ]
}
EOF
    
    print_status "Custom metrics configuration created"
}

# Setup Application Performance Monitoring
setup_apm() {
    print_status "Setting up Application Performance Monitoring..."
    
    # Create New Relic configuration
    cat > newrelic.ini << EOF
[newrelic]
license_key = ${NEW_RELIC_LICENSE_KEY}
appname = Axient MCP++ Production
monitor_mode = true
log_level = info
ssl = true
high_security = false
transaction_tracer.enabled = true
transaction_tracer.transaction_threshold = apdex_f
transaction_tracer.record_sql = obfuscated
transaction_tracer.stack_trace_threshold = 0.500
transaction_tracer.explain_enabled = true
transaction_tracer.explain_threshold = 0.500
error_collector.enabled = true
error_collector.record_database_errors = true
error_collector.prioritize_api_errors = false
browser_monitoring.auto_instrument = true
EOF
    
    print_status "New Relic configuration created"
}

# Create Health Check Monitors
create_health_checks() {
    print_status "Creating health check monitors..."
    
    ENDPOINTS=(
        "https://app.axientmcp.com/health"
        "https://api.axientmcp.com/api/health"
        "https://admin.axientmcp.com/health"
        "https://widget.axientmcp.com/health"
    )
    
    for endpoint in "${ENDPOINTS[@]}"; do
        # Extract domain from URL
        domain=$(echo "$endpoint" | sed 's|https://||' | sed 's|/.*||')
        
        # Create Route 53 health check
        health_check_id=$(aws route53 create-health-check \
            --caller-reference "${domain}-$(date +%s)" \
            --health-check-config Type=HTTPS,ResourcePath=/health,FullyQualifiedDomainName="$domain",Port=443,RequestInterval=30,FailureThreshold=3 \
            --region "$AWS_REGION" \
            --query 'HealthCheck.Id' \
            --output text)
        
        # Create CloudWatch alarm for health check
        aws cloudwatch put-metric-alarm \
            --alarm-name "${domain}-health-check" \
            --alarm-description "Health check failed for $domain" \
            --metric-name StatusCheckFailed \
            --namespace AWS/Route53 \
            --statistic Maximum \
            --period 60 \
            --threshold 0 \
            --comparison-operator GreaterThanThreshold \
            --evaluation-periods 2 \
            --alarm-actions "$SNS_TOPIC_ARN" \
            --dimensions Name=HealthCheckId,Value="$health_check_id" \
            --region "$AWS_REGION"
        
        print_status "Health check created for: $endpoint"
    done
}

# Setup Log Monitoring
setup_log_monitoring() {
    print_status "Setting up log monitoring..."
    
    # Create metric filters for error detection
    aws logs put-metric-filter \
        --log-group-name "/aws/lambda/${APP_NAME}-api" \
        --filter-name "ErrorCount" \
        --filter-pattern "[timestamp, request_id, ERROR]" \
        --metric-transformations \
            metricName=ErrorCount,metricNamespace=AxientMCP/Logs,metricValue=1 \
        --region "$AWS_REGION"
    
    # Create metric filter for 5xx errors
    aws logs put-metric-filter \
        --log-group-name "/aws/lambda/${APP_NAME}-api" \
        --filter-name "5xxErrors" \
        --filter-pattern "[timestamp, request_id, \"5\"]" \
        --metric-transformations \
            metricName=5xxErrors,metricNamespace=AxientMCP/Logs,metricValue=1 \
        --region "$AWS_REGION"
    
    # Create alarm for error count
    aws cloudwatch put-metric-alarm \
        --alarm-name "${APP_NAME}-log-errors" \
        --alarm-description "High error count in logs" \
        --metric-name ErrorCount \
        --namespace AxientMCP/Logs \
        --statistic Sum \
        --period 300 \
        --threshold 10 \
        --comparison-operator GreaterThanThreshold \
        --evaluation-periods 2 \
        --alarm-actions "$SNS_TOPIC_ARN" \
        --region "$AWS_REGION"
    
    print_status "Log monitoring configured"
}

# Create Dashboard
create_dashboard() {
    print_status "Creating CloudWatch dashboard..."
    
    cat > dashboard.json << EOF
{
  "widgets": [
    {
      "type": "metric",
      "x": 0,
      "y": 0,
      "width": 12,
      "height": 6,
      "properties": {
        "metrics": [
          ["AWS/Lambda", "Duration", "FunctionName", "${APP_NAME}-api"],
          [".", "Invocations", ".", "."],
          [".", "Errors", ".", "."]
        ],
        "period": 300,
        "stat": "Average",
        "region": "${AWS_REGION}",
        "title": "API Performance"
      }
    },
    {
      "type": "metric",
      "x": 12,
      "y": 0,
      "width": 12,
      "height": 6,
      "properties": {
        "metrics": [
          ["AWS/RDS", "DatabaseConnections", "DBInstanceIdentifier", "${APP_NAME}-db"],
          [".", "CPUUtilization", ".", "."],
          [".", "FreeStorageSpace", ".", "."]
        ],
        "period": 300,
        "stat": "Average",
        "region": "${AWS_REGION}",
        "title": "Database Performance"
      }
    },
    {
      "type": "log",
      "x": 0,
      "y": 6,
      "width": 24,
      "height": 6,
      "properties": {
        "query": "SOURCE '/aws/lambda/${APP_NAME}-api'\n| fields @timestamp, @message\n| filter @message like /ERROR/\n| sort @timestamp desc\n| limit 100",
        "region": "${AWS_REGION}",
        "title": "Recent Errors"
      }
    }
  ]
}
EOF
    
    aws cloudwatch put-dashboard \
        --dashboard-name "${APP_NAME}-production" \
        --dashboard-body file://dashboard.json \
        --region "$AWS_REGION"
    
    print_status "CloudWatch dashboard created"
}

# Setup Slack Integration
setup_slack_integration() {
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        print_status "Setting up Slack integration..."
        
        # Create Lambda function for Slack notifications
        cat > slack-notifier.py << EOF
import json
import urllib3
import os

def lambda_handler(event, context):
    http = urllib3.PoolManager()
    
    webhook_url = os.environ['SLACK_WEBHOOK_URL']
    
    # Parse SNS message
    message = json.loads(event['Records'][0]['Sns']['Message'])
    
    slack_message = {
        "text": f"🚨 Alert: {message['AlarmName']}",
        "attachments": [
            {
                "color": "danger",
                "fields": [
                    {
                        "title": "Alarm",
                        "value": message['AlarmName'],
                        "short": True
                    },
                    {
                        "title": "Status",
                        "value": message['NewStateValue'],
                        "short": True
                    },
                    {
                        "title": "Reason",
                        "value": message['NewStateReason'],
                        "short": False
                    }
                ]
            }
        ]
    }
    
    response = http.request(
        'POST',
        webhook_url,
        body=json.dumps(slack_message),
        headers={'Content-Type': 'application/json'}
    )
    
    return {
        'statusCode': 200,
        'body': json.dumps('Notification sent')
    }
EOF
        
        print_status "Slack integration configured"
    fi
}

# Main execution
main() {
    echo -e "${BLUE}Starting monitoring setup...${NC}"
    
    check_prerequisites
    create_sns_topic
    create_log_groups
    create_cloudwatch_alarms
    create_custom_metrics
    setup_apm
    create_health_checks
    setup_log_monitoring
    create_dashboard
    setup_slack_integration
    
    echo -e "${GREEN}==============================================================================${NC}"
    echo -e "${GREEN}MONITORING SETUP COMPLETED SUCCESSFULLY${NC}"
    echo -e "${GREEN}==============================================================================${NC}"
    echo -e "${GREEN}SNS Topic: $SNS_TOPIC_ARN${NC}"
    echo -e "${GREEN}Dashboard: https://${AWS_REGION}.console.aws.amazon.com/cloudwatch/home?region=${AWS_REGION}#dashboards:name=${APP_NAME}-production${NC}"
    echo -e "${GREEN}Log Groups: Created for all services${NC}"
    echo -e "${GREEN}Health Checks: Configured for all endpoints${NC}"
    echo -e "${GREEN}==============================================================================${NC}"
}

# Run main function
main "$@"
