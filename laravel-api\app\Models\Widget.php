<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Widget extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'widget_id',
        'description',
        'configuration',
        'styling',
        'behavior',
        'status',
        'embed_code',
        'usage_count',
        'last_used_at'
    ];

    protected $casts = [
        'configuration' => 'array',
        'styling' => 'array',
        'behavior' => 'array',
        'usage_count' => 'integer',
        'last_used_at' => 'datetime'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($widget) {
            if (empty($widget->widget_id)) {
                $widget->widget_id = 'widget_' . Str::random(12);
            }
            
            // Generate embed code
            $widget->embed_code = $widget->generateEmbedCode();
        });

        static::updating(function ($widget) {
            // Regenerate embed code if configuration changes
            if ($widget->isDirty(['configuration', 'styling', 'behavior'])) {
                $widget->embed_code = $widget->generateEmbedCode();
            }
        });
    }

    /**
     * Get default configuration
     */
    public function getDefaultConfiguration(): array
    {
        return [
            'title' => 'AI Assistant',
            'welcome_message' => 'Hello! How can I help you today?',
            'placeholder' => 'Type your message...',
            'enabled_features' => [
                'file_upload' => false,
                'voice_input' => false,
                'typing_indicator' => true,
                'message_history' => true
            ],
            'max_message_length' => 1000,
            'rate_limit' => [
                'messages_per_minute' => 10,
                'enabled' => true
            ]
        ];
    }

    /**
     * Get default styling
     */
    public function getDefaultStyling(): array
    {
        $tenant = tenant();
        $branding = $tenant ? $tenant->getBranding() : [];

        return [
            'theme' => $branding['widget_theme'] ?? 'light',
            'position' => $branding['widget_position'] ?? 'bottom-right',
            'colors' => [
                'primary' => $branding['primary_color'] ?? '#3B82F6',
                'secondary' => $branding['secondary_color'] ?? '#1F2937',
                'background' => '#FFFFFF',
                'text' => '#1F2937',
                'user_message' => '#3B82F6',
                'bot_message' => '#F3F4F6'
            ],
            'dimensions' => [
                'width' => '400px',
                'height' => '600px',
                'border_radius' => '12px'
            ],
            'fonts' => [
                'family' => 'Inter, system-ui, sans-serif',
                'size' => '14px'
            ],
            'animations' => [
                'enabled' => true,
                'duration' => '300ms'
            ]
        ];
    }

    /**
     * Get default behavior
     */
    public function getDefaultBehavior(): array
    {
        return [
            'auto_open' => false,
            'auto_open_delay' => 3000,
            'minimize_on_click_outside' => true,
            'show_agent_typing' => true,
            'sound_notifications' => false,
            'persist_conversation' => true,
            'conversation_timeout' => 30, // minutes
            'offline_message' => 'We are currently offline. Please leave a message and we will get back to you.',
            'error_message' => 'Sorry, something went wrong. Please try again.',
            'max_retries' => 3
        ];
    }

    /**
     * Generate embed code for the widget
     */
    public function generateEmbedCode(): string
    {
        $config = array_merge($this->getDefaultConfiguration(), $this->configuration ?? []);
        $styling = array_merge($this->getDefaultStyling(), $this->styling ?? []);
        $behavior = array_merge($this->getDefaultBehavior(), $this->behavior ?? []);

        $widgetConfig = [
            'widgetId' => $this->widget_id,
            'apiUrl' => config('app.url') . '/api',
            'config' => $config,
            'styling' => $styling,
            'behavior' => $behavior
        ];

        $configJson = json_encode($widgetConfig, JSON_UNESCAPED_SLASHES);

        return <<<HTML
<!-- Axient AI Widget -->
<div id="axient-widget-{$this->widget_id}"></div>
<script>
(function() {
    var config = {$configJson};
    var script = document.createElement('script');
    script.src = '{$this->getWidgetScriptUrl()}';
    script.onload = function() {
        if (window.AxientWidget) {
            window.AxientWidget.init(config);
        }
    };
    document.head.appendChild(script);
})();
</script>
HTML;
    }

    /**
     * Get widget script URL
     */
    protected function getWidgetScriptUrl(): string
    {
        return config('app.url') . '/widget/axient-widget.js';
    }

    /**
     * Increment usage count
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => now()]);
    }

    /**
     * Get configuration value
     */
    public function getConfig(string $key, $default = null)
    {
        $config = array_merge($this->getDefaultConfiguration(), $this->configuration ?? []);
        return data_get($config, $key, $default);
    }

    /**
     * Set configuration value
     */
    public function setConfig(string $key, $value): void
    {
        $config = $this->configuration ?? [];
        data_set($config, $key, $value);
        $this->configuration = $config;
        $this->save();
    }

    /**
     * Get styling value
     */
    public function getStyling(string $key, $default = null)
    {
        $styling = array_merge($this->getDefaultStyling(), $this->styling ?? []);
        return data_get($styling, $key, $default);
    }

    /**
     * Set styling value
     */
    public function setStyling(string $key, $value): void
    {
        $styling = $this->styling ?? [];
        data_set($styling, $key, $value);
        $this->styling = $styling;
        $this->save();
    }

    /**
     * Get behavior value
     */
    public function getBehavior(string $key, $default = null)
    {
        $behavior = array_merge($this->getDefaultBehavior(), $this->behavior ?? []);
        return data_get($behavior, $key, $default);
    }

    /**
     * Set behavior value
     */
    public function setBehavior(string $key, $value): void
    {
        $behavior = $this->behavior ?? [];
        data_set($behavior, $key, $value);
        $this->behavior = $behavior;
        $this->save();
    }

    /**
     * Scope for active widgets
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Get widget analytics
     */
    public function getAnalytics(): array
    {
        return [
            'total_usage' => $this->usage_count,
            'last_used' => $this->last_used_at,
            'status' => $this->status,
            'created_at' => $this->created_at
        ];
    }
}
