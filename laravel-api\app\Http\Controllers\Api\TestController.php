<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Contracts\MCPOrchestratorInterface;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

class TestController extends Controller
{
    public function __construct(
        private MCPOrchestratorInterface $orchestrator
    ) {}

    /**
     * Test the MCP pipeline with a simple message
     */
    public function testMCPPipeline(): JsonResponse
    {
        $sessionId = Str::uuid()->toString();

        $input = [
            'message' => 'Hello, can you help me understand how AI works?',
            'context' => [],
            'user_id' => 1,
            'timestamp' => now()->toISOString(),
        ];

        try {
            $result = $this->orchestrator->processRequest($input, $sessionId);

            return response()->json([
                'success' => true,
                'session_id' => $sessionId,
                'input' => $input,
                'result' => $result,
                'agents_executed' => $this->getExecutedAgents($result),
                'pipeline_summary' => $this->getPipelineSummary($result),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 500);
        }
    }

    /**
     * Test the full MCP pipeline with different message types
     */
    public function testFullPipeline(): JsonResponse
    {
        $testCases = [
            [
                'name' => 'Question Intent',
                'message' => 'What is the weather like today?',
                'expected_intent' => 'question'
            ],
            [
                'name' => 'Search Intent',
                'message' => 'Search for information about machine learning',
                'expected_intent' => 'search'
            ],
            [
                'name' => 'Request Intent',
                'message' => 'Can you help me create a presentation?',
                'expected_intent' => 'request'
            ],
            [
                'name' => 'Conversation Intent',
                'message' => 'Hello, how are you doing today?',
                'expected_intent' => 'conversation'
            ]
        ];

        $results = [];

        foreach ($testCases as $testCase) {
            $sessionId = Str::uuid()->toString();

            $input = [
                'message' => $testCase['message'],
                'context' => [],
                'user_id' => 1,
                'timestamp' => now()->toISOString(),
            ];

            try {
                $result = $this->orchestrator->processRequest($input, $sessionId);

                $results[] = [
                    'test_case' => $testCase['name'],
                    'input_message' => $testCase['message'],
                    'expected_intent' => $testCase['expected_intent'],
                    'actual_intent' => $result['intent'] ?? 'unknown',
                    'success' => true,
                    'agents_executed' => $this->getExecutedAgents($result),
                    'pipeline_summary' => $this->getPipelineSummary($result),
                    'session_id' => $sessionId
                ];

            } catch (\Exception $e) {
                $results[] = [
                    'test_case' => $testCase['name'],
                    'input_message' => $testCase['message'],
                    'success' => false,
                    'error' => $e->getMessage(),
                    'session_id' => $sessionId
                ];
            }
        }

        return response()->json([
            'success' => true,
            'test_results' => $results,
            'summary' => [
                'total_tests' => count($testCases),
                'successful_tests' => count(array_filter($results, fn($r) => $r['success'])),
                'failed_tests' => count(array_filter($results, fn($r) => !$r['success']))
            ]
        ]);
    }

    /**
     * Test individual agents
     */
    public function testAgent(string $agentName): JsonResponse
    {
        $agent = $this->orchestrator->getAgent($agentName);

        if (!$agent) {
            return response()->json([
                'success' => false,
                'error' => "Agent '{$agentName}' not found",
            ], 404);
        }

        $sessionId = Str::uuid()->toString();

        $input = [
            'message' => 'Test message for ' . $agentName,
            'context' => [],
            'user_id' => 1,
            'timestamp' => now()->toISOString(),
        ];

        try {
            $result = $agent->execute($input, $sessionId);

            return response()->json([
                'success' => true,
                'agent' => $agentName,
                'session_id' => $sessionId,
                'input' => $input,
                'result' => $result,
                'agent_info' => [
                    'name' => $agent->getName(),
                    'type' => $agent->getType(),
                    'execution_order' => $agent->getExecutionOrder(),
                    'is_active' => $agent->isActive(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'agent' => $agentName,
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 500);
        }
    }

    /**
     * Get system status
     */
    public function getSystemStatus(): JsonResponse
    {
        try {
            $agents = collect($this->orchestrator->getAgents())
                ->map(function ($agent) {
                    return [
                        'name' => $agent->getName(),
                        'type' => $agent->getType(),
                        'execution_order' => $agent->getExecutionOrder(),
                        'is_active' => $agent->isActive(),
                        'config_keys' => array_keys($agent->getConfig()),
                    ];
                })
                ->values();

            return response()->json([
                'success' => true,
                'system_status' => 'operational',
                'agents' => $agents,
                'agent_count' => $agents->count(),
                'primary_agents' => $agents->where('type', 'primary')->count(),
                'secondary_agents' => $agents->where('type', 'secondary')->count(),
                'timestamp' => now()->toISOString(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'system_status' => 'error',
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString(),
            ], 500);
        }
    }

    private function getExecutedAgents(array $result): array
    {
        $executed = [];

        if (isset($result['intent'])) {
            $executed[] = 'intent';
        }

        if (isset($result['retrieved_documents'])) {
            $executed[] = 'retriever';
        }

        if (isset($result['llm_response'])) {
            $executed[] = 'llm';
        }

        if (isset($result['conversation_id'])) {
            $executed[] = 'memory';
        }

        if (isset($result['tool_executions'])) {
            $executed[] = 'tool';
        }

        if (isset($result['workflow_execution'])) {
            $executed[] = 'workflow';
        }

        if (isset($result['formatted_response'])) {
            $executed[] = 'formatter';
        }

        if (isset($result['guardrail_report'])) {
            $executed[] = 'guardrail';
        }

        return $executed;
    }

    private function getPipelineSummary(array $result): array
    {
        return [
            'intent_detected' => $result['intent'] ?? 'unknown',
            'confidence' => $result['confidence'] ?? 0,
            'documents_retrieved' => count($result['retrieved_documents'] ?? []),
            'tools_executed' => count($result['tool_executions'] ?? []),
            'workflow_executed' => isset($result['workflow_execution']),
            'response_formatted' => isset($result['formatted_response']),
            'content_filtered' => isset($result['guardrail_report']),
            'conversation_stored' => isset($result['conversation_id']),
            'total_agents' => count($this->getExecutedAgents($result))
        ];
    }
}
