<?php

declare(strict_types=1);

namespace App\Services\MCP\Agents;

use App\Services\MCP\AbstractMCPAgent;
use Illuminate\Support\Facades\Log;

class FormatterAgent extends AbstractMCPAgent
{
    protected array $formatters = [
        'markdown' => 'formatMarkdown',
        'html' => 'formatHtml',
        'json' => 'formatJson',
        'plain' => 'formatPlain',
        'structured' => 'formatStructured'
    ];

    public function getName(): string
    {
        return 'formatter';
    }

    public function getType(): string
    {
        return 'secondary';
    }

    public function getExecutionOrder(): int
    {
        return 12; // After Workflow Agent
    }

    protected function process(array $input, string $sessionId): array
    {
        $llmResponse = $input['llm_response'] ?? '';
        $intent = $input['intent'] ?? 'general';
        $toolData = $input['tool_data'] ?? [];
        $retrievedDocs = $input['retrieved_documents'] ?? [];

        Log::info('Formatter Agent processing response', [
            'session_id' => $sessionId,
            'intent' => $intent,
            'has_llm_response' => !empty($llmResponse),
            'has_tool_data' => !empty($toolData)
        ]);

        try {
            // Determine output format based on intent and context
            $outputFormat = $this->determineOutputFormat($input);
            
            // Format the response
            $formattedResponse = $this->formatResponse($input, $outputFormat);
            
            // Apply post-processing
            $finalResponse = $this->postProcessResponse($formattedResponse, $input);

            $result = array_merge($input, [
                'formatted_response' => $finalResponse,
                'original_llm_response' => $llmResponse,
                'formatter_metadata' => [
                    'output_format' => $outputFormat,
                    'original_length' => strlen($llmResponse),
                    'formatted_length' => strlen($finalResponse['content'] ?? ''),
                    'enhancements_applied' => $finalResponse['enhancements'] ?? [],
                    'processing_time' => microtime(true) - ($_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true))
                ]
            ]);

            Log::info('Formatter Agent completed formatting', [
                'session_id' => $sessionId,
                'output_format' => $outputFormat,
                'original_length' => strlen($llmResponse),
                'formatted_length' => strlen($finalResponse['content'] ?? '')
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Formatter Agent failed', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);

            return array_merge($input, [
                'formatted_response' => [
                    'content' => $llmResponse,
                    'format' => 'plain',
                    'error' => true
                ],
                'formatter_metadata' => [
                    'error' => $e->getMessage(),
                    'fallback_used' => true
                ]
            ]);
        }
    }

    protected function determineOutputFormat(array $input): string
    {
        $intent = $input['intent'] ?? 'general';
        $message = strtolower($input['message'] ?? '');
        $toolData = $input['tool_data'] ?? [];

        // Check for explicit format requests
        if (strpos($message, 'markdown') !== false) return 'markdown';
        if (strpos($message, 'html') !== false) return 'html';
        if (strpos($message, 'json') !== false) return 'json';
        if (strpos($message, 'structured') !== false) return 'structured';

        // Format based on intent
        switch ($intent) {
            case 'search':
                return !empty($toolData) ? 'structured' : 'markdown';
            case 'question':
                return 'markdown';
            case 'request':
                return 'structured';
            case 'workflow':
                return 'structured';
            default:
                return 'plain';
        }
    }

    protected function formatResponse(array $input, string $format): array
    {
        $formatter = $this->formatters[$format] ?? 'formatPlain';
        return $this->$formatter($input);
    }

    protected function formatMarkdown(array $input): array
    {
        $llmResponse = $input['llm_response'] ?? '';
        $retrievedDocs = $input['retrieved_documents'] ?? [];
        $toolData = $input['tool_data'] ?? [];

        $content = $llmResponse;
        $enhancements = [];

        // Add sources section if documents were retrieved
        if (!empty($retrievedDocs)) {
            $content .= "\n\n## Sources\n";
            foreach ($retrievedDocs as $index => $doc) {
                $title = $doc['title'] ?? 'Untitled';
                $relevance = number_format(($doc['relevance_score'] ?? 0) * 100, 1);
                $content .= "- **{$title}** (Relevance: {$relevance}%)\n";
            }
            $enhancements[] = 'sources_added';
        }

        // Add tool data if available
        if (!empty($toolData)) {
            $content .= "\n\n## Additional Information\n";
            foreach ($toolData as $tool => $data) {
                $content .= "### " . ucfirst($tool) . " Data\n";
                $content .= $this->formatToolDataAsMarkdown($data);
            }
            $enhancements[] = 'tool_data_added';
        }

        return [
            'content' => $content,
            'format' => 'markdown',
            'enhancements' => $enhancements
        ];
    }

    protected function formatHtml(array $input): array
    {
        $llmResponse = $input['llm_response'] ?? '';
        $retrievedDocs = $input['retrieved_documents'] ?? [];
        $toolData = $input['tool_data'] ?? [];

        $content = '<div class="ai-response">';
        $content .= '<div class="main-content">' . nl2br(htmlspecialchars($llmResponse)) . '</div>';
        $enhancements = [];

        // Add sources section
        if (!empty($retrievedDocs)) {
            $content .= '<div class="sources"><h3>Sources</h3><ul>';
            foreach ($retrievedDocs as $doc) {
                $title = htmlspecialchars($doc['title'] ?? 'Untitled');
                $relevance = number_format(($doc['relevance_score'] ?? 0) * 100, 1);
                $content .= "<li><strong>{$title}</strong> (Relevance: {$relevance}%)</li>";
            }
            $content .= '</ul></div>';
            $enhancements[] = 'sources_added';
        }

        // Add tool data
        if (!empty($toolData)) {
            $content .= '<div class="tool-data"><h3>Additional Information</h3>';
            foreach ($toolData as $tool => $data) {
                $content .= '<div class="tool-section">';
                $content .= '<h4>' . ucfirst($tool) . '</h4>';
                $content .= $this->formatToolDataAsHtml($data);
                $content .= '</div>';
            }
            $content .= '</div>';
            $enhancements[] = 'tool_data_added';
        }

        $content .= '</div>';

        return [
            'content' => $content,
            'format' => 'html',
            'enhancements' => $enhancements
        ];
    }

    protected function formatJson(array $input): array
    {
        $response = [
            'response' => $input['llm_response'] ?? '',
            'intent' => $input['intent'] ?? 'general',
            'confidence' => $input['confidence'] ?? 0,
            'sources' => $input['retrieved_documents'] ?? [],
            'tool_data' => $input['tool_data'] ?? [],
            'metadata' => [
                'session_id' => $input['session_id'] ?? null,
                'timestamp' => now()->toISOString()
            ]
        ];

        return [
            'content' => json_encode($response, JSON_PRETTY_PRINT),
            'format' => 'json',
            'enhancements' => ['structured_data']
        ];
    }

    protected function formatPlain(array $input): array
    {
        return [
            'content' => $input['llm_response'] ?? '',
            'format' => 'plain',
            'enhancements' => []
        ];
    }

    protected function formatStructured(array $input): array
    {
        $llmResponse = $input['llm_response'] ?? '';
        $intent = $input['intent'] ?? 'general';
        $toolData = $input['tool_data'] ?? [];
        $retrievedDocs = $input['retrieved_documents'] ?? [];

        $structured = [
            'main_response' => $llmResponse,
            'intent' => $intent,
            'confidence' => $input['confidence'] ?? 0,
            'sections' => []
        ];

        $enhancements = [];

        // Add tool data sections
        if (!empty($toolData)) {
            foreach ($toolData as $tool => $data) {
                $structured['sections'][] = [
                    'type' => 'tool_data',
                    'title' => ucfirst($tool) . ' Information',
                    'content' => $data
                ];
            }
            $enhancements[] = 'tool_sections_added';
        }

        // Add sources section
        if (!empty($retrievedDocs)) {
            $structured['sections'][] = [
                'type' => 'sources',
                'title' => 'Sources',
                'content' => array_map(function ($doc) {
                    return [
                        'title' => $doc['title'] ?? 'Untitled',
                        'relevance' => $doc['relevance_score'] ?? 0,
                        'source_type' => $doc['source_type'] ?? 'unknown'
                    ];
                }, $retrievedDocs)
            ];
            $enhancements[] = 'sources_section_added';
        }

        return [
            'content' => $structured,
            'format' => 'structured',
            'enhancements' => $enhancements
        ];
    }

    protected function formatToolDataAsMarkdown(array $data): string
    {
        if (isset($data['message'])) {
            $content = $data['message'] . "\n\n";
        } else {
            $content = '';
        }

        if (isset($data['data']) && is_array($data['data'])) {
            foreach ($data['data'] as $key => $value) {
                if (is_scalar($value)) {
                    $content .= "- **{$key}**: {$value}\n";
                }
            }
        }

        return $content . "\n";
    }

    protected function formatToolDataAsHtml(array $data): string
    {
        $content = '';
        
        if (isset($data['message'])) {
            $content .= '<p>' . htmlspecialchars($data['message']) . '</p>';
        }

        if (isset($data['data']) && is_array($data['data'])) {
            $content .= '<ul>';
            foreach ($data['data'] as $key => $value) {
                if (is_scalar($value)) {
                    $content .= '<li><strong>' . htmlspecialchars($key) . ':</strong> ' . htmlspecialchars($value) . '</li>';
                }
            }
            $content .= '</ul>';
        }

        return $content;
    }

    protected function postProcessResponse(array $formattedResponse, array $input): array
    {
        // Apply any post-processing enhancements
        $content = $formattedResponse['content'];
        $enhancements = $formattedResponse['enhancements'] ?? [];

        // Add timestamps if structured format
        if ($formattedResponse['format'] === 'structured' && is_array($content)) {
            $content['timestamp'] = now()->toISOString();
            $enhancements[] = 'timestamp_added';
        }

        // Clean up formatting
        if (is_string($content)) {
            $content = $this->cleanupFormatting($content);
            $enhancements[] = 'formatting_cleaned';
        }

        return [
            'content' => $content,
            'format' => $formattedResponse['format'],
            'enhancements' => $enhancements
        ];
    }

    protected function cleanupFormatting(string $content): string
    {
        // Remove excessive whitespace
        $content = preg_replace('/\n{3,}/', "\n\n", $content);
        $content = trim($content);
        
        return $content;
    }

    public function validateInput(array $input): bool
    {
        return isset($input['llm_response']) || isset($input['tool_data']);
    }
}
