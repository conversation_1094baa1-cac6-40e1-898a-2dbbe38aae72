<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('documents', function (Blueprint $table) {
            $table->foreignId('knowledge_base_id')->nullable()->after('id')->constrained()->onDelete('cascade');
            $table->string('file_name')->nullable()->after('title');
            $table->string('file_path')->nullable()->after('file_name');
            $table->string('file_type')->nullable()->after('file_path');
            $table->bigInteger('file_size')->nullable()->after('file_type');
            $table->string('mime_type')->nullable()->after('file_size');
            $table->string('status')->default('pending')->after('content'); // pending, processing, completed, failed
            $table->text('processing_error')->nullable()->after('status');
            $table->json('metadata')->nullable()->after('processing_error');
            $table->integer('chunk_count')->default(0)->after('metadata');
            $table->timestamp('processed_at')->nullable()->after('chunk_count');
            
            $table->index(['knowledge_base_id', 'status']);
            $table->index(['status', 'created_at']);
        });
    }

    public function down(): void
    {
        Schema::table('documents', function (Blueprint $table) {
            $table->dropForeign(['knowledge_base_id']);
            $table->dropColumn([
                'knowledge_base_id',
                'file_name',
                'file_path',
                'file_type',
                'file_size',
                'mime_type',
                'status',
                'processing_error',
                'metadata',
                'chunk_count',
                'processed_at'
            ]);
        });
    }
};
