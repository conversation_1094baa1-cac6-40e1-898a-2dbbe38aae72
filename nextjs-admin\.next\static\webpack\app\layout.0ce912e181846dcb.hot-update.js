"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a3f5c5661a92\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcbGFyYWdvblxcd3d3XFxhdWdtZW50TUNQXFxuZXh0anMtYWRtaW5cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImEzZjVjNTY2MWE5MlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useApi: () => (/* binding */ useApi),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth,useApi auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tenant, setTenant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isAuthenticated = !!user && !!token;\n    const isAdmin = !!user && user.role === 'admin';\n    // Initialize auth state from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initAuth = {\n                \"AuthProvider.useEffect.initAuth\": async ()=>{\n                    try {\n                        const storedToken = localStorage.getItem('auth_token');\n                        const storedUser = localStorage.getItem('user_data');\n                        const storedTenant = localStorage.getItem('tenant_data');\n                        if (storedToken && storedUser) {\n                            setToken(storedToken);\n                            setUser(JSON.parse(storedUser));\n                            if (storedTenant) {\n                                setTenant(JSON.parse(storedTenant));\n                            }\n                            // Verify token is still valid\n                            await refreshUser();\n                        }\n                    } catch (error) {\n                        console.error('Auth initialization error:', error);\n                        clearAuthData();\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initAuth\"];\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (email, password, tenantSubdomain)=>{\n        try {\n            const response = await fetch('http://localhost:8000/api/auth/login', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email,\n                    password,\n                    tenant_subdomain: tenantSubdomain\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                const { user: userData, tenant: tenantData, token: authToken } = data.data;\n                setUser(userData);\n                setTenant(tenantData);\n                setToken(authToken);\n                // Store in localStorage\n                localStorage.setItem('auth_token', authToken);\n                localStorage.setItem('user_data', JSON.stringify(userData));\n                if (tenantData) {\n                    localStorage.setItem('tenant_data', JSON.stringify(tenantData));\n                }\n                return true;\n            } else {\n                throw new Error(data.error || 'Login failed');\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n            return false;\n        }\n    };\n    const logout = async ()=>{\n        try {\n            if (token) {\n                await fetch('http://localhost:8000/api/auth/logout', {\n                    method: 'POST',\n                    headers: {\n                        'Authorization': \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n            }\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            clearAuthData();\n            router.push('/login');\n        }\n    };\n    const updateProfile = async (data)=>{\n        try {\n            if (!token) return false;\n            const response = await fetch('http://localhost:8000/api/auth/profile', {\n                method: 'PUT',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(data)\n            });\n            const result = await response.json();\n            if (result.success) {\n                const updatedUser = {\n                    ...user,\n                    ...result.data.user\n                };\n                setUser(updatedUser);\n                localStorage.setItem('user_data', JSON.stringify(updatedUser));\n                return true;\n            } else {\n                throw new Error(result.error || 'Profile update failed');\n            }\n        } catch (error) {\n            console.error('Profile update error:', error);\n            return false;\n        }\n    };\n    const refreshUser = async ()=>{\n        try {\n            if (!token) return;\n            const response = await fetch('http://localhost:8000/api/auth/me', {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                const { user: userData, tenant: tenantData } = data.data;\n                setUser(userData);\n                setTenant(tenantData);\n                // Update localStorage\n                localStorage.setItem('user_data', JSON.stringify(userData));\n                if (tenantData) {\n                    localStorage.setItem('tenant_data', JSON.stringify(tenantData));\n                }\n            } else {\n                // Token is invalid\n                clearAuthData();\n                router.push('/login');\n            }\n        } catch (error) {\n            console.error('Refresh user error:', error);\n            clearAuthData();\n            router.push('/login');\n        }\n    };\n    const clearAuthData = ()=>{\n        setUser(null);\n        setTenant(null);\n        setToken(null);\n        localStorage.removeItem('auth_token');\n        localStorage.removeItem('user_data');\n        localStorage.removeItem('tenant_data');\n    };\n    // API helper function with auth\n    const apiCall = async function(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const headers = {\n            'Content-Type': 'application/json',\n            ...token && {\n                'Authorization': \"Bearer \".concat(token)\n            },\n            ...tenant && {\n                'X-Tenant-ID': tenant.id\n            },\n            ...options.headers\n        };\n        const response = await fetch(\"http://localhost:8000/api\".concat(endpoint), {\n            ...options,\n            headers\n        });\n        if (response.status === 401) {\n            // Token expired or invalid\n            clearAuthData();\n            router.push('/login');\n            throw new Error('Authentication required');\n        }\n        return response;\n    };\n    const value = {\n        user,\n        tenant,\n        token,\n        isLoading,\n        isAuthenticated,\n        login,\n        logout,\n        updateProfile,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 251,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"LQsZIWx/yCbPHNdYfWhZFvNVfuA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Higher-order component for protected routes\nfunction withAuth(Component) {\n    var _s = $RefreshSig$();\n    return _s(function AuthenticatedComponent(props) {\n        _s();\n        const { isAuthenticated, isLoading } = useAuth();\n        const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"withAuth.AuthenticatedComponent.useEffect\": ()=>{\n                if (!isLoading && !isAuthenticated) {\n                    router.push('/login');\n                }\n            }\n        }[\"withAuth.AuthenticatedComponent.useEffect\"], [\n            isAuthenticated,\n            isLoading,\n            router\n        ]);\n        if (isLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 279,\n                columnNumber: 9\n            }, this);\n        }\n        if (!isAuthenticated) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 289,\n            columnNumber: 12\n        }, this);\n    }, \"mEH+GTDGNx6l1kiic8iucxoBZHI=\", false, function() {\n        return [\n            useAuth,\n            next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n        ];\n    });\n}\n// Hook for making authenticated API calls\nfunction useApi() {\n    _s2();\n    const { token, tenant } = useAuth();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const apiCall = async function(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const headers = {\n            'Content-Type': 'application/json',\n            ...token && {\n                'Authorization': \"Bearer \".concat(token)\n            },\n            ...tenant && {\n                'X-Tenant-ID': tenant.id\n            },\n            ...options.headers\n        };\n        const response = await fetch(\"http://localhost:8000/api\".concat(endpoint), {\n            ...options,\n            headers\n        });\n        if (response.status === 401) {\n            router.push('/login');\n            throw new Error('Authentication required');\n        }\n        return response;\n    };\n    return {\n        apiCall\n    };\n}\n_s2(useApi, \"10kWhbfrcpP8NLrtg6YhXrYFKow=\", false, function() {\n    return [\n        useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ })

});