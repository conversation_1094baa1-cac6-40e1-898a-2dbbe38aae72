<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Workflow;
use App\Models\WorkflowExecution;
use App\Services\MCP\MCPPipeline;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WorkflowExecutionService
{
    protected MCPPipeline $mcpPipeline;

    public function __construct(MCPPipeline $mcpPipeline)
    {
        $this->mcpPipeline = $mcpPipeline;
    }

    /**
     * Execute a workflow
     */
    public function execute(Workflow $workflow, array $inputData = []): WorkflowExecution
    {
        $execution = WorkflowExecution::create([
            'workflow_id' => $workflow->id,
            'status' => 'running',
            'input_data' => $inputData,
            'started_at' => now(),
            'execution_log' => []
        ]);

        try {
            $this->logStep($execution, 'workflow_started', 'Workflow execution started');

            // Validate workflow structure
            $validationErrors = $workflow->validateStructure();
            if (!empty($validationErrors)) {
                throw new \Exception('Workflow validation failed: ' . implode(', ', $validationErrors));
            }

            // Execute workflow nodes
            $result = $this->executeNodes($workflow, $inputData, $execution);

            // Complete execution
            $execution->update([
                'status' => 'completed',
                'output_data' => $result,
                'completed_at' => now(),
                'duration_ms' => now()->diffInMilliseconds($execution->started_at)
            ]);

            // Update workflow statistics
            $workflow->increment('execution_count');
            $workflow->update(['last_executed_at' => now()]);

            $this->logStep($execution, 'workflow_completed', 'Workflow execution completed successfully');

        } catch (\Exception $e) {
            $execution->update([
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'completed_at' => now(),
                'duration_ms' => now()->diffInMilliseconds($execution->started_at)
            ]);

            $this->logStep($execution, 'workflow_failed', 'Workflow execution failed: ' . $e->getMessage());

            Log::error('Workflow execution failed', [
                'workflow_id' => $workflow->id,
                'execution_id' => $execution->id,
                'error' => $e->getMessage()
            ]);
        }

        return $execution;
    }

    /**
     * Execute workflow nodes
     */
    protected function executeNodes(Workflow $workflow, array $inputData, WorkflowExecution $execution): array
    {
        $nodes = $workflow->nodes ?? [];
        $nodeResults = [];
        $currentData = $inputData;

        // Find trigger node
        $triggerNode = collect($nodes)->first(fn($node) => $node['type'] === 'trigger');
        if (!$triggerNode) {
            throw new \Exception('No trigger node found');
        }

        // Start execution from trigger
        $executionQueue = [$triggerNode['id']];
        $processedNodes = [];

        while (!empty($executionQueue)) {
            $nodeId = array_shift($executionQueue);
            
            if (in_array($nodeId, $processedNodes)) {
                continue; // Avoid infinite loops
            }

            $node = $nodes[$nodeId] ?? null;
            if (!$node) {
                continue;
            }

            $this->logStep($execution, 'node_started', "Executing node: {$nodeId} ({$node['type']})");

            try {
                // Execute node
                $nodeResult = $this->executeNode($node, $currentData, $execution);
                $nodeResults[$nodeId] = $nodeResult;
                $processedNodes[] = $nodeId;

                // Update current data for next nodes
                if (isset($nodeResult['output'])) {
                    $currentData = array_merge($currentData, $nodeResult['output']);
                }

                $this->logStep($execution, 'node_completed', "Node {$nodeId} completed successfully");

                // Find connected nodes
                $connectedNodes = $this->findConnectedNodes($nodes, $nodeId, $nodeResult);
                $executionQueue = array_merge($executionQueue, $connectedNodes);

            } catch (\Exception $e) {
                $this->logStep($execution, 'node_failed', "Node {$nodeId} failed: " . $e->getMessage());
                throw new \Exception("Node {$nodeId} execution failed: " . $e->getMessage());
            }
        }

        return $currentData;
    }

    /**
     * Execute a single node
     */
    protected function executeNode(array $node, array $inputData, WorkflowExecution $execution): array
    {
        $nodeType = $node['type'];
        $nodeData = $node['data'] ?? [];

        return match($nodeType) {
            'trigger' => $this->executeTriggerNode($node, $inputData),
            'llm_agent' => $this->executeLLMAgentNode($node, $inputData),
            'knowledge_search' => $this->executeKnowledgeSearchNode($node, $inputData),
            'condition' => $this->executeConditionNode($node, $inputData),
            'http_request' => $this->executeHttpRequestNode($node, $inputData),
            'data_transform' => $this->executeDataTransformNode($node, $inputData),
            'delay' => $this->executeDelayNode($node, $inputData),
            'output' => $this->executeOutputNode($node, $inputData),
            default => throw new \Exception("Unknown node type: {$nodeType}")
        };
    }

    /**
     * Execute trigger node
     */
    protected function executeTriggerNode(array $node, array $inputData): array
    {
        return [
            'output' => $inputData,
            'metadata' => ['triggered_at' => now()->toISOString()]
        ];
    }

    /**
     * Execute LLM agent node
     */
    protected function executeLLMAgentNode(array $node, array $inputData): array
    {
        $config = $node['data'] ?? [];
        $prompt = $config['prompt'] ?? '';
        
        // Replace variables in prompt
        $processedPrompt = $this->replaceVariables($prompt, $inputData);

        $result = $this->mcpPipeline->process([
            'messages' => [
                ['role' => 'user', 'content' => $processedPrompt]
            ],
            'context' => $inputData
        ]);

        if (!$result['success']) {
            throw new \Exception('LLM Agent failed: ' . ($result['error'] ?? 'Unknown error'));
        }

        return [
            'output' => [
                'llm_response' => $result['llm_response'],
                'provider_used' => $result['provider_used'] ?? 'unknown'
            ],
            'metadata' => $result['llm_metadata'] ?? []
        ];
    }

    /**
     * Execute knowledge search node
     */
    protected function executeKnowledgeSearchNode(array $node, array $inputData): array
    {
        $config = $node['data'] ?? [];
        $knowledgeBaseId = $config['knowledge_base_id'] ?? null;
        $query = $this->replaceVariables($config['query'] ?? '', $inputData);
        $limit = $config['limit'] ?? 10;

        if (!$knowledgeBaseId) {
            throw new \Exception('Knowledge base ID not configured');
        }

        // Use the embedding service to search
        $embeddingService = app(\App\Services\EmbeddingService::class);
        $results = $embeddingService->searchSimilar($query, $knowledgeBaseId, $limit);

        return [
            'output' => [
                'search_results' => $results,
                'query' => $query,
                'result_count' => count($results)
            ]
        ];
    }

    /**
     * Execute condition node
     */
    protected function executeConditionNode(array $node, array $inputData): array
    {
        $config = $node['data'] ?? [];
        $condition = $config['condition'] ?? '';
        $operator = $config['operator'] ?? 'equals';
        
        // Simple condition evaluation
        $result = $this->evaluateCondition($condition, $operator, $inputData);

        return [
            'output' => ['condition_result' => $result],
            'branch' => $result ? 'true' : 'false'
        ];
    }

    /**
     * Execute HTTP request node
     */
    protected function executeHttpRequestNode(array $node, array $inputData): array
    {
        $config = $node['data'] ?? [];
        $url = $this->replaceVariables($config['url'] ?? '', $inputData);
        $method = $config['method'] ?? 'GET';
        $headers = $config['headers'] ?? [];
        $body = $config['body'] ?? [];

        $response = Http::withHeaders($headers)
            ->timeout(30)
            ->{strtolower($method)}($url, $body);

        return [
            'output' => [
                'response_status' => $response->status(),
                'response_data' => $response->json(),
                'response_headers' => $response->headers()
            ]
        ];
    }

    /**
     * Execute data transform node
     */
    protected function executeDataTransformNode(array $node, array $inputData): array
    {
        $config = $node['data'] ?? [];
        $transformation = $config['transformation'] ?? [];

        // Apply transformation rules
        $transformedData = $this->applyTransformation($inputData, $transformation);

        return [
            'output' => $transformedData
        ];
    }

    /**
     * Execute delay node
     */
    protected function executeDelayNode(array $node, array $inputData): array
    {
        $config = $node['data'] ?? [];
        $duration = $config['duration'] ?? 1;
        $unit = $config['unit'] ?? 'seconds';

        $seconds = match($unit) {
            'minutes' => $duration * 60,
            'hours' => $duration * 3600,
            default => $duration
        };

        sleep($seconds);

        return [
            'output' => $inputData,
            'metadata' => ['delayed_seconds' => $seconds]
        ];
    }

    /**
     * Execute output node
     */
    protected function executeOutputNode(array $node, array $inputData): array
    {
        $config = $node['data'] ?? [];
        $format = $config['format'] ?? 'json';

        $output = match($format) {
            'text' => is_string($inputData) ? $inputData : json_encode($inputData),
            'html' => $this->formatAsHtml($inputData),
            default => $inputData
        };

        return [
            'output' => $output,
            'final_output' => true
        ];
    }

    /**
     * Find connected nodes
     */
    protected function findConnectedNodes(array $nodes, string $fromNodeId, array $nodeResult): array
    {
        $connectedNodes = [];
        
        // Simple connection logic - in a real implementation, you'd have explicit connections
        // For now, we'll use a simple sequential execution
        $nodeIds = array_keys($nodes);
        $currentIndex = array_search($fromNodeId, $nodeIds);
        
        if ($currentIndex !== false && isset($nodeIds[$currentIndex + 1])) {
            $connectedNodes[] = $nodeIds[$currentIndex + 1];
        }

        return $connectedNodes;
    }

    /**
     * Replace variables in text
     */
    protected function replaceVariables(string $text, array $data): string
    {
        foreach ($data as $key => $value) {
            $placeholder = "{{$key}}";
            $replacement = is_string($value) ? $value : json_encode($value);
            $text = str_replace($placeholder, $replacement, $text);
        }
        
        return $text;
    }

    /**
     * Evaluate condition
     */
    protected function evaluateCondition(string $condition, string $operator, array $data): bool
    {
        // Simple condition evaluation - in production, use a proper expression evaluator
        $value = $data[$condition] ?? null;
        
        return match($operator) {
            'equals' => $value === true,
            'not_equals' => $value !== true,
            'contains' => is_string($value) && str_contains($value, $condition),
            'greater_than' => is_numeric($value) && $value > 0,
            'less_than' => is_numeric($value) && $value < 0,
            default => false
        };
    }

    /**
     * Apply data transformation
     */
    protected function applyTransformation(array $data, array $transformation): array
    {
        // Simple transformation - in production, use a proper transformation engine
        $result = [];
        
        foreach ($transformation as $key => $rule) {
            if (isset($data[$rule])) {
                $result[$key] = $data[$rule];
            }
        }
        
        return $result;
    }

    /**
     * Format data as HTML
     */
    protected function formatAsHtml(array $data): string
    {
        $html = '<div class="workflow-output">';
        foreach ($data as $key => $value) {
            $html .= "<div><strong>{$key}:</strong> " . htmlspecialchars(json_encode($value)) . "</div>";
        }
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Log execution step
     */
    protected function logStep(WorkflowExecution $execution, string $type, string $message): void
    {
        $log = $execution->execution_log ?? [];
        $log[] = [
            'timestamp' => now()->toISOString(),
            'type' => $type,
            'message' => $message
        ];
        
        $execution->update(['execution_log' => $log]);
    }
}
