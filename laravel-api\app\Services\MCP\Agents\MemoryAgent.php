<?php

declare(strict_types=1);

namespace App\Services\MCP\Agents;

use App\Services\MCP\AbstractMCPAgent;
use App\Models\Conversation;
use App\Models\Message;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class MemoryAgent extends AbstractMCPAgent
{
    protected int $maxContextMessages = 10;
    protected int $contextWindowTokens = 4000;
    protected int $cacheTimeout = 3600; // 1 hour

    public function getName(): string
    {
        return 'memory';
    }

    public function getType(): string
    {
        return 'primary';
    }

    public function getExecutionOrder(): int
    {
        return 4; // Last in primary agent pipeline
    }

    protected function process(array $input, string $sessionId): array
    {
        Log::info('Memory Agent processing conversation', [
            'session_id' => $sessionId
        ]);

        try {
            // Get or create conversation
            $conversation = $this->getOrCreateConversation($sessionId, $input);
            
            // Store the user message
            $userMessage = $this->storeUserMessage($conversation, $input);
            
            // Get conversation context
            $context = $this->getConversationContext($conversation);
            
            // Store the assistant response if available
            $assistantMessage = null;
            if (!empty($input['llm_response'])) {
                $assistantMessage = $this->storeAssistantMessage($conversation, $input);
            }

            // Update conversation metadata
            $this->updateConversationMetadata($conversation, $input);

            $result = array_merge($input, [
                'conversation_id' => $conversation->id,
                'conversation_context' => $context,
                'message_id' => $userMessage->id,
                'memory_metadata' => [
                    'conversation_length' => $conversation->messages()->count(),
                    'context_messages' => count($context['messages']),
                    'total_tokens_estimate' => $this->estimateTokens($context),
                    'cache_key' => $this->getCacheKey($sessionId)
                ]
            ]);

            // Cache the context for quick access
            $this->cacheContext($sessionId, $context);

            Log::info('Memory Agent completed processing', [
                'session_id' => $sessionId,
                'conversation_id' => $conversation->id,
                'message_count' => $conversation->messages()->count()
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Memory Agent failed', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);

            return array_merge($input, [
                'memory_metadata' => [
                    'error' => $e->getMessage(),
                    'fallback_used' => true
                ]
            ]);
        }
    }

    protected function getOrCreateConversation(string $sessionId, array $input): Conversation
    {
        $conversation = Conversation::where('session_id', $sessionId)->first();

        if (!$conversation) {
            $conversation = Conversation::create([
                'session_id' => $sessionId,
                'user_id' => $input['user_id'] ?? null,
                'title' => $this->generateConversationTitle($input['message'] ?? ''),
                'context' => [
                    'intent' => $input['intent'] ?? 'general',
                    'created_at' => now()->toISOString(),
                    'user_agent' => request()->header('User-Agent'),
                    'ip_address' => request()->ip()
                ],
                'last_activity_at' => now()
            ]);
        } else {
            $conversation->update(['last_activity_at' => now()]);
        }

        return $conversation;
    }

    protected function storeUserMessage(Conversation $conversation, array $input): Message
    {
        return Message::create([
            'conversation_id' => $conversation->id,
            'role' => 'user',
            'content' => $input['message'] ?? '',
            'metadata' => [
                'intent' => $input['intent'] ?? 'general',
                'confidence' => $input['confidence'] ?? 0,
                'entities' => $input['entities'] ?? [],
                'retrieved_docs_count' => count($input['retrieved_documents'] ?? []),
                'timestamp' => now()->toISOString()
            ]
        ]);
    }

    protected function storeAssistantMessage(Conversation $conversation, array $input): Message
    {
        return Message::create([
            'conversation_id' => $conversation->id,
            'role' => 'assistant',
            'content' => $input['llm_response'] ?? '',
            'metadata' => [
                'llm_provider' => $input['llm_metadata']['provider'] ?? 'unknown',
                'model' => $input['llm_metadata']['model'] ?? 'unknown',
                'tokens_used' => $input['llm_metadata']['tokens_used'] ?? 0,
                'processing_time' => $input['llm_metadata']['processing_time'] ?? 0,
                'retrieved_docs' => array_map(function ($doc) {
                    return [
                        'title' => $doc['title'] ?? 'Untitled',
                        'relevance_score' => $doc['relevance_score'] ?? 0,
                        'source_type' => $doc['source_type'] ?? 'unknown'
                    ];
                }, $input['retrieved_documents'] ?? []),
                'timestamp' => now()->toISOString()
            ]
        ]);
    }

    protected function getConversationContext(Conversation $conversation): array
    {
        // Get recent messages for context
        $messages = $conversation->messages()
            ->orderBy('created_at', 'desc')
            ->limit($this->maxContextMessages)
            ->get()
            ->reverse()
            ->values();

        // Build context structure
        $context = [
            'conversation_id' => $conversation->id,
            'session_id' => $conversation->session_id,
            'title' => $conversation->title,
            'messages' => $messages->map(function ($message) {
                return [
                    'id' => $message->id,
                    'role' => $message->role,
                    'content' => $message->content,
                    'timestamp' => $message->created_at->toISOString(),
                    'metadata' => $message->metadata
                ];
            })->toArray(),
            'summary' => $this->generateConversationSummary($messages),
            'topics' => $this->extractTopics($messages),
            'last_activity' => $conversation->last_activity_at->toISOString()
        ];

        return $context;
    }

    protected function updateConversationMetadata(Conversation $conversation, array $input): void
    {
        $context = $conversation->context ?? [];
        
        // Update context with new information
        $context['last_intent'] = $input['intent'] ?? 'general';
        $context['message_count'] = $conversation->messages()->count();
        $context['last_updated'] = now()->toISOString();
        
        // Track topics and entities
        if (!empty($input['entities'])) {
            $context['entities'] = array_merge($context['entities'] ?? [], $input['entities']);
        }

        $conversation->update(['context' => $context]);
    }

    protected function generateConversationTitle(string $firstMessage): string
    {
        $title = substr($firstMessage, 0, 50);
        if (strlen($firstMessage) > 50) {
            $title .= '...';
        }
        return $title ?: 'New Conversation';
    }

    protected function generateConversationSummary($messages): string
    {
        if ($messages->count() <= 2) {
            return 'New conversation';
        }

        // Simple summary generation - can be enhanced with LLM
        $topics = $this->extractTopics($messages);
        if (!empty($topics)) {
            return 'Discussion about: ' . implode(', ', array_slice($topics, 0, 3));
        }

        return sprintf('Conversation with %d messages', $messages->count());
    }

    protected function extractTopics($messages): array
    {
        $topics = [];
        $commonWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'a', 'an'];

        foreach ($messages as $message) {
            if ($message->role === 'user') {
                $words = str_word_count(strtolower($message->content), 1);
                $words = array_filter($words, function ($word) use ($commonWords) {
                    return strlen($word) > 3 && !in_array($word, $commonWords);
                });
                $topics = array_merge($topics, $words);
            }
        }

        // Get most frequent topics
        $topicCounts = array_count_values($topics);
        arsort($topicCounts);
        
        return array_keys(array_slice($topicCounts, 0, 5));
    }

    protected function estimateTokens(array $context): int
    {
        $text = '';
        foreach ($context['messages'] as $message) {
            $text .= $message['content'] . ' ';
        }
        
        // Rough estimation: 1 token ≈ 4 characters
        return (int) (strlen($text) / 4);
    }

    protected function getCacheKey(string $sessionId): string
    {
        return "conversation_context:{$sessionId}";
    }

    protected function cacheContext(string $sessionId, array $context): void
    {
        Cache::put($this->getCacheKey($sessionId), $context, $this->cacheTimeout);
    }

    public function validateInput(array $input): bool
    {
        return isset($input['message']) && is_string($input['message']);
    }
}
