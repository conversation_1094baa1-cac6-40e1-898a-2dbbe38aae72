<?php

declare(strict_types=1);

namespace App\Services\Tools\Built;

use App\Services\Tools\AbstractTool;

class WebSearchTool extends AbstractTool
{
    protected array $requiredParams = ['query'];
    protected array $optionalParams = ['limit', 'safe_search'];

    public function getName(): string
    {
        return 'web_search';
    }

    public function getDescription(): string
    {
        return 'Search the web for information using search engines';
    }

    protected function initialize(): void
    {
        $this->config = array_merge([
            'api_key' => env('SEARCH_API_KEY'),
            'search_engine_id' => env('SEARCH_ENGINE_ID'),
            'base_url' => 'https://www.googleapis.com/customsearch/v1',
            'default_limit' => 5
        ], $this->config);
    }

    public function isAvailable(): bool
    {
        return !empty($this->config['api_key']) && !empty($this->config['search_engine_id']);
    }

    protected function process(array $params, string $sessionId): array
    {
        $query = $params['query'];
        $limit = $params['limit'] ?? $this->config['default_limit'];
        
        // For demo purposes, return mock data if no API key
        if (!$this->isAvailable()) {
            return $this->getMockSearchResults($query, $limit);
        }

        try {
            $response = $this->makeHttpRequest($this->config['base_url'], [
                'key' => $this->config['api_key'],
                'cx' => $this->config['search_engine_id'],
                'q' => $query,
                'num' => min($limit, 10)
            ]);

            $results = [];
            if (isset($response['items'])) {
                foreach ($response['items'] as $item) {
                    $results[] = [
                        'title' => $item['title'] ?? '',
                        'url' => $item['link'] ?? '',
                        'snippet' => $item['snippet'] ?? '',
                        'display_url' => $item['displayLink'] ?? ''
                    ];
                }
            }

            return $this->formatResponse([
                'query' => $query,
                'results' => $results,
                'total_results' => $response['searchInformation']['totalResults'] ?? 0
            ], "Found " . count($results) . " results for '{$query}'");

        } catch (\Exception $e) {
            return $this->getMockSearchResults($query, $limit);
        }
    }

    protected function getMockSearchResults(string $query, int $limit): array
    {
        $mockResults = [
            [
                'title' => "Search result for: {$query}",
                'url' => 'https://example.com/result1',
                'snippet' => "This is a mock search result for the query '{$query}'. In a real implementation, this would contain actual search results.",
                'display_url' => 'example.com'
            ],
            [
                'title' => "Another result about {$query}",
                'url' => 'https://example.org/result2',
                'snippet' => "Additional information about '{$query}' would be displayed here in real search results.",
                'display_url' => 'example.org'
            ]
        ];

        return $this->formatResponse([
            'query' => $query,
            'results' => array_slice($mockResults, 0, $limit),
            'total_results' => count($mockResults),
            'mock' => true
        ], "Mock search results for '{$query}'");
    }
}
