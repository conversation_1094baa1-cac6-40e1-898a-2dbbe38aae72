<?php

declare(strict_types=1);

namespace App\Services\MCP\Agents;

use App\Services\MCP\AbstractMCPAgent;
use App\Models\Workflow;
use App\Models\WorkflowExecution;
use Illuminate\Support\Facades\Log;

class WorkflowAgent extends AbstractMCPAgent
{
    protected int $maxExecutionTime = 300; // 5 minutes
    protected int $maxSteps = 20;

    public function getName(): string
    {
        return 'workflow';
    }

    public function getType(): string
    {
        return 'secondary';
    }

    public function getExecutionOrder(): int
    {
        return 11; // After Tool Agent
    }

    protected function process(array $input, string $sessionId): array
    {
        $intent = $input['intent'] ?? 'general';
        $message = $input['message'] ?? '';

        // Skip if workflow execution is not required
        if (!($input['requires_workflow'] ?? false) && $intent !== 'workflow') {
            Log::info('Workflow Agent skipped - no workflow required', [
                'session_id' => $sessionId
            ]);
            
            return array_merge($input, [
                'workflow_execution' => null,
                'workflow_metadata' => [
                    'skipped' => true,
                    'reason' => 'not_required'
                ]
            ]);
        }

        Log::info('Workflow Agent processing request', [
            'session_id' => $sessionId,
            'intent' => $intent
        ]);

        try {
            // Identify workflow to execute
            $workflow = $this->identifyWorkflow($input);
            
            if (!$workflow) {
                return array_merge($input, [
                    'workflow_execution' => null,
                    'workflow_metadata' => [
                        'skipped' => true,
                        'reason' => 'no_workflow_found'
                    ]
                ]);
            }

            // Execute workflow
            $execution = $this->executeWorkflow($workflow, $input, $sessionId);

            $result = array_merge($input, [
                'workflow_execution' => $execution,
                'workflow_metadata' => [
                    'workflow_id' => $workflow->id,
                    'workflow_name' => $workflow->name,
                    'execution_id' => $execution['id'],
                    'status' => $execution['status'],
                    'steps_completed' => $execution['current_step'],
                    'total_steps' => count($workflow->steps),
                    'execution_time' => $execution['execution_time']
                ]
            ]);

            Log::info('Workflow Agent completed execution', [
                'session_id' => $sessionId,
                'workflow_id' => $workflow->id,
                'status' => $execution['status']
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Workflow Agent failed', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);

            return array_merge($input, [
                'workflow_execution' => null,
                'workflow_metadata' => [
                    'error' => $e->getMessage(),
                    'fallback_used' => true
                ]
            ]);
        }
    }

    protected function identifyWorkflow(array $input): ?Workflow
    {
        $message = strtolower($input['message'] ?? '');
        $intent = $input['intent'] ?? 'general';

        // Look for explicit workflow mentions
        if (preg_match('/workflow\s+(\w+)/i', $message, $matches)) {
            $workflowName = $matches[1];
            return Workflow::where('name', 'like', "%{$workflowName}%")
                          ->where('is_active', true)
                          ->first();
        }

        // Look for workflow triggers based on intent and keywords
        $workflows = Workflow::where('is_active', true)->get();
        
        foreach ($workflows as $workflow) {
            if ($this->matchesWorkflowTrigger($workflow, $input)) {
                return $workflow;
            }
        }

        return null;
    }

    protected function matchesWorkflowTrigger(Workflow $workflow, array $input): bool
    {
        $config = $workflow->config ?? [];
        $triggers = $config['triggers'] ?? [];
        $message = strtolower($input['message'] ?? '');
        $intent = $input['intent'] ?? 'general';

        // Check intent triggers
        if (isset($triggers['intents']) && in_array($intent, $triggers['intents'])) {
            return true;
        }

        // Check keyword triggers
        if (isset($triggers['keywords'])) {
            foreach ($triggers['keywords'] as $keyword) {
                if (strpos($message, strtolower($keyword)) !== false) {
                    return true;
                }
            }
        }

        // Check pattern triggers
        if (isset($triggers['patterns'])) {
            foreach ($triggers['patterns'] as $pattern) {
                if (preg_match($pattern, $message)) {
                    return true;
                }
            }
        }

        return false;
    }

    protected function executeWorkflow(Workflow $workflow, array $input, string $sessionId): array
    {
        $startTime = microtime(true);
        
        // Create workflow execution record
        $execution = WorkflowExecution::create([
            'workflow_id' => $workflow->id,
            'session_id' => $sessionId,
            'input_data' => $input,
            'status' => 'running',
            'current_step' => 0
        ]);

        try {
            $steps = $workflow->steps ?? [];
            $context = $input;
            $stepResults = [];

            foreach ($steps as $stepIndex => $step) {
                if (microtime(true) - $startTime > $this->maxExecutionTime) {
                    throw new \RuntimeException('Workflow execution timeout');
                }

                $execution->update(['current_step' => $stepIndex + 1]);

                $stepResult = $this->executeWorkflowStep($step, $context, $sessionId);
                $stepResults[] = $stepResult;

                // Update context with step result
                $context = array_merge($context, $stepResult['output'] ?? []);

                // Check for step failure
                if ($stepResult['status'] === 'failed') {
                    throw new \RuntimeException("Step {$stepIndex} failed: " . ($stepResult['error'] ?? 'Unknown error'));
                }

                // Check for early termination
                if ($stepResult['terminate'] ?? false) {
                    break;
                }
            }

            $executionTime = microtime(true) - $startTime;

            $execution->update([
                'status' => 'completed',
                'output_data' => [
                    'steps' => $stepResults,
                    'final_context' => $context
                ]
            ]);

            return [
                'id' => $execution->id,
                'status' => 'completed',
                'current_step' => $execution->current_step,
                'execution_time' => $executionTime,
                'steps' => $stepResults,
                'output' => $context
            ];

        } catch (\Exception $e) {
            $executionTime = microtime(true) - $startTime;

            $execution->update([
                'status' => 'failed',
                'error_message' => $e->getMessage()
            ]);

            return [
                'id' => $execution->id,
                'status' => 'failed',
                'current_step' => $execution->current_step,
                'execution_time' => $executionTime,
                'error' => $e->getMessage()
            ];
        }
    }

    protected function executeWorkflowStep(array $step, array $context, string $sessionId): array
    {
        $stepType = $step['type'] ?? 'action';
        $stepConfig = $step['config'] ?? [];
        $stepStartTime = microtime(true);

        try {
            switch ($stepType) {
                case 'condition':
                    return $this->executeConditionStep($stepConfig, $context);
                
                case 'action':
                    return $this->executeActionStep($stepConfig, $context, $sessionId);
                
                case 'loop':
                    return $this->executeLoopStep($stepConfig, $context, $sessionId);
                
                case 'delay':
                    return $this->executeDelayStep($stepConfig);
                
                default:
                    throw new \InvalidArgumentException("Unknown step type: {$stepType}");
            }

        } catch (\Exception $e) {
            return [
                'status' => 'failed',
                'error' => $e->getMessage(),
                'execution_time' => microtime(true) - $stepStartTime
            ];
        }
    }

    protected function executeConditionStep(array $config, array $context): array
    {
        $condition = $config['condition'] ?? '';
        $trueAction = $config['true_action'] ?? [];
        $falseAction = $config['false_action'] ?? [];

        // Simple condition evaluation (can be enhanced)
        $result = $this->evaluateCondition($condition, $context);

        return [
            'status' => 'completed',
            'type' => 'condition',
            'condition_result' => $result,
            'output' => $result ? $trueAction : $falseAction,
            'execution_time' => 0.001
        ];
    }

    protected function executeActionStep(array $config, array $context, string $sessionId): array
    {
        $action = $config['action'] ?? '';
        $params = $config['params'] ?? [];

        // Replace variables in params with context values
        $params = $this->replaceVariables($params, $context);

        // Execute the action (this could call tools, APIs, etc.)
        $result = $this->performAction($action, $params, $sessionId);

        return [
            'status' => 'completed',
            'type' => 'action',
            'action' => $action,
            'output' => $result,
            'execution_time' => 0.1
        ];
    }

    protected function executeLoopStep(array $config, array $context, string $sessionId): array
    {
        $iterations = $config['iterations'] ?? 1;
        $steps = $config['steps'] ?? [];
        $results = [];

        for ($i = 0; $i < $iterations; $i++) {
            foreach ($steps as $step) {
                $stepResult = $this->executeWorkflowStep($step, $context, $sessionId);
                $results[] = $stepResult;
                
                if ($stepResult['status'] === 'failed') {
                    break 2;
                }
            }
        }

        return [
            'status' => 'completed',
            'type' => 'loop',
            'iterations' => $iterations,
            'output' => $results,
            'execution_time' => 0.1
        ];
    }

    protected function executeDelayStep(array $config): array
    {
        $seconds = $config['seconds'] ?? 1;
        sleep($seconds);

        return [
            'status' => 'completed',
            'type' => 'delay',
            'delay_seconds' => $seconds,
            'output' => [],
            'execution_time' => $seconds
        ];
    }

    protected function evaluateCondition(string $condition, array $context): bool
    {
        // Simple condition evaluation - can be enhanced with a proper expression parser
        return true; // Placeholder
    }

    protected function replaceVariables(array $params, array $context): array
    {
        // Replace {{variable}} patterns with context values
        $json = json_encode($params);
        
        foreach ($context as $key => $value) {
            if (is_scalar($value)) {
                $json = str_replace("{{{$key}}}", $value, $json);
            }
        }
        
        return json_decode($json, true) ?? $params;
    }

    protected function performAction(string $action, array $params, string $sessionId): array
    {
        // Placeholder for action execution
        return ['action_executed' => $action, 'params' => $params];
    }

    public function validateInput(array $input): bool
    {
        return isset($input['message']) && is_string($input['message']);
    }
}
