<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mcp_agents', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // intent, retriever, llm, memory, tool, workflow, formatter, guardrail
            $table->string('type'); // primary, secondary
            $table->text('description')->nullable();
            $table->json('config'); // Agent-specific configuration
            $table->boolean('is_active')->default(true);
            $table->integer('execution_order')->default(0);
            $table->timestamps();
        });

        Schema::create('agent_executions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('agent_id')->constrained('mcp_agents')->onDelete('cascade');
            $table->string('session_id');
            $table->json('input_data');
            $table->json('output_data')->nullable();
            $table->string('status'); // pending, running, completed, failed
            $table->text('error_message')->nullable();
            $table->integer('execution_time_ms')->nullable();
            $table->timestamps();
            
            $table->index(['session_id', 'created_at']);
        });

        Schema::create('agent_communications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('from_agent_id')->constrained('mcp_agents')->onDelete('cascade');
            $table->foreignId('to_agent_id')->constrained('mcp_agents')->onDelete('cascade');
            $table->string('session_id');
            $table->string('message_type'); // request, response, event
            $table->json('payload');
            $table->timestamps();
            
            $table->index(['session_id', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('agent_communications');
        Schema::dropIfExists('agent_executions');
        Schema::dropIfExists('mcp_agents');
    }
};
