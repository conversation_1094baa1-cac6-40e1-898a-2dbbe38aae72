<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Document;
use App\Models\DocumentChunk;
use App\Models\KnowledgeBase;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\UploadedFile;

class DocumentProcessingService
{
    protected EmbeddingService $embeddingService;

    public function __construct(EmbeddingService $embeddingService)
    {
        $this->embeddingService = $embeddingService;
    }

    /**
     * Upload and process a document
     */
    public function uploadDocument(UploadedFile $file, KnowledgeBase $knowledgeBase, array $metadata = []): Document
    {
        try {
            // Validate file
            $this->validateFile($file, $knowledgeBase);

            // Store file
            $filePath = $this->storeFile($file, $knowledgeBase);

            // Create document record
            $document = Document::create([
                'knowledge_base_id' => $knowledgeBase->id,
                'title' => $metadata['title'] ?? pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME),
                'file_name' => $file->getClientOriginalName(),
                'file_path' => $filePath,
                'file_type' => $file->getClientOriginalExtension(),
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'status' => 'pending',
                'metadata' => $metadata
            ]);

            // Process document if auto-processing is enabled
            if ($knowledgeBase->settings['auto_process'] ?? true) {
                $this->processDocument($document);
            }

            return $document;

        } catch (\Exception $e) {
            Log::error('Document upload failed', [
                'file_name' => $file->getClientOriginalName(),
                'knowledge_base_id' => $knowledgeBase->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Process a document (extract text and create chunks)
     */
    public function processDocument(Document $document): void
    {
        try {
            $document->update(['status' => 'processing']);

            // Extract text content
            $content = $this->extractContent($document);
            
            if (empty($content)) {
                throw new \Exception('No content could be extracted from the document');
            }

            // Update document with extracted content
            $document->update(['content' => $content]);

            // Create chunks
            $chunks = $this->createChunks($document, $content);

            // Update document statistics
            $document->update([
                'status' => 'completed',
                'chunk_count' => count($chunks),
                'processed_at' => now()
            ]);

            // Update knowledge base statistics
            $document->knowledgeBase->updateStatistics();

            // Process embeddings if enabled
            if ($document->knowledgeBase->settings['auto_process'] ?? true) {
                $this->processEmbeddings($document);
            }

            Log::info('Document processed successfully', [
                'document_id' => $document->id,
                'chunks_created' => count($chunks)
            ]);

        } catch (\Exception $e) {
            $document->update([
                'status' => 'failed',
                'processing_error' => $e->getMessage()
            ]);

            Log::error('Document processing failed', [
                'document_id' => $document->id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Extract content from document based on file type
     */
    protected function extractContent(Document $document): string
    {
        $filePath = Storage::path($document->file_path);
        
        if (!file_exists($filePath)) {
            throw new \Exception('Document file not found');
        }

        return match($document->file_type) {
            'txt', 'md' => $this->extractTextContent($filePath),
            'pdf' => $this->extractPdfContent($filePath),
            'docx' => $this->extractDocxContent($filePath),
            'html', 'htm' => $this->extractHtmlContent($filePath),
            'csv' => $this->extractCsvContent($filePath),
            'json' => $this->extractJsonContent($filePath),
            default => throw new \Exception("Unsupported file type: {$document->file_type}")
        };
    }

    /**
     * Extract text content from plain text files
     */
    protected function extractTextContent(string $filePath): string
    {
        $content = file_get_contents($filePath);
        return $content !== false ? $content : '';
    }

    /**
     * Extract content from PDF files
     */
    protected function extractPdfContent(string $filePath): string
    {
        // For production, you would use a library like smalot/pdfparser
        // For now, return a placeholder
        return "PDF content extraction would be implemented here using a PDF parsing library.";
    }

    /**
     * Extract content from DOCX files
     */
    protected function extractDocxContent(string $filePath): string
    {
        // For production, you would use a library like phpoffice/phpword
        // For now, return a placeholder
        return "DOCX content extraction would be implemented here using PhpWord library.";
    }

    /**
     * Extract content from HTML files
     */
    protected function extractHtmlContent(string $filePath): string
    {
        $html = file_get_contents($filePath);
        if ($html === false) return '';
        
        // Strip HTML tags and decode entities
        $text = html_entity_decode(strip_tags($html));
        
        // Clean up whitespace
        return preg_replace('/\s+/', ' ', trim($text));
    }

    /**
     * Extract content from CSV files
     */
    protected function extractCsvContent(string $filePath): string
    {
        $content = [];
        if (($handle = fopen($filePath, 'r')) !== false) {
            $headers = fgetcsv($handle);
            while (($data = fgetcsv($handle)) !== false) {
                if ($headers && count($headers) === count($data)) {
                    $row = array_combine($headers, $data);
                    $content[] = implode(': ', array_map(fn($k, $v) => "$k: $v", array_keys($row), $row));
                }
            }
            fclose($handle);
        }
        return implode("\n", $content);
    }

    /**
     * Extract content from JSON files
     */
    protected function extractJsonContent(string $filePath): string
    {
        $json = file_get_contents($filePath);
        if ($json === false) return '';
        
        $data = json_decode($json, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return $json; // Return raw content if not valid JSON
        }
        
        return $this->flattenJsonToText($data);
    }

    /**
     * Flatten JSON data to readable text
     */
    protected function flattenJsonToText($data, string $prefix = ''): string
    {
        $result = [];
        
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $newPrefix = $prefix ? "$prefix.$key" : $key;
                if (is_array($value) || is_object($value)) {
                    $result[] = $this->flattenJsonToText($value, $newPrefix);
                } else {
                    $result[] = "$newPrefix: $value";
                }
            }
        } elseif (is_object($data)) {
            foreach ($data as $key => $value) {
                $newPrefix = $prefix ? "$prefix.$key" : $key;
                if (is_array($value) || is_object($value)) {
                    $result[] = $this->flattenJsonToText($value, $newPrefix);
                } else {
                    $result[] = "$newPrefix: $value";
                }
            }
        } else {
            return "$prefix: $data";
        }
        
        return implode("\n", array_filter($result));
    }

    /**
     * Create chunks from document content
     */
    protected function createChunks(Document $document, string $content): array
    {
        $settings = $document->knowledgeBase->settings;
        $chunkSize = $settings['chunk_size'] ?? 1000;
        $chunkOverlap = $settings['chunk_overlap'] ?? 200;

        // Split content into sentences for better chunking
        $sentences = $this->splitIntoSentences($content);
        $chunks = [];
        $currentChunk = '';
        $chunkIndex = 0;

        foreach ($sentences as $sentence) {
            $testChunk = $currentChunk . ' ' . $sentence;
            
            if (strlen($testChunk) > $chunkSize && !empty($currentChunk)) {
                // Create chunk
                $chunk = DocumentChunk::create([
                    'document_id' => $document->id,
                    'knowledge_base_id' => $document->knowledge_base_id,
                    'chunk_index' => $chunkIndex,
                    'content' => trim($currentChunk),
                    'chunk_type' => 'text',
                    'token_count' => $this->estimateTokenCount(trim($currentChunk)),
                    'embedding_status' => 'pending'
                ]);
                
                $chunks[] = $chunk;
                $chunkIndex++;
                
                // Start new chunk with overlap
                if ($chunkOverlap > 0) {
                    $words = explode(' ', trim($currentChunk));
                    $overlapWords = array_slice($words, -($chunkOverlap / 4)); // Rough word estimate
                    $currentChunk = implode(' ', $overlapWords) . ' ' . $sentence;
                } else {
                    $currentChunk = $sentence;
                }
            } else {
                $currentChunk = trim($testChunk);
            }
        }

        // Create final chunk if there's remaining content
        if (!empty(trim($currentChunk))) {
            $chunk = DocumentChunk::create([
                'document_id' => $document->id,
                'knowledge_base_id' => $document->knowledge_base_id,
                'chunk_index' => $chunkIndex,
                'content' => trim($currentChunk),
                'chunk_type' => 'text',
                'token_count' => $this->estimateTokenCount(trim($currentChunk)),
                'embedding_status' => 'pending'
            ]);
            
            $chunks[] = $chunk;
        }

        return $chunks;
    }

    /**
     * Split text into sentences
     */
    protected function splitIntoSentences(string $text): array
    {
        // Simple sentence splitting - in production, use a more sophisticated approach
        $sentences = preg_split('/(?<=[.!?])\s+/', $text, -1, PREG_SPLIT_NO_EMPTY);
        return array_filter($sentences, fn($s) => strlen(trim($s)) > 10);
    }

    /**
     * Estimate token count for text
     */
    protected function estimateTokenCount(string $text): int
    {
        // Rough estimation: 1 token ≈ 4 characters for English text
        return (int) ceil(strlen($text) / 4);
    }

    /**
     * Process embeddings for document chunks
     */
    protected function processEmbeddings(Document $document): void
    {
        $chunks = $document->chunks()->where('embedding_status', 'pending')->get();
        
        foreach ($chunks as $chunk) {
            try {
                $this->embeddingService->processChunk($chunk);
            } catch (\Exception $e) {
                Log::error('Embedding processing failed', [
                    'chunk_id' => $chunk->id,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Validate uploaded file
     */
    protected function validateFile(UploadedFile $file, KnowledgeBase $knowledgeBase): void
    {
        $settings = $knowledgeBase->settings;
        
        // Check file size
        $maxSize = $settings['max_file_size'] ?? 10485760; // 10MB default
        if ($file->getSize() > $maxSize) {
            throw new \Exception("File size exceeds maximum allowed size of " . ($maxSize / 1048576) . "MB");
        }
        
        // Check file type
        $allowedTypes = $settings['allowed_file_types'] ?? ['pdf', 'txt', 'docx'];
        $fileExtension = strtolower($file->getClientOriginalExtension());
        
        if (!in_array($fileExtension, $allowedTypes)) {
            throw new \Exception("File type '{$fileExtension}' is not allowed");
        }
        
        // Check MIME type
        if (!KnowledgeBase::isFileTypeSupported($file->getMimeType())) {
            throw new \Exception("MIME type '{$file->getMimeType()}' is not supported");
        }
    }

    /**
     * Store uploaded file
     */
    protected function storeFile(UploadedFile $file, KnowledgeBase $knowledgeBase): string
    {
        $directory = "knowledge-bases/{$knowledgeBase->id}/documents";
        $filename = time() . '_' . $file->getClientOriginalName();
        
        return $file->storeAs($directory, $filename, 'local');
    }

    /**
     * Delete document and its file
     */
    public function deleteDocument(Document $document): void
    {
        try {
            // Delete file
            if ($document->file_path && Storage::exists($document->file_path)) {
                Storage::delete($document->file_path);
            }
            
            // Delete chunks
            $document->chunks()->delete();
            
            // Delete document
            $document->delete();
            
            // Update knowledge base statistics
            if ($document->knowledgeBase) {
                $document->knowledgeBase->updateStatistics();
            }
            
        } catch (\Exception $e) {
            Log::error('Document deletion failed', [
                'document_id' => $document->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
