<?php

declare(strict_types=1);

namespace App\Contracts;

interface LLMProviderInterface
{
    /**
     * Get the provider name
     */
    public function getName(): string;

    /**
     * Get the current model being used
     */
    public function getModel(): string;

    /**
     * Set the model to use
     */
    public function setModel(string $model): void;

    /**
     * Generate a response from the LLM
     */
    public function generateResponse(string $prompt, array $options = []): array;

    /**
     * Generate a streaming response from the LLM
     */
    public function generateStreamingResponse(string $prompt, array $options = []): \Generator;

    /**
     * Check if the provider is available
     */
    public function isAvailable(): bool;

    /**
     * Get available models for this provider
     */
    public function getAvailableModels(): array;

    /**
     * Get provider configuration
     */
    public function getConfig(): array;

    /**
     * Set provider configuration
     */
    public function setConfig(array $config): void;

    /**
     * Validate the provider configuration
     */
    public function validateConfig(): bool;
}
