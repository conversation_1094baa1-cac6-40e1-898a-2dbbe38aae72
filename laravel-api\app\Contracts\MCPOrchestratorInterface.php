<?php

declare(strict_types=1);

namespace App\Contracts;

interface MCPOrchestratorInterface
{
    /**
     * Process a user request through the MCP agent pipeline
     */
    public function processRequest(array $input, string $sessionId): array;

    /**
     * Register an agent with the orchestrator
     */
    public function registerAgent(MCPAgentInterface $agent): void;

    /**
     * Get all registered agents
     */
    public function getAgents(): array;

    /**
     * Get agent by name
     */
    public function getAgent(string $name): ?MCPAgentInterface;

    /**
     * Execute agents in sequence
     */
    public function executeAgentPipeline(array $agents, array $input, string $sessionId): array;

    /**
     * Handle agent communication
     */
    public function sendMessage(string $fromAgent, string $toAgent, array $payload, string $sessionId): void;

    /**
     * Get execution history for a session
     */
    public function getExecutionHistory(string $sessionId): array;
}
