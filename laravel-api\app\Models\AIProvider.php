<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class AIProvider extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'is_enabled',
        'is_primary',
        'priority',
        'configuration',
        'capabilities',
        'limits',
        'usage_stats',
        'last_tested_at',
        'status',
        'status_message'
    ];

    protected $casts = [
        'is_enabled' => 'boolean',
        'is_primary' => 'boolean',
        'priority' => 'integer',
        'configuration' => 'array',
        'capabilities' => 'array',
        'limits' => 'array',
        'usage_stats' => 'array',
        'last_tested_at' => 'datetime'
    ];

    /**
     * Get default configuration for each provider
     */
    public static function getDefaultProviders(): array
    {
        return [
            'openai' => [
                'display_name' => 'OpenAI',
                'description' => 'GPT-4, GPT-3.5-turbo, and other OpenAI models',
                'capabilities' => [
                    'chat_completion' => true,
                    'text_embedding' => true,
                    'function_calling' => true,
                    'streaming' => true,
                    'vision' => true
                ],
                'configuration' => [
                    'api_key' => null,
                    'organization_id' => null,
                    'base_url' => 'https://api.openai.com/v1',
                    'default_model' => 'gpt-4',
                    'max_tokens' => 4096,
                    'temperature' => 0.7
                ],
                'limits' => [
                    'requests_per_minute' => 3500,
                    'tokens_per_minute' => 90000,
                    'requests_per_day' => 10000
                ]
            ],
            'anthropic' => [
                'display_name' => 'Anthropic',
                'description' => 'Claude 3.5 Sonnet, Claude 3 Opus, and other Anthropic models',
                'capabilities' => [
                    'chat_completion' => true,
                    'text_embedding' => false,
                    'function_calling' => true,
                    'streaming' => true,
                    'vision' => true
                ],
                'configuration' => [
                    'api_key' => null,
                    'base_url' => 'https://api.anthropic.com',
                    'default_model' => 'claude-3-5-sonnet-20241022',
                    'max_tokens' => 4096,
                    'temperature' => 0.7
                ],
                'limits' => [
                    'requests_per_minute' => 1000,
                    'tokens_per_minute' => 40000,
                    'requests_per_day' => 5000
                ]
            ],
            'groq' => [
                'display_name' => 'Groq',
                'description' => 'Ultra-fast inference with Llama, Mixtral, and Gemma models',
                'capabilities' => [
                    'chat_completion' => true,
                    'text_embedding' => false,
                    'function_calling' => true,
                    'streaming' => true,
                    'vision' => false
                ],
                'configuration' => [
                    'api_key' => null,
                    'base_url' => 'https://api.groq.com/openai/v1',
                    'default_model' => 'llama-3.1-70b-versatile',
                    'max_tokens' => 4096,
                    'temperature' => 0.7
                ],
                'limits' => [
                    'requests_per_minute' => 30,
                    'tokens_per_minute' => 6000,
                    'requests_per_day' => 14400
                ]
            ],
            'google' => [
                'display_name' => 'Google AI',
                'description' => 'Gemini Pro and other Google AI models',
                'capabilities' => [
                    'chat_completion' => true,
                    'text_embedding' => true,
                    'function_calling' => true,
                    'streaming' => true,
                    'vision' => true
                ],
                'configuration' => [
                    'api_key' => null,
                    'base_url' => 'https://generativelanguage.googleapis.com/v1',
                    'default_model' => 'gemini-pro',
                    'max_tokens' => 4096,
                    'temperature' => 0.7
                ],
                'limits' => [
                    'requests_per_minute' => 60,
                    'tokens_per_minute' => 32000,
                    'requests_per_day' => 1500
                ]
            ],
            'openrouter' => [
                'display_name' => 'OpenRouter',
                'description' => 'Access to multiple AI models through a unified API',
                'capabilities' => [
                    'chat_completion' => true,
                    'text_embedding' => false,
                    'function_calling' => true,
                    'streaming' => true,
                    'vision' => true
                ],
                'configuration' => [
                    'api_key' => null,
                    'base_url' => 'https://openrouter.ai/api/v1',
                    'default_model' => 'anthropic/claude-3.5-sonnet',
                    'max_tokens' => 4096,
                    'temperature' => 0.7
                ],
                'limits' => [
                    'requests_per_minute' => 200,
                    'tokens_per_minute' => 40000,
                    'requests_per_day' => 1000
                ]
            ]
        ];
    }

    /**
     * Test provider connection and API key
     */
    public function testConnection(): array
    {
        try {
            $result = match($this->name) {
                'openai' => $this->testOpenAI(),
                'anthropic' => $this->testAnthropic(),
                'groq' => $this->testGroq(),
                'google' => $this->testGoogle(),
                'openrouter' => $this->testOpenRouter(),
                default => ['success' => false, 'error' => 'Unknown provider']
            };

            // Update status based on test result
            $this->update([
                'status' => $result['success'] ? 'active' : 'error',
                'status_message' => $result['success'] ? 'Connection successful' : $result['error'],
                'last_tested_at' => now()
            ]);

            return $result;

        } catch (\Exception $e) {
            $this->update([
                'status' => 'error',
                'status_message' => $e->getMessage(),
                'last_tested_at' => now()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Test OpenAI connection
     */
    protected function testOpenAI(): array
    {
        $apiKey = $this->configuration['api_key'] ?? null;
        if (!$apiKey) {
            return ['success' => false, 'error' => 'API key not configured'];
        }

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$apiKey}",
            'Content-Type' => 'application/json'
        ])->timeout(10)->post($this->configuration['base_url'] . '/chat/completions', [
            'model' => 'gpt-3.5-turbo',
            'messages' => [['role' => 'user', 'content' => 'Test connection']],
            'max_tokens' => 5
        ]);

        if ($response->successful()) {
            return ['success' => true, 'response' => $response->json()];
        }

        return ['success' => false, 'error' => $response->json()['error']['message'] ?? 'Connection failed'];
    }

    /**
     * Test Anthropic connection
     */
    protected function testAnthropic(): array
    {
        $apiKey = $this->configuration['api_key'] ?? null;
        if (!$apiKey) {
            return ['success' => false, 'error' => 'API key not configured'];
        }

        $response = Http::withHeaders([
            'x-api-key' => $apiKey,
            'Content-Type' => 'application/json',
            'anthropic-version' => '2023-06-01'
        ])->timeout(10)->post($this->configuration['base_url'] . '/v1/messages', [
            'model' => 'claude-3-haiku-20240307',
            'max_tokens' => 5,
            'messages' => [['role' => 'user', 'content' => 'Test']]
        ]);

        if ($response->successful()) {
            return ['success' => true, 'response' => $response->json()];
        }

        return ['success' => false, 'error' => $response->json()['error']['message'] ?? 'Connection failed'];
    }

    /**
     * Test Groq connection
     */
    protected function testGroq(): array
    {
        $apiKey = $this->configuration['api_key'] ?? null;
        if (!$apiKey) {
            return ['success' => false, 'error' => 'API key not configured'];
        }

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$apiKey}",
            'Content-Type' => 'application/json'
        ])->timeout(10)->post($this->configuration['base_url'] . '/chat/completions', [
            'model' => 'llama-3.1-8b-instant',
            'messages' => [['role' => 'user', 'content' => 'Test']],
            'max_tokens' => 5
        ]);

        if ($response->successful()) {
            return ['success' => true, 'response' => $response->json()];
        }

        return ['success' => false, 'error' => $response->json()['error']['message'] ?? 'Connection failed'];
    }

    /**
     * Test Google AI connection
     */
    protected function testGoogle(): array
    {
        $apiKey = $this->configuration['api_key'] ?? null;
        if (!$apiKey) {
            return ['success' => false, 'error' => 'API key not configured'];
        }

        $response = Http::timeout(10)->post(
            $this->configuration['base_url'] . "/models/gemini-pro:generateContent?key={$apiKey}",
            [
                'contents' => [
                    ['parts' => [['text' => 'Test connection']]]
                ]
            ]
        );

        if ($response->successful()) {
            return ['success' => true, 'response' => $response->json()];
        }

        return ['success' => false, 'error' => $response->json()['error']['message'] ?? 'Connection failed'];
    }

    /**
     * Test OpenRouter connection
     */
    protected function testOpenRouter(): array
    {
        $apiKey = $this->configuration['api_key'] ?? null;
        if (!$apiKey) {
            return ['success' => false, 'error' => 'API key not configured'];
        }

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$apiKey}",
            'Content-Type' => 'application/json'
        ])->timeout(10)->post($this->configuration['base_url'] . '/chat/completions', [
            'model' => 'openai/gpt-3.5-turbo',
            'messages' => [['role' => 'user', 'content' => 'Test']],
            'max_tokens' => 5
        ]);

        if ($response->successful()) {
            return ['success' => true, 'response' => $response->json()];
        }

        return ['success' => false, 'error' => $response->json()['error']['message'] ?? 'Connection failed'];
    }

    /**
     * Record usage statistics
     */
    public function recordUsage(array $usage): void
    {
        $stats = $this->usage_stats ?? [];
        $today = now()->format('Y-m-d');
        
        if (!isset($stats[$today])) {
            $stats[$today] = [
                'requests' => 0,
                'tokens' => 0,
                'errors' => 0,
                'response_time_ms' => []
            ];
        }
        
        $stats[$today]['requests'] += $usage['requests'] ?? 1;
        $stats[$today]['tokens'] += $usage['tokens'] ?? 0;
        $stats[$today]['errors'] += $usage['errors'] ?? 0;
        
        if (isset($usage['response_time_ms'])) {
            $stats[$today]['response_time_ms'][] = $usage['response_time_ms'];
        }
        
        // Keep only last 30 days
        $cutoff = now()->subDays(30)->format('Y-m-d');
        $stats = array_filter($stats, fn($key) => $key >= $cutoff, ARRAY_FILTER_USE_KEY);
        
        $this->update(['usage_stats' => $stats]);
    }

    /**
     * Get usage statistics for a date range
     */
    public function getUsageStats(int $days = 7): array
    {
        $stats = $this->usage_stats ?? [];
        $result = [];
        
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $dayStats = $stats[$date] ?? [
                'requests' => 0,
                'tokens' => 0,
                'errors' => 0,
                'response_time_ms' => []
            ];
            
            $result[$date] = [
                'requests' => $dayStats['requests'],
                'tokens' => $dayStats['tokens'],
                'errors' => $dayStats['errors'],
                'avg_response_time' => !empty($dayStats['response_time_ms']) 
                    ? array_sum($dayStats['response_time_ms']) / count($dayStats['response_time_ms'])
                    : 0
            ];
        }
        
        return $result;
    }

    /**
     * Check if provider is within rate limits
     */
    public function isWithinLimits(): bool
    {
        if (!$this->limits) {
            return true;
        }
        
        $stats = $this->getUsageStats(1);
        $today = now()->format('Y-m-d');
        $todayStats = $stats[$today] ?? ['requests' => 0, 'tokens' => 0];
        
        // Check daily limits
        if (isset($this->limits['requests_per_day']) && 
            $todayStats['requests'] >= $this->limits['requests_per_day']) {
            return false;
        }
        
        // Check minute limits (simplified - would need more sophisticated tracking)
        $cacheKey = "provider_usage_{$this->id}_" . now()->format('Y-m-d-H-i');
        $minuteRequests = Cache::get($cacheKey, 0);
        
        if (isset($this->limits['requests_per_minute']) && 
            $minuteRequests >= $this->limits['requests_per_minute']) {
            return false;
        }
        
        return true;
    }

    /**
     * Increment minute usage counter
     */
    public function incrementUsage(): void
    {
        $cacheKey = "provider_usage_{$this->id}_" . now()->format('Y-m-d-H-i');
        Cache::increment($cacheKey, 1);
        Cache::put($cacheKey, Cache::get($cacheKey, 1), 60); // Expire after 1 minute
    }

    /**
     * Scopes
     */
    public function scopeEnabled($query)
    {
        return $query->where('is_enabled', true);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'asc');
    }

    public function scopePrimary($query)
    {
        return $query->where('is_primary', true);
    }
}
