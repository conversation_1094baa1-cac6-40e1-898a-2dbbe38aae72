'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { apiClient, type Agent } from '@/lib/api'
import { Play, Settings, RefreshCw } from 'lucide-react'

export default function AgentsPage() {
  const [agents, setAgents] = useState<Agent[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [testingAgent, setTestingAgent] = useState<string | null>(null)
  const [testResults, setTestResults] = useState<Record<string, any>>({})

  const fetchAgents = async () => {
    setIsLoading(true)
    try {
      const response = await apiClient.getSystemStatus()
      if (response.success && response.data) {
        setAgents(response.data.agents)
      }
    } catch (error) {
      console.error('Failed to fetch agents:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const testAgent = async (agentName: string) => {
    setTestingAgent(agentName)
    try {
      const response = await apiClient.testAgent(agentName)
      setTestResults(prev => ({
        ...prev,
        [agentName]: response
      }))
    } catch (error) {
      console.error(`Failed to test agent ${agentName}:`, error)
    } finally {
      setTestingAgent(null)
    }
  }

  useEffect(() => {
    fetchAgents()
  }, [])

  const primaryAgents = agents.filter(agent => agent.type === 'primary')
  const secondaryAgents = agents.filter(agent => agent.type === 'secondary')

  return (
    <DashboardLayout>
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Agents</h1>
            <p className="text-gray-600">
              Manage and monitor MCP agents
            </p>
          </div>
          <Button onClick={fetchAgents} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>

        {/* Primary Agents */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Primary Agents</h2>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {primaryAgents.map((agent) => (
              <Card key={agent.name}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg capitalize">{agent.name}</CardTitle>
                    <Badge variant={agent.is_active ? 'success' : 'error'}>
                      {agent.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="text-sm text-gray-600">
                    <div>Order: {agent.execution_order}</div>
                    <div>Config: {Object.keys(agent.config || {}).length} settings</div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => testAgent(agent.name)}
                      disabled={testingAgent === agent.name}
                    >
                      {testingAgent === agent.name ? (
                        <RefreshCw className="h-3 w-3 animate-spin" />
                      ) : (
                        <Play className="h-3 w-3" />
                      )}
                    </Button>
                    <Button size="sm" variant="outline">
                      <Settings className="h-3 w-3" />
                    </Button>
                  </div>

                  {testResults[agent.name] && (
                    <div className="text-xs">
                      <Badge variant={testResults[agent.name].success ? 'success' : 'error'}>
                        {testResults[agent.name].success ? 'Test Passed' : 'Test Failed'}
                      </Badge>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Secondary Agents */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Secondary Agents</h2>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {secondaryAgents.map((agent) => (
              <Card key={agent.name}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg capitalize">{agent.name}</CardTitle>
                    <Badge variant={agent.is_active ? 'success' : 'error'}>
                      {agent.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="text-sm text-gray-600">
                    <div>Order: {agent.execution_order}</div>
                    <div>Config: {Object.keys(agent.config || {}).length} settings</div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => testAgent(agent.name)}
                      disabled={testingAgent === agent.name}
                    >
                      {testingAgent === agent.name ? (
                        <RefreshCw className="h-3 w-3 animate-spin" />
                      ) : (
                        <Play className="h-3 w-3" />
                      )}
                    </Button>
                    <Button size="sm" variant="outline">
                      <Settings className="h-3 w-3" />
                    </Button>
                  </div>

                  {testResults[agent.name] && (
                    <div className="text-xs">
                      <Badge variant={testResults[agent.name].success ? 'success' : 'error'}>
                        {testResults[agent.name].success ? 'Test Passed' : 'Test Failed'}
                      </Badge>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
