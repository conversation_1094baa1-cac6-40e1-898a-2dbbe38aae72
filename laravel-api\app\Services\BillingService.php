<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Subscription;
use App\Models\Tenant;
use Illuminate\Support\Facades\Log;
use Stripe\StripeClient;
use Stripe\Exception\ApiErrorException;

class BillingService
{
    protected StripeClient $stripe;
    protected array $plans;

    public function __construct()
    {
        $this->stripe = new StripeClient(config('services.stripe.secret'));
        $this->plans = $this->getPlansConfig();
    }

    /**
     * Get available subscription plans
     */
    public function getAvailablePlans(): array
    {
        return $this->plans;
    }

    /**
     * Create a new subscription
     */
    public function createSubscription(string $tenantId, string $planId, string $paymentMethodId, int $trialDays = 0): array
    {
        try {
            $tenant = Tenant::findOrFail($tenantId);
            $plan = $this->getPlan($planId);

            if (!$plan) {
                throw new \Exception('Invalid plan selected');
            }

            // Create or get Stripe customer
            $customer = $this->getOrCreateStripeCustomer($tenant);

            // Attach payment method to customer
            $this->stripe->paymentMethods->attach($paymentMethodId, [
                'customer' => $customer->id
            ]);

            // Set as default payment method
            $this->stripe->customers->update($customer->id, [
                'invoice_settings' => [
                    'default_payment_method' => $paymentMethodId
                ]
            ]);

            // Create subscription
            $subscriptionData = [
                'customer' => $customer->id,
                'items' => [
                    ['price' => $plan['stripe_price_id']]
                ],
                'payment_behavior' => 'default_incomplete',
                'payment_settings' => [
                    'save_default_payment_method' => 'on_subscription'
                ],
                'expand' => ['latest_invoice.payment_intent']
            ];

            if ($trialDays > 0) {
                $subscriptionData['trial_period_days'] = $trialDays;
            }

            $stripeSubscription = $this->stripe->subscriptions->create($subscriptionData);

            // Save subscription to database
            $subscription = Subscription::create([
                'tenant_id' => $tenantId,
                'plan_id' => $planId,
                'stripe_subscription_id' => $stripeSubscription->id,
                'stripe_customer_id' => $customer->id,
                'status' => $stripeSubscription->status,
                'current_period_start' => date('Y-m-d H:i:s', $stripeSubscription->current_period_start),
                'current_period_end' => date('Y-m-d H:i:s', $stripeSubscription->current_period_end),
                'trial_ends_at' => $stripeSubscription->trial_end 
                    ? date('Y-m-d H:i:s', $stripeSubscription->trial_end) 
                    : null
            ]);

            Log::info('Subscription created', [
                'tenant_id' => $tenantId,
                'subscription_id' => $subscription->id,
                'stripe_subscription_id' => $stripeSubscription->id
            ]);

            return [
                'subscription' => $subscription,
                'stripe_subscription' => $stripeSubscription,
                'client_secret' => $stripeSubscription->latest_invoice->payment_intent->client_secret ?? null
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe API error during subscription creation', [
                'error' => $e->getMessage(),
                'tenant_id' => $tenantId
            ]);
            throw new \Exception('Payment processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Update subscription plan
     */
    public function updateSubscription(string $tenantId, string $newPlanId): array
    {
        try {
            $subscription = Subscription::where('tenant_id', $tenantId)->firstOrFail();
            $newPlan = $this->getPlan($newPlanId);

            if (!$newPlan) {
                throw new \Exception('Invalid plan selected');
            }

            // Update Stripe subscription
            $stripeSubscription = $this->stripe->subscriptions->update(
                $subscription->stripe_subscription_id,
                [
                    'items' => [
                        [
                            'id' => $this->getSubscriptionItemId($subscription->stripe_subscription_id),
                            'price' => $newPlan['stripe_price_id']
                        ]
                    ],
                    'proration_behavior' => 'create_prorations'
                ]
            );

            // Update local subscription
            $subscription->update([
                'plan_id' => $newPlanId,
                'status' => $stripeSubscription->status,
                'current_period_start' => date('Y-m-d H:i:s', $stripeSubscription->current_period_start),
                'current_period_end' => date('Y-m-d H:i:s', $stripeSubscription->current_period_end)
            ]);

            Log::info('Subscription updated', [
                'tenant_id' => $tenantId,
                'old_plan' => $subscription->plan_id,
                'new_plan' => $newPlanId
            ]);

            return [
                'subscription' => $subscription->fresh(),
                'stripe_subscription' => $stripeSubscription
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe API error during subscription update', [
                'error' => $e->getMessage(),
                'tenant_id' => $tenantId
            ]);
            throw new \Exception('Subscription update failed: ' . $e->getMessage());
        }
    }

    /**
     * Cancel subscription
     */
    public function cancelSubscription(string $tenantId, bool $cancelAtPeriodEnd = true, ?string $reason = null): array
    {
        try {
            $subscription = Subscription::where('tenant_id', $tenantId)->firstOrFail();

            if ($cancelAtPeriodEnd) {
                // Cancel at period end
                $stripeSubscription = $this->stripe->subscriptions->update(
                    $subscription->stripe_subscription_id,
                    ['cancel_at_period_end' => true]
                );

                $subscription->update([
                    'canceled_at' => now(),
                    'ends_at' => $subscription->current_period_end,
                    'metadata' => array_merge($subscription->metadata ?? [], [
                        'cancellation_reason' => $reason,
                        'canceled_at_period_end' => true
                    ])
                ]);
            } else {
                // Cancel immediately
                $stripeSubscription = $this->stripe->subscriptions->cancel(
                    $subscription->stripe_subscription_id
                );

                $subscription->update([
                    'status' => 'canceled',
                    'canceled_at' => now(),
                    'ends_at' => now(),
                    'metadata' => array_merge($subscription->metadata ?? [], [
                        'cancellation_reason' => $reason,
                        'canceled_immediately' => true
                    ])
                ]);
            }

            Log::info('Subscription canceled', [
                'tenant_id' => $tenantId,
                'subscription_id' => $subscription->id,
                'cancel_at_period_end' => $cancelAtPeriodEnd
            ]);

            return [
                'subscription' => $subscription->fresh(),
                'stripe_subscription' => $stripeSubscription
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe API error during subscription cancellation', [
                'error' => $e->getMessage(),
                'tenant_id' => $tenantId
            ]);
            throw new \Exception('Subscription cancellation failed: ' . $e->getMessage());
        }
    }

    /**
     * Resume canceled subscription
     */
    public function resumeSubscription(string $tenantId): array
    {
        try {
            $subscription = Subscription::where('tenant_id', $tenantId)->firstOrFail();

            if (!$subscription->isCanceled()) {
                throw new \Exception('Subscription is not canceled');
            }

            // Resume Stripe subscription
            $stripeSubscription = $this->stripe->subscriptions->update(
                $subscription->stripe_subscription_id,
                ['cancel_at_period_end' => false]
            );

            $subscription->update([
                'status' => $stripeSubscription->status,
                'canceled_at' => null,
                'ends_at' => null,
                'metadata' => array_merge($subscription->metadata ?? [], [
                    'resumed_at' => now()->toISOString()
                ])
            ]);

            Log::info('Subscription resumed', [
                'tenant_id' => $tenantId,
                'subscription_id' => $subscription->id
            ]);

            return [
                'subscription' => $subscription->fresh(),
                'stripe_subscription' => $stripeSubscription
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe API error during subscription resume', [
                'error' => $e->getMessage(),
                'tenant_id' => $tenantId
            ]);
            throw new \Exception('Subscription resume failed: ' . $e->getMessage());
        }
    }

    /**
     * Get or create Stripe customer
     */
    protected function getOrCreateStripeCustomer(Tenant $tenant): \Stripe\Customer
    {
        if ($tenant->stripe_customer_id) {
            try {
                return $this->stripe->customers->retrieve($tenant->stripe_customer_id);
            } catch (ApiErrorException $e) {
                // Customer doesn't exist, create new one
            }
        }

        $customer = $this->stripe->customers->create([
            'email' => $tenant->email ?? $tenant->name . '@example.com',
            'name' => $tenant->name,
            'metadata' => [
                'tenant_id' => $tenant->id
            ]
        ]);

        $tenant->update(['stripe_customer_id' => $customer->id]);

        return $customer;
    }

    /**
     * Get subscription item ID
     */
    protected function getSubscriptionItemId(string $subscriptionId): string
    {
        $subscription = $this->stripe->subscriptions->retrieve($subscriptionId);
        return $subscription->items->data[0]->id;
    }

    /**
     * Get plan configuration
     */
    protected function getPlan(string $planId): ?array
    {
        return $this->plans[$planId] ?? null;
    }

    /**
     * Get plans configuration
     */
    protected function getPlansConfig(): array
    {
        return [
            'starter' => [
                'id' => 'starter',
                'name' => 'Starter',
                'description' => 'Perfect for small teams getting started',
                'price' => 29.00,
                'currency' => 'usd',
                'interval' => 'month',
                'stripe_price_id' => config('services.stripe.prices.starter'),
                'features' => [
                    '1,000 AI requests per month',
                    '5 knowledge bases',
                    '10 workflows',
                    '1 widget',
                    'Email support'
                ],
                'limits' => [
                    'ai_requests' => 1000,
                    'knowledge_bases' => 5,
                    'workflows' => 10,
                    'widgets' => 1,
                    'storage_gb' => 1
                ]
            ],
            'professional' => [
                'id' => 'professional',
                'name' => 'Professional',
                'description' => 'For growing businesses with advanced needs',
                'price' => 99.00,
                'currency' => 'usd',
                'interval' => 'month',
                'stripe_price_id' => config('services.stripe.prices.professional'),
                'features' => [
                    '10,000 AI requests per month',
                    '25 knowledge bases',
                    '50 workflows',
                    '5 widgets',
                    'Priority support',
                    'Advanced analytics'
                ],
                'limits' => [
                    'ai_requests' => 10000,
                    'knowledge_bases' => 25,
                    'workflows' => 50,
                    'widgets' => 5,
                    'storage_gb' => 10
                ]
            ],
            'enterprise' => [
                'id' => 'enterprise',
                'name' => 'Enterprise',
                'description' => 'For large organizations with custom requirements',
                'price' => 299.00,
                'currency' => 'usd',
                'interval' => 'month',
                'stripe_price_id' => config('services.stripe.prices.enterprise'),
                'features' => [
                    'Unlimited AI requests',
                    'Unlimited knowledge bases',
                    'Unlimited workflows',
                    'Unlimited widgets',
                    '24/7 phone support',
                    'Custom integrations',
                    'Dedicated account manager'
                ],
                'limits' => [
                    'ai_requests' => -1, // Unlimited
                    'knowledge_bases' => -1,
                    'workflows' => -1,
                    'widgets' => -1,
                    'storage_gb' => 100
                ]
            ]
        ];
    }

    /**
     * Handle Stripe webhook
     */
    public function handleWebhook(string $payload, string $signature): void
    {
        try {
            $event = \Stripe\Webhook::constructEvent(
                $payload,
                $signature,
                config('services.stripe.webhook_secret')
            );

            Log::info('Stripe webhook received', ['type' => $event->type]);

            switch ($event->type) {
                case 'customer.subscription.updated':
                    $this->handleSubscriptionUpdated($event->data->object);
                    break;
                case 'customer.subscription.deleted':
                    $this->handleSubscriptionDeleted($event->data->object);
                    break;
                case 'invoice.payment_succeeded':
                    $this->handlePaymentSucceeded($event->data->object);
                    break;
                case 'invoice.payment_failed':
                    $this->handlePaymentFailed($event->data->object);
                    break;
            }

        } catch (\Exception $e) {
            Log::error('Webhook handling failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Handle subscription updated webhook
     */
    protected function handleSubscriptionUpdated($stripeSubscription): void
    {
        $subscription = Subscription::where('stripe_subscription_id', $stripeSubscription->id)->first();
        
        if ($subscription) {
            $subscription->update([
                'status' => $stripeSubscription->status,
                'current_period_start' => date('Y-m-d H:i:s', $stripeSubscription->current_period_start),
                'current_period_end' => date('Y-m-d H:i:s', $stripeSubscription->current_period_end)
            ]);
        }
    }

    /**
     * Handle subscription deleted webhook
     */
    protected function handleSubscriptionDeleted($stripeSubscription): void
    {
        $subscription = Subscription::where('stripe_subscription_id', $stripeSubscription->id)->first();
        
        if ($subscription) {
            $subscription->update([
                'status' => 'canceled',
                'ends_at' => now()
            ]);
        }
    }

    /**
     * Handle payment succeeded webhook
     */
    protected function handlePaymentSucceeded($invoice): void
    {
        // Log successful payment
        Log::info('Payment succeeded', [
            'invoice_id' => $invoice->id,
            'amount' => $invoice->amount_paid,
            'customer' => $invoice->customer
        ]);
    }

    /**
     * Handle payment failed webhook
     */
    protected function handlePaymentFailed($invoice): void
    {
        // Log failed payment and potentially notify customer
        Log::warning('Payment failed', [
            'invoice_id' => $invoice->id,
            'amount' => $invoice->amount_due,
            'customer' => $invoice->customer
        ]);
    }

    /**
     * Create setup intent for payment method
     */
    public function createSetupIntent(string $tenantId): \Stripe\SetupIntent
    {
        $tenant = Tenant::findOrFail($tenantId);
        $customer = $this->getOrCreateStripeCustomer($tenant);

        return $this->stripe->setupIntents->create([
            'customer' => $customer->id,
            'payment_method_types' => ['card'],
            'usage' => 'off_session'
        ]);
    }

    /**
     * Get payment methods for tenant
     */
    public function getPaymentMethods(string $tenantId): array
    {
        $tenant = Tenant::findOrFail($tenantId);
        
        if (!$tenant->stripe_customer_id) {
            return [];
        }

        try {
            $paymentMethods = $this->stripe->paymentMethods->all([
                'customer' => $tenant->stripe_customer_id,
                'type' => 'card'
            ]);

            return array_map(function($pm) {
                return [
                    'id' => $pm->id,
                    'type' => $pm->type,
                    'card' => [
                        'brand' => $pm->card->brand,
                        'last4' => $pm->card->last4,
                        'exp_month' => $pm->card->exp_month,
                        'exp_year' => $pm->card->exp_year
                    ]
                ];
            }, $paymentMethods->data);

        } catch (ApiErrorException $e) {
            Log::error('Failed to retrieve payment methods', [
                'tenant_id' => $tenantId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Add payment method
     */
    public function addPaymentMethod(string $tenantId, string $paymentMethodId, bool $setAsDefault = false): array
    {
        $tenant = Tenant::findOrFail($tenantId);
        $customer = $this->getOrCreateStripeCustomer($tenant);

        // Attach payment method
        $this->stripe->paymentMethods->attach($paymentMethodId, [
            'customer' => $customer->id
        ]);

        if ($setAsDefault) {
            $this->stripe->customers->update($customer->id, [
                'invoice_settings' => [
                    'default_payment_method' => $paymentMethodId
                ]
            ]);
        }

        return ['payment_method_id' => $paymentMethodId];
    }

    /**
     * Remove payment method
     */
    public function removePaymentMethod(string $tenantId, string $paymentMethodId): array
    {
        $this->stripe->paymentMethods->detach($paymentMethodId);
        return ['payment_method_id' => $paymentMethodId];
    }

    /**
     * Get billing history
     */
    public function getBillingHistory(string $tenantId, int $limit = 10, int $offset = 0): array
    {
        $tenant = Tenant::findOrFail($tenantId);
        
        if (!$tenant->stripe_customer_id) {
            return [];
        }

        try {
            $invoices = $this->stripe->invoices->all([
                'customer' => $tenant->stripe_customer_id,
                'limit' => $limit,
                'starting_after' => $offset > 0 ? $this->getInvoiceIdAtOffset($tenant->stripe_customer_id, $offset) : null
            ]);

            return array_map(function($invoice) {
                return [
                    'id' => $invoice->id,
                    'amount' => $invoice->amount_paid / 100,
                    'currency' => strtoupper($invoice->currency),
                    'status' => $invoice->status,
                    'date' => date('Y-m-d', $invoice->created),
                    'invoice_url' => $invoice->hosted_invoice_url,
                    'pdf_url' => $invoice->invoice_pdf
                ];
            }, $invoices->data);

        } catch (ApiErrorException $e) {
            Log::error('Failed to retrieve billing history', [
                'tenant_id' => $tenantId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get usage statistics
     */
    public function getUsageStats(string $tenantId, string $period = 'current'): array
    {
        $subscription = Subscription::where('tenant_id', $tenantId)->first();
        
        if (!$subscription) {
            return [];
        }

        // This would integrate with your usage tracking system
        // For now, return mock data
        return [
            'ai_requests' => [
                'used' => 750,
                'limit' => $subscription->plan->limits['ai_requests'] ?? 1000,
                'percentage' => 75
            ],
            'knowledge_bases' => [
                'used' => 3,
                'limit' => $subscription->plan->limits['knowledge_bases'] ?? 5,
                'percentage' => 60
            ],
            'workflows' => [
                'used' => 7,
                'limit' => $subscription->plan->limits['workflows'] ?? 10,
                'percentage' => 70
            ],
            'widgets' => [
                'used' => 1,
                'limit' => $subscription->plan->limits['widgets'] ?? 1,
                'percentage' => 100
            ]
        ];
    }

    /**
     * Get dashboard data
     */
    public function getDashboardData(string $tenantId): array
    {
        $subscription = Subscription::where('tenant_id', $tenantId)->with('plan')->first();
        
        if (!$subscription) {
            return [
                'subscription' => null,
                'usage' => [],
                'billing_history' => [],
                'payment_methods' => []
            ];
        }

        return [
            'subscription' => $subscription,
            'usage' => $this->getUsageStats($tenantId),
            'billing_history' => $this->getBillingHistory($tenantId, 5),
            'payment_methods' => $this->getPaymentMethods($tenantId)
        ];
    }

    /**
     * Helper to get invoice ID at offset
     */
    protected function getInvoiceIdAtOffset(string $customerId, int $offset): ?string
    {
        if ($offset === 0) return null;
        
        $invoices = $this->stripe->invoices->all([
            'customer' => $customerId,
            'limit' => 1,
            'starting_after' => null
        ]);
        
        return $invoices->data[0]->id ?? null;
    }
}
