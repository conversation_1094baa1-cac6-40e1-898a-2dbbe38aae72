/**
 * Axient AI Widget
 * Embeddable chat widget for AI assistant integration
 */
(function() {
    'use strict';

    // Prevent multiple initializations
    if (window.AxientWidget) {
        return;
    }

    class AxientWidget {
        constructor() {
            this.config = null;
            this.isOpen = false;
            this.isMinimized = true;
            this.conversationId = null;
            this.messageHistory = [];
            this.container = null;
            this.chatWindow = null;
            this.messageContainer = null;
            this.inputField = null;
            this.isTyping = false;
            this.retryCount = 0;
        }

        init(config) {
            this.config = config;
            this.conversationId = this.generateSessionId();
            
            // Load conversation history if enabled
            if (config.behavior.persist_conversation) {
                this.loadConversationHistory();
            }

            this.createWidget();
            this.attachEventListeners();
            
            // Auto-open if configured
            if (config.behavior.auto_open) {
                setTimeout(() => this.openWidget(), config.behavior.auto_open_delay);
            }
        }

        createWidget() {
            // Create main container
            this.container = document.createElement('div');
            this.container.id = `axient-widget-${this.config.widgetId}`;
            this.container.className = 'axient-widget-container';
            this.container.innerHTML = this.getWidgetHTML();
            
            // Apply styles
            this.applyStyles();
            
            // Insert into page
            const targetElement = document.getElementById(`axient-widget-${this.config.widgetId}`);
            if (targetElement) {
                targetElement.appendChild(this.container);
            } else {
                document.body.appendChild(this.container);
            }

            // Get references to elements
            this.chatWindow = this.container.querySelector('.axient-chat-window');
            this.messageContainer = this.container.querySelector('.axient-messages');
            this.inputField = this.container.querySelector('.axient-input');

            // Add welcome message
            if (this.config.config.welcome_message) {
                this.addMessage(this.config.config.welcome_message, 'bot');
            }
        }

        getWidgetHTML() {
            const position = this.config.styling.position;
            const positionClass = `axient-position-${position}`;

            return `
                <div class="axient-widget ${positionClass}">
                    <!-- Chat Toggle Button -->
                    <div class="axient-toggle-btn" onclick="window.AxientWidget.toggleWidget()">
                        <svg class="axient-chat-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
                        </svg>
                        <svg class="axient-close-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                        </svg>
                    </div>

                    <!-- Chat Window -->
                    <div class="axient-chat-window" style="display: none;">
                        <!-- Header -->
                        <div class="axient-header">
                            <div class="axient-header-content">
                                <h3 class="axient-title">${this.config.config.title}</h3>
                                <div class="axient-status">
                                    <span class="axient-status-dot"></span>
                                    <span class="axient-status-text">Online</span>
                                </div>
                            </div>
                            <button class="axient-minimize-btn" onclick="window.AxientWidget.minimizeWidget()">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19 13H5v-2h14v2z"/>
                                </svg>
                            </button>
                        </div>

                        <!-- Messages -->
                        <div class="axient-messages"></div>

                        <!-- Typing Indicator -->
                        <div class="axient-typing" style="display: none;">
                            <div class="axient-typing-dots">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                            <span class="axient-typing-text">AI is typing...</span>
                        </div>

                        <!-- Input Area -->
                        <div class="axient-input-area">
                            <div class="axient-input-container">
                                <input 
                                    type="text" 
                                    class="axient-input" 
                                    placeholder="${this.config.config.placeholder}"
                                    maxlength="${this.config.config.max_message_length}"
                                />
                                <button class="axient-send-btn" onclick="window.AxientWidget.sendMessage()">
                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        applyStyles() {
            const styles = `
                .axient-widget-container {
                    position: fixed;
                    z-index: 999999;
                    font-family: ${this.config.styling.fonts.family};
                    font-size: ${this.config.styling.fonts.size};
                }

                .axient-widget {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-end;
                }

                .axient-position-bottom-right {
                    bottom: 20px;
                    right: 20px;
                }

                .axient-position-bottom-left {
                    bottom: 20px;
                    left: 20px;
                    align-items: flex-start;
                }

                .axient-toggle-btn {
                    width: 60px;
                    height: 60px;
                    border-radius: 50%;
                    background: ${this.config.styling.colors.primary};
                    color: white;
                    border: none;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    transition: all ${this.config.styling.animations.duration};
                    margin-bottom: 10px;
                }

                .axient-toggle-btn:hover {
                    transform: scale(1.1);
                    box-shadow: 0 6px 20px rgba(0,0,0,0.2);
                }

                .axient-toggle-btn svg {
                    width: 24px;
                    height: 24px;
                }

                .axient-chat-window {
                    width: ${this.config.styling.dimensions.width};
                    height: ${this.config.styling.dimensions.height};
                    background: ${this.config.styling.colors.background};
                    border-radius: ${this.config.styling.dimensions.border_radius};
                    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                    border: 1px solid #e5e7eb;
                }

                .axient-header {
                    background: ${this.config.styling.colors.primary};
                    color: white;
                    padding: 16px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .axient-title {
                    margin: 0;
                    font-size: 16px;
                    font-weight: 600;
                }

                .axient-status {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    font-size: 12px;
                    opacity: 0.9;
                }

                .axient-status-dot {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    background: #10b981;
                }

                .axient-minimize-btn {
                    background: none;
                    border: none;
                    color: white;
                    cursor: pointer;
                    padding: 4px;
                    border-radius: 4px;
                    opacity: 0.8;
                }

                .axient-minimize-btn:hover {
                    opacity: 1;
                    background: rgba(255,255,255,0.1);
                }

                .axient-minimize-btn svg {
                    width: 16px;
                    height: 16px;
                }

                .axient-messages {
                    flex: 1;
                    overflow-y: auto;
                    padding: 16px;
                    display: flex;
                    flex-direction: column;
                    gap: 12px;
                }

                .axient-message {
                    display: flex;
                    gap: 8px;
                    max-width: 80%;
                }

                .axient-message.user {
                    align-self: flex-end;
                    flex-direction: row-reverse;
                }

                .axient-message-content {
                    padding: 12px 16px;
                    border-radius: 18px;
                    line-height: 1.4;
                }

                .axient-message.bot .axient-message-content {
                    background: ${this.config.styling.colors.bot_message};
                    color: ${this.config.styling.colors.text};
                }

                .axient-message.user .axient-message-content {
                    background: ${this.config.styling.colors.user_message};
                    color: white;
                }

                .axient-typing {
                    padding: 16px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    color: #6b7280;
                    font-size: 12px;
                }

                .axient-typing-dots {
                    display: flex;
                    gap: 4px;
                }

                .axient-typing-dots span {
                    width: 6px;
                    height: 6px;
                    border-radius: 50%;
                    background: #6b7280;
                    animation: axient-typing 1.4s infinite ease-in-out;
                }

                .axient-typing-dots span:nth-child(1) { animation-delay: -0.32s; }
                .axient-typing-dots span:nth-child(2) { animation-delay: -0.16s; }

                @keyframes axient-typing {
                    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
                    40% { transform: scale(1); opacity: 1; }
                }

                .axient-input-area {
                    padding: 16px;
                    border-top: 1px solid #e5e7eb;
                }

                .axient-input-container {
                    display: flex;
                    gap: 8px;
                    align-items: center;
                }

                .axient-input {
                    flex: 1;
                    padding: 12px 16px;
                    border: 1px solid #d1d5db;
                    border-radius: 24px;
                    outline: none;
                    font-size: 14px;
                }

                .axient-input:focus {
                    border-color: ${this.config.styling.colors.primary};
                    box-shadow: 0 0 0 3px ${this.config.styling.colors.primary}20;
                }

                .axient-send-btn {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    background: ${this.config.styling.colors.primary};
                    color: white;
                    border: none;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: all ${this.config.styling.animations.duration};
                }

                .axient-send-btn:hover {
                    transform: scale(1.05);
                }

                .axient-send-btn svg {
                    width: 16px;
                    height: 16px;
                }

                @media (max-width: 480px) {
                    .axient-chat-window {
                        width: calc(100vw - 40px);
                        height: calc(100vh - 100px);
                        max-width: 400px;
                        max-height: 600px;
                    }
                }
            `;

            // Inject styles
            const styleSheet = document.createElement('style');
            styleSheet.textContent = styles;
            document.head.appendChild(styleSheet);
        }

        attachEventListeners() {
            // Enter key to send message
            this.inputField = this.container.querySelector('.axient-input');
            this.inputField.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendMessage();
                }
            });

            // Click outside to minimize
            if (this.config.behavior.minimize_on_click_outside) {
                document.addEventListener('click', (e) => {
                    if (this.isOpen && !this.container.contains(e.target)) {
                        this.minimizeWidget();
                    }
                });
            }
        }

        toggleWidget() {
            if (this.isOpen) {
                this.minimizeWidget();
            } else {
                this.openWidget();
            }
        }

        openWidget() {
            this.isOpen = true;
            this.isMinimized = false;
            this.chatWindow.style.display = 'flex';
            this.container.querySelector('.axient-chat-icon').style.display = 'none';
            this.container.querySelector('.axient-close-icon').style.display = 'block';
            this.inputField.focus();
        }

        minimizeWidget() {
            this.isOpen = false;
            this.isMinimized = true;
            this.chatWindow.style.display = 'none';
            this.container.querySelector('.axient-chat-icon').style.display = 'block';
            this.container.querySelector('.axient-close-icon').style.display = 'none';
        }

        async sendMessage() {
            const message = this.inputField.value.trim();
            if (!message) return;

            // Add user message
            this.addMessage(message, 'user');
            this.inputField.value = '';

            // Show typing indicator
            this.showTyping();

            try {
                // Send to API
                const response = await this.callAPI(message);
                this.hideTyping();
                
                if (response.success) {
                    this.addMessage(response.response.formatted_response || response.response.llm_response, 'bot');
                    this.retryCount = 0;
                } else {
                    throw new Error(response.error || 'Unknown error');
                }
            } catch (error) {
                this.hideTyping();
                this.handleError(error);
            }
        }

        async callAPI(message) {
            const response = await fetch(`${this.config.apiUrl}/mcp/process`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Widget-ID': this.config.widgetId
                },
                body: JSON.stringify({
                    message: message,
                    session_id: this.conversationId,
                    context: this.getContext()
                })
            });

            return await response.json();
        }

        addMessage(content, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `axient-message ${type}`;
            messageDiv.innerHTML = `
                <div class="axient-message-content">${this.escapeHtml(content)}</div>
            `;
            
            this.messageContainer.appendChild(messageDiv);
            this.messageContainer.scrollTop = this.messageContainer.scrollHeight;

            // Store in history
            this.messageHistory.push({ content, type, timestamp: Date.now() });
            this.saveConversationHistory();
        }

        showTyping() {
            this.isTyping = true;
            this.container.querySelector('.axient-typing').style.display = 'flex';
            this.messageContainer.scrollTop = this.messageContainer.scrollHeight;
        }

        hideTyping() {
            this.isTyping = false;
            this.container.querySelector('.axient-typing').style.display = 'none';
        }

        handleError(error) {
            console.error('Axient Widget Error:', error);
            
            if (this.retryCount < this.config.behavior.max_retries) {
                this.retryCount++;
                this.addMessage('Sorry, there was an issue. Retrying...', 'bot');
                // Could implement retry logic here
            } else {
                this.addMessage(this.config.behavior.error_message, 'bot');
                this.retryCount = 0;
            }
        }

        getContext() {
            return {
                widget_id: this.config.widgetId,
                conversation_id: this.conversationId,
                message_history: this.messageHistory.slice(-10) // Last 10 messages
            };
        }

        generateSessionId() {
            return 'widget_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
        }

        saveConversationHistory() {
            if (this.config.behavior.persist_conversation) {
                localStorage.setItem(`axient_conversation_${this.config.widgetId}`, JSON.stringify({
                    conversationId: this.conversationId,
                    messageHistory: this.messageHistory,
                    timestamp: Date.now()
                }));
            }
        }

        loadConversationHistory() {
            try {
                const saved = localStorage.getItem(`axient_conversation_${this.config.widgetId}`);
                if (saved) {
                    const data = JSON.parse(saved);
                    const age = Date.now() - data.timestamp;
                    const maxAge = this.config.behavior.conversation_timeout * 60 * 1000;
                    
                    if (age < maxAge) {
                        this.conversationId = data.conversationId;
                        this.messageHistory = data.messageHistory || [];
                    }
                }
            } catch (error) {
                console.warn('Failed to load conversation history:', error);
            }
        }

        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    }

    // Global instance
    window.AxientWidget = new AxientWidget();

})();
