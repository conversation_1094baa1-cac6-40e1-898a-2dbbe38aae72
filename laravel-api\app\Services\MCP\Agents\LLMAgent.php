<?php

declare(strict_types=1);

namespace App\Services\MCP\Agents;

use App\Services\MCP\AbstractMCPAgent;
use App\Services\LLM\LLMProviderFactory;
use App\Models\AIProvider;
use Illuminate\Support\Facades\Log;

class LLMAgent extends AbstractMCPAgent
{
    protected LLMProviderFactory $providerFactory;
    protected int $maxTokens = 2000;
    protected float $temperature = 0.7;

    public function __construct(array $config = [])
    {
        parent::__construct($config);
        $this->providerFactory = new LLMProviderFactory();
    }

    public function getName(): string
    {
        return 'llm';
    }

    public function getType(): string
    {
        return 'primary';
    }

    public function getExecutionOrder(): int
    {
        return 3; // After Intent and Retriever agents
    }

    protected function process(array $input, string $sessionId): array
    {
        // Skip if LLM processing is not required
        if (!($input['requires_llm'] ?? false)) {
            Log::info('LLM Agent skipped - no LLM processing required', [
                'session_id' => $sessionId
            ]);

            return array_merge($input, [
                'llm_response' => null,
                'llm_metadata' => [
                    'skipped' => true,
                    'reason' => 'not_required'
                ]
            ]);
        }

        $message = $input['message'] ?? '';
        $intent = $input['intent'] ?? 'general';
        $retrievedDocs = $input['retrieved_documents'] ?? [];
        $context = $input['context'] ?? [];

        Log::info('LLM Agent processing request', [
            'session_id' => $sessionId,
            'intent' => $intent,
            'retrieved_docs_count' => count($retrievedDocs),
            'message_length' => strlen($message)
        ]);

        try {
            // Build the prompt
            $prompt = $this->buildPrompt($message, $intent, $retrievedDocs, $context);

            // Get available providers with fallback
            $providers = $this->getAvailableProviders();

            if (empty($providers)) {
                throw new \Exception('No AI providers available');
            }

            $lastError = null;
            $startTime = microtime(true);

            // Try providers in order of priority
            foreach ($providers as $providerModel) {
                try {
                    // Check rate limits
                    if (!$providerModel->isWithinLimits()) {
                        Log::warning("Provider {$providerModel->name} exceeds rate limits, skipping");
                        continue;
                    }

                    // Increment usage counter
                    $providerModel->incrementUsage();

                    // Get LLM provider instance
                    $provider = $this->providerFactory->create($providerModel->name, $providerModel->configuration);

                    // Generate response
                    $response = $provider->generateResponse($prompt, [
                        'max_tokens' => $this->maxTokens,
                        'temperature' => $this->temperature,
                        'session_id' => $sessionId
                    ]);

                    $endTime = microtime(true);
                    $responseTime = ($endTime - $startTime) * 1000;

                    // Record successful usage
                    $providerModel->recordUsage([
                        'requests' => 1,
                        'tokens' => $response['tokens_used'] ?? 0,
                        'errors' => 0,
                        'response_time_ms' => $responseTime
                    ]);

                    $result = array_merge($input, [
                        'llm_response' => $response['content'],
                        'llm_metadata' => [
                            'provider' => $provider->getName(),
                            'provider_display_name' => $providerModel->display_name,
                            'model' => $provider->getModel(),
                            'tokens_used' => $response['tokens_used'] ?? 0,
                            'processing_time' => $response['processing_time'] ?? 0,
                            'prompt_tokens' => $response['prompt_tokens'] ?? 0,
                            'completion_tokens' => $response['completion_tokens'] ?? 0,
                            'temperature' => $this->temperature,
                            'max_tokens' => $this->maxTokens,
                            'response_time_ms' => $responseTime
                        ]
                    ]);

                    Log::info('LLM Agent completed generation', [
                        'session_id' => $sessionId,
                        'provider' => $provider->getName(),
                        'tokens_used' => $response['tokens_used'] ?? 0,
                        'response_length' => strlen($response['content']),
                        'response_time_ms' => $responseTime
                    ]);

                    return $result;

                } catch (\Exception $e) {
                    $lastError = $e;

                    // Record failed usage
                    $providerModel->recordUsage([
                        'requests' => 1,
                        'tokens' => 0,
                        'errors' => 1,
                        'response_time_ms' => 0
                    ]);

                    Log::warning("Provider {$providerModel->name} failed: " . $e->getMessage());
                    continue;
                }
            }

            // All providers failed
            throw new \Exception('All AI providers failed. Last error: ' . ($lastError ? $lastError->getMessage() : 'Unknown error'));

        } catch (\Exception $e) {
            Log::error('LLM Agent failed', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);

            // Fallback response
            return array_merge($input, [
                'llm_response' => $this->getFallbackResponse($intent),
                'llm_metadata' => [
                    'error' => $e->getMessage(),
                    'fallback_used' => true
                ]
            ]);
        }
    }

    protected function buildPrompt(string $message, string $intent, array $retrievedDocs, array $context): string
    {
        $systemPrompt = $this->getSystemPrompt($intent);
        $contextPrompt = $this->buildContextPrompt($retrievedDocs, $context);

        $prompt = $systemPrompt . "\n\n";

        if (!empty($contextPrompt)) {
            $prompt .= "Context Information:\n" . $contextPrompt . "\n\n";
        }

        $prompt .= "User Query: " . $message . "\n\n";
        $prompt .= "Please provide a helpful and accurate response based on the context provided.";

        return $prompt;
    }

    protected function getSystemPrompt(string $intent): string
    {
        $prompts = [
            'question' => "You are a helpful AI assistant that answers questions accurately and concisely. Use the provided context to give informative responses.",
            'request' => "You are a helpful AI assistant that helps users with their requests. Be creative and practical in your responses while staying within appropriate boundaries.",
            'search' => "You are a search assistant that helps users find relevant information. Present search results clearly and highlight the most relevant findings.",
            'conversation' => "You are a friendly AI assistant engaging in casual conversation. Be warm, helpful, and personable in your responses.",
            'workflow' => "You are a workflow assistant that helps users execute processes and automations. Be clear and step-by-step in your guidance.",
            'general' => "You are a helpful AI assistant. Provide clear, accurate, and helpful responses to user queries."
        ];

        return $prompts[$intent] ?? $prompts['general'];
    }

    protected function buildContextPrompt(array $retrievedDocs, array $context): string
    {
        $contextParts = [];

        // Add retrieved documents
        if (!empty($retrievedDocs)) {
            $contextParts[] = "Relevant Documents:";
            foreach ($retrievedDocs as $index => $doc) {
                $contextParts[] = sprintf(
                    "[%d] %s (Relevance: %.2f)\n%s",
                    $index + 1,
                    $doc['title'] ?? 'Untitled',
                    $doc['relevance_score'] ?? 0,
                    substr($doc['content'], 0, 500) . (strlen($doc['content']) > 500 ? '...' : '')
                );
            }
        }

        // Add conversation context
        if (!empty($context['previous_messages'])) {
            $contextParts[] = "\nRecent Conversation:";
            foreach (array_slice($context['previous_messages'], -3) as $msg) {
                $contextParts[] = sprintf("%s: %s", $msg['role'], $msg['content']);
            }
        }

        return implode("\n", $contextParts);
    }

    protected function getProvider()
    {
        // Get available providers with fallback
        $providers = $this->getAvailableProviders();

        if (empty($providers)) {
            throw new \Exception('No AI providers available');
        }

        // Use primary provider first, then fallback by priority
        $provider = $providers[0];

        // Check rate limits
        if (!$provider->isWithinLimits()) {
            // Try next available provider
            foreach ($providers as $fallbackProvider) {
                if ($fallbackProvider->isWithinLimits()) {
                    $provider = $fallbackProvider;
                    break;
                }
            }
        }

        // Create LLM provider instance
        return $this->providerFactory->create($provider->name, $provider->configuration);
    }

    /**
     * Get available AI providers ordered by priority
     */
    protected function getAvailableProviders(): array
    {
        return AIProvider::enabled()
            ->active()
            ->byPriority()
            ->get()
            ->toArray();
    }

    protected function getFallbackResponse(string $intent): string
    {
        $responses = [
            'question' => "I apologize, but I'm unable to process your question at the moment. Please try again later or rephrase your question.",
            'request' => "I'm sorry, but I can't complete your request right now. Please try again later.",
            'search' => "I'm unable to search for information at the moment. Please try again later.",
            'conversation' => "I'm having trouble responding right now. How else can I help you?",
            'workflow' => "I can't execute workflows at the moment. Please try again later.",
            'general' => "I'm experiencing technical difficulties. Please try again later."
        ];

        return $responses[$intent] ?? $responses['general'];
    }

    public function validateInput(array $input): bool
    {
        return isset($input['message']) &&
               isset($input['intent']) &&
               is_string($input['message']) &&
               !empty(trim($input['message']));
    }
}
