/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5CaugmentMCP%5Cnextjs-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5CaugmentMCP%5Cnextjs-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5CaugmentMCP%5Cnextjs-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5CaugmentMCP%5Cnextjs-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5CaugmentMCP%5Cnextjs-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5CaugmentMCP%5Cnextjs-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDYXVnbWVudE1DUCU1QyU1Q25leHRqcy1hZG1pbiU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBbUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXGxhcmFnb25cXFxcd3d3XFxcXGF1Z21lbnRNQ1BcXFxcbmV4dGpzLWFkbWluXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxsYXJhZ29uXFx3d3dcXGF1Z21lbnRNQ1BcXG5leHRqcy1hZG1pblxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcbGFyYWdvblxcd3d3XFxhdWdtZW50TUNQXFxuZXh0anMtYWRtaW5cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Axient MCP Dashboard\",\n    description: \"Multi-Agent Conversation Platform Administration\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFLTUE7QUFLQUM7QUFSaUI7QUFDK0I7QUFZL0MsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFDQ0MsV0FBVyxHQUFHWCwyTEFBa0IsQ0FBQyxDQUFDLEVBQUVDLGdNQUFrQixDQUFDLFlBQVksQ0FBQztzQkFFcEUsNEVBQUNDLCtEQUFZQTswQkFDVks7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsiQzpcXGxhcmFnb25cXHd3d1xcYXVnbWVudE1DUFxcbmV4dGpzLWFkbWluXFxzcmNcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEdlaXN0LCBHZWlzdF9Nb25vIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gXCJAL2NvbnRleHRzL0F1dGhDb250ZXh0XCI7XG5cbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuY29uc3QgZ2Vpc3RNb25vID0gR2Vpc3RfTW9ubyh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkF4aWVudCBNQ1AgRGFzaGJvYXJkXCIsXG4gIGRlc2NyaXB0aW9uOiBcIk11bHRpLUFnZW50IENvbnZlcnNhdGlvbiBQbGF0Zm9ybSBBZG1pbmlzdHJhdGlvblwiLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5XG4gICAgICAgIGNsYXNzTmFtZT17YCR7Z2Vpc3RTYW5zLnZhcmlhYmxlfSAke2dlaXN0TW9uby52YXJpYWJsZX0gYW50aWFsaWFzZWRgfVxuICAgICAgPlxuICAgICAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9BdXRoUHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImdlaXN0U2FucyIsImdlaXN0TW9ubyIsIkF1dGhQcm92aWRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsInZhcmlhYmxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\laragon\\www\\augmentMCP\\nextjs-admin\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useApi: () => (/* binding */ useApi),
/* harmony export */   useAuth: () => (/* binding */ useAuth),
/* harmony export */   withAdminAuth: () => (/* binding */ withAdminAuth),
/* harmony export */   withAuth: () => (/* binding */ withAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\laragon\\www\\augmentMCP\\nextjs-admin\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\laragon\\www\\augmentMCP\\nextjs-admin\\src\\contexts\\AuthContext.tsx",
"useAuth",
);const withAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call withAuth() from the server but withAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\laragon\\www\\augmentMCP\\nextjs-admin\\src\\contexts\\AuthContext.tsx",
"withAuth",
);const withAdminAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call withAdminAuth() from the server but withAdminAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\laragon\\www\\augmentMCP\\nextjs-admin\\src\\contexts\\AuthContext.tsx",
"withAdminAuth",
);const useApi = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useApi() from the server but useApi is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\laragon\\www\\augmentMCP\\nextjs-admin\\src\\contexts\\AuthContext.tsx",
"useApi",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDYXVnbWVudE1DUCU1QyU1Q25leHRqcy1hZG1pbiU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBbUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXGxhcmFnb25cXFxcd3d3XFxcXGF1Z21lbnRNQ1BcXFxcbmV4dGpzLWFkbWluXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5CaugmentMCP%5C%5Cnextjs-admin%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/dashboard-layout */ \"(ssr)/./src/components/layout/dashboard-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/workflow.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction DashboardPage() {\n    const { apiCall } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useApi)();\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchDashboardData = async ()=>{\n        setIsLoading(true);\n        try {\n            const response = await apiCall('/admin/dashboard');\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setDashboardData(data.data);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to fetch dashboard data:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            fetchDashboardData();\n            // Refresh data every 30 seconds\n            const interval = setInterval(fetchDashboardData, 30000);\n            return ({\n                \"DashboardPage.useEffect\": ()=>clearInterval(interval)\n            })[\"DashboardPage.useEffect\"];\n        }\n    }[\"DashboardPage.useEffect\"], []);\n    const formatNumber = (num)=>{\n        return new Intl.NumberFormat().format(num);\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD'\n        }).format(amount);\n    };\n    const getTrendIcon = (rate)=>{\n        if (rate > 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-4 w-4 text-green-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 102,\n            columnNumber: 26\n        }, this);\n        if (rate < 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-4 w-4 text-red-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 103,\n            columnNumber: 26\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-4 w-4 text-gray-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 104,\n            columnNumber: 12\n        }, this);\n    };\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'healthy':\n            case 'active':\n            case 'success':\n                return 'text-green-600';\n            case 'warning':\n            case 'degraded':\n                return 'text-yellow-600';\n            case 'error':\n            case 'failed':\n            case 'critical':\n                return 'text-red-600';\n            default:\n                return 'text-gray-600';\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status.toLowerCase()){\n            case 'healthy':\n            case 'active':\n            case 'success':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 16\n                }, this);\n            case 'warning':\n            case 'degraded':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n            case 'failed':\n            case 'critical':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_2__.DashboardLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-1/4 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8\",\n                            children: [\n                                ...Array(8)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-32 bg-gray-200 rounded\"\n                                }, i, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_2__.DashboardLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Welcome back! Here's what's happening with your Axient MCP++ platform.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"View Analytics\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Create Workflow\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this),\n                dashboardData?.system_health && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"System Health\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: dashboardData.system_health.overall_health === 'healthy' ? 'success' : 'destructive',\n                                        className: \"ml-auto\",\n                                        children: dashboardData.system_health.overall_health.toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"API Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    getStatusIcon(dashboardData.system_health.api_status),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `text-sm ${getStatusColor(dashboardData.system_health.api_status)}`,\n                                                        children: dashboardData.system_health.api_status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Database\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    getStatusIcon(dashboardData.system_health.database_status),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `text-sm ${getStatusColor(dashboardData.system_health.database_status)}`,\n                                                        children: dashboardData.system_health.database_status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"AI Providers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    getStatusIcon(dashboardData.system_health.ai_providers_status),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `text-sm ${getStatusColor(dashboardData.system_health.ai_providers_status)}`,\n                                                        children: dashboardData.system_health.ai_providers_status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 11\n                }, this),\n                dashboardData?.overview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Users\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatNumber(dashboardData.overview.total_users)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-xs text-muted-foreground\",\n                                            children: [\n                                                getTrendIcon(dashboardData.overview.user_growth_rate),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-1\",\n                                                    children: [\n                                                        dashboardData.overview.user_growth_rate >= 0 ? '+' : '',\n                                                        dashboardData.overview.user_growth_rate.toFixed(1),\n                                                        \"% from last month\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Active Users\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatNumber(dashboardData.overview.active_users)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                Math.round(dashboardData.overview.active_users / dashboardData.overview.total_users * 100),\n                                                \"% of total users\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Documents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatNumber(dashboardData.overview.total_documents)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Knowledge base documents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Monthly Revenue\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatCurrency(dashboardData.overview.monthly_revenue)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Recurring revenue\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Workflows\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatNumber(dashboardData.overview.total_workflows)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Automation workflows\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Executions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatNumber(dashboardData.overview.total_executions)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Workflow executions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Success Rate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: [\n                                                Math.round(dashboardData.overview.execution_success_rate),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Execution success rate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Today's Activity\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: dashboardData.quick_stats ? formatNumber(dashboardData.quick_stats.today_executions) : '0'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Executions today\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"cursor-pointer hover:shadow-md transition-shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Create Workflow\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-4\",\n                                            children: \"Build automated workflows with our drag-and-drop builder\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            className: \"w-full\",\n                                            children: [\n                                                \"Get Started\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"cursor-pointer hover:shadow-md transition-shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Upload Documents\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-4\",\n                                            children: \"Add documents to your knowledge base for AI processing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            className: \"w-full\",\n                                            variant: \"outline\",\n                                            children: [\n                                                \"Upload Files\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"cursor-pointer hover:shadow-md transition-shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-5 w-5 text-purple-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Create Widget\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-4\",\n                                            children: \"Embed AI chat widgets on your website\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            className: \"w-full\",\n                                            variant: \"outline\",\n                                            children: [\n                                                \"Create Widget\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this),\n                dashboardData?.recent_activity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: \"Recent Activity\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    dashboardData.recent_activity.slice(0, 5).map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        getStatusIcon(activity.status),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: activity.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: new Date(activity.timestamp).toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: activity.status === 'success' ? 'success' : 'secondary',\n                                                    children: activity.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, activity.id, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 19\n                                        }, this)),\n                                    dashboardData.recent_activity.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No recent activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.withAdminAuth)(DashboardPage));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/dashboard-layout.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/dashboard-layout.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardLayout: () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./sidebar */ \"(ssr)/./src/components/layout/sidebar.tsx\");\n/* harmony import */ var _header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./header */ \"(ssr)/./src/components/layout/header.tsx\");\n/* __next_internal_client_entry_do_not_use__ DashboardLayout auto */ \n\n\nfunction DashboardLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sidebar__WEBPACK_IMPORTED_MODULE_1__.Sidebar, {}, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header__WEBPACK_IMPORTED_MODULE_2__.Header, {}, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto p-6\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvZGFzaGJvYXJkLWxheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRW1DO0FBQ0Y7QUFNMUIsU0FBU0UsZ0JBQWdCLEVBQUVDLFFBQVEsRUFBd0I7SUFDaEUscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDTCw2Q0FBT0E7Ozs7OzBCQUNSLDhEQUFDSTtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNKLDJDQUFNQTs7Ozs7a0NBQ1AsOERBQUNLO3dCQUFLRCxXQUFVO2tDQUNiRjs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIkM6XFxsYXJhZ29uXFx3d3dcXGF1Z21lbnRNQ1BcXG5leHRqcy1hZG1pblxcc3JjXFxjb21wb25lbnRzXFxsYXlvdXRcXGRhc2hib2FyZC1sYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBTaWRlYmFyIH0gZnJvbSAnLi9zaWRlYmFyJ1xuaW1wb3J0IHsgSGVhZGVyIH0gZnJvbSAnLi9oZWFkZXInXG5cbmludGVyZmFjZSBEYXNoYm9hcmRMYXlvdXRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIERhc2hib2FyZExheW91dCh7IGNoaWxkcmVuIH06IERhc2hib2FyZExheW91dFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGgtc2NyZWVuIGJnLWdyYXktMTAwXCI+XG4gICAgICA8U2lkZWJhciAvPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtMSBmbGV4LWNvbCBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgPEhlYWRlciAvPlxuICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3cteS1hdXRvIHAtNlwiPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9tYWluPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJTaWRlYmFyIiwiSGVhZGVyIiwiRGFzaGJvYXJkTGF5b3V0IiwiY2hpbGRyZW4iLCJkaXYiLCJjbGFzc05hbWUiLCJtYWluIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/dashboard-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_useClientTime__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useClientTime */ \"(ssr)/./src/hooks/useClientTime.ts\");\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_RefreshCw_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,RefreshCw,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_RefreshCw_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,RefreshCw,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_RefreshCw_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,RefreshCw,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_RefreshCw_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,RefreshCw,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_RefreshCw_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,RefreshCw,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\n\n\n\nfunction Header() {\n    const { user, tenant, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [systemStatus, setSystemStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('operational');\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUserMenu, setShowUserMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { formatTime, updateTime } = (0,_hooks_useClientTime__WEBPACK_IMPORTED_MODULE_6__.useClientTime)();\n    const checkSystemStatus = async ()=>{\n        setIsRefreshing(true);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.apiClient.getSystemStatus();\n            if (response.success && response.data) {\n                setSystemStatus(response.data.system_status === 'operational' ? 'operational' : 'degraded');\n            } else {\n                setSystemStatus('down');\n            }\n        } catch (error) {\n            setSystemStatus('down');\n        } finally{\n            setIsRefreshing(false);\n            updateTime();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            checkSystemStatus();\n            const interval = setInterval(checkSystemStatus, 30000) // Check every 30 seconds\n            ;\n            return ({\n                \"Header.useEffect\": ()=>clearInterval(interval)\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], []);\n    const getStatusVariant = ()=>{\n        switch(systemStatus){\n            case 'operational':\n                return 'success';\n            case 'degraded':\n                return 'warning';\n            case 'down':\n                return 'error';\n            default:\n                return 'secondary';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"border-b bg-white px-6 py-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"MCP Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                            variant: getStatusVariant(),\n                            children: systemStatus.charAt(0).toUpperCase() + systemStatus.slice(1)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"Last updated: \",\n                                formatTime()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: checkSystemStatus,\n                            disabled: isRefreshing,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_RefreshCw_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: `h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                \"Refresh\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"icon\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_RefreshCw_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setShowUserMenu(!showUserMenu),\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_RefreshCw_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden md:inline\",\n                                            children: user?.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                showUserMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg border border-gray-200 z-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 border-b border-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: user?.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: user?.email\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 19\n                                                }, this),\n                                                tenant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-400 mt-1\",\n                                                    children: [\n                                                        tenant.name,\n                                                        \" (\",\n                                                        tenant.subscription_plan,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"py-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                    onClick: ()=>setShowUserMenu(false),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_RefreshCw_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                            lineNumber: 111,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Settings\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50\",\n                                                    onClick: ()=>{\n                                                        setShowUserMenu(false);\n                                                        logout();\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_RefreshCw_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Sign Out\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\header.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Brain,Database,LayoutDashboard,MessageSquare,Settings,Shield,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Brain,Database,LayoutDashboard,MessageSquare,Settings,Shield,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Brain,Database,LayoutDashboard,MessageSquare,Settings,Shield,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Brain,Database,LayoutDashboard,MessageSquare,Settings,Shield,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Brain,Database,LayoutDashboard,MessageSquare,Settings,Shield,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Brain,Database,LayoutDashboard,MessageSquare,Settings,Shield,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Brain,Database,LayoutDashboard,MessageSquare,Settings,Shield,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/workflow.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Brain,Database,LayoutDashboard,MessageSquare,Settings,Shield,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Brain,Database,LayoutDashboard,MessageSquare,Settings,Shield,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Brain,Database,LayoutDashboard,MessageSquare,Settings,Shield,Workflow,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\nconst navigation = [\n    {\n        name: 'Dashboard',\n        href: '/',\n        icon: _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: 'Agents',\n        href: '/agents',\n        icon: _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: 'Conversations',\n        href: '/conversations',\n        icon: _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'Knowledge Bases',\n        href: '/knowledge-bases',\n        icon: _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'AI Providers',\n        href: '/providers',\n        icon: _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: 'Tools',\n        href: '/tools',\n        icon: _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: 'Workflows',\n        href: '/workflows',\n        icon: _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: 'Analytics',\n        href: '/analytics',\n        icon: _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: 'Security',\n        href: '/security',\n        icon: _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        name: 'Settings',\n        href: '/settings',\n        icon: _barrel_optimize_names_Activity_Bot_Brain_Database_LayoutDashboard_MessageSquare_Settings_Shield_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    }\n];\nfunction Sidebar() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full w-64 flex-col bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-16 items-center px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl font-bold text-white\",\n                    children: \"Axient MCP\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 space-y-1 px-3 py-4\",\n                children: navigation.map((item)=>{\n                    const isActive = pathname === item.href;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: item.href,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('group flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors', isActive ? 'bg-gray-800 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('mr-3 h-5 w-5 flex-shrink-0', isActive ? 'text-white' : 'text-gray-400 group-hover:text-white')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 15\n                            }, this),\n                            item.name\n                        ]\n                    }, item.name, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-700 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 w-8 rounded-full bg-gray-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-white\",\n                                    children: \"Admin User\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"<EMAIL>\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvc2lkZWJhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTRCO0FBQ2lCO0FBQ2I7QUFhWDtBQUVyQixNQUFNYSxhQUFhO0lBQ2pCO1FBQ0VDLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxNQUFNYixrS0FBZUE7SUFDdkI7SUFDQTtRQUNFVyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsTUFBTVosa0tBQUdBO0lBQ1g7SUFDQTtRQUNFVSxNQUFNO1FBQ05DLE1BQU07UUFDTkMsTUFBTVgsa0tBQWFBO0lBQ3JCO0lBQ0E7UUFDRVMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLE1BQU1WLGtLQUFRQTtJQUNoQjtJQUNBO1FBQ0VRLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxNQUFNTCxrS0FBS0E7SUFDYjtJQUNBO1FBQ0VHLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxNQUFNTixrS0FBR0E7SUFDWDtJQUNBO1FBQ0VJLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxNQUFNUixtS0FBUUE7SUFDaEI7SUFDQTtRQUNFTSxNQUFNO1FBQ05DLE1BQU07UUFDTkMsTUFBTUosbUtBQVFBO0lBQ2hCO0lBQ0E7UUFDRUUsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLE1BQU1QLG1LQUFNQTtJQUNkO0lBQ0E7UUFDRUssTUFBTTtRQUNOQyxNQUFNO1FBQ05DLE1BQU1ULG1LQUFRQTtJQUNoQjtDQUNEO0FBRU0sU0FBU1U7SUFDZCxNQUFNQyxXQUFXakIsNERBQVdBO0lBRTVCLHFCQUNFLDhEQUFDa0I7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDQztvQkFBR0QsV0FBVTs4QkFBK0I7Ozs7Ozs7Ozs7OzBCQUUvQyw4REFBQ0U7Z0JBQUlGLFdBQVU7MEJBQ1pQLFdBQVdVLEdBQUcsQ0FBQyxDQUFDQztvQkFDZixNQUFNQyxXQUFXUCxhQUFhTSxLQUFLVCxJQUFJO29CQUN2QyxxQkFDRSw4REFBQ2Ysa0RBQUlBO3dCQUVIZSxNQUFNUyxLQUFLVCxJQUFJO3dCQUNmSyxXQUFXbEIsOENBQUVBLENBQ1gsc0ZBQ0F1QixXQUNJLDJCQUNBOzswQ0FHTiw4REFBQ0QsS0FBS1IsSUFBSTtnQ0FDUkksV0FBV2xCLDhDQUFFQSxDQUNYLDhCQUNBdUIsV0FBVyxlQUFlOzs7Ozs7NEJBRzdCRCxLQUFLVixJQUFJOzt1QkFmTFUsS0FBS1YsSUFBSTs7Ozs7Z0JBa0JwQjs7Ozs7OzBCQUVGLDhEQUFDSztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7Ozs7O3NDQUNmLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNNO29DQUFFTixXQUFVOzhDQUFpQzs7Ozs7OzhDQUM5Qyw4REFBQ007b0NBQUVOLFdBQVU7OENBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1qRCIsInNvdXJjZXMiOlsiQzpcXGxhcmFnb25cXHd3d1xcYXVnbWVudE1DUFxcbmV4dGpzLWFkbWluXFxzcmNcXGNvbXBvbmVudHNcXGxheW91dFxcc2lkZWJhci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcbmltcG9ydCB7IHVzZVBhdGhuYW1lIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscydcbmltcG9ydCB7XG4gIExheW91dERhc2hib2FyZCxcbiAgQm90LFxuICBNZXNzYWdlU3F1YXJlLFxuICBEYXRhYmFzZSxcbiAgU2V0dGluZ3MsXG4gIFdvcmtmbG93LFxuICBTaGllbGQsXG4gIEJhckNoYXJ0MyxcbiAgWmFwLFxuICBCcmFpbixcbiAgQWN0aXZpdHksXG59IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuY29uc3QgbmF2aWdhdGlvbiA9IFtcbiAge1xuICAgIG5hbWU6ICdEYXNoYm9hcmQnLFxuICAgIGhyZWY6ICcvJyxcbiAgICBpY29uOiBMYXlvdXREYXNoYm9hcmQsXG4gIH0sXG4gIHtcbiAgICBuYW1lOiAnQWdlbnRzJyxcbiAgICBocmVmOiAnL2FnZW50cycsXG4gICAgaWNvbjogQm90LFxuICB9LFxuICB7XG4gICAgbmFtZTogJ0NvbnZlcnNhdGlvbnMnLFxuICAgIGhyZWY6ICcvY29udmVyc2F0aW9ucycsXG4gICAgaWNvbjogTWVzc2FnZVNxdWFyZSxcbiAgfSxcbiAge1xuICAgIG5hbWU6ICdLbm93bGVkZ2UgQmFzZXMnLFxuICAgIGhyZWY6ICcva25vd2xlZGdlLWJhc2VzJyxcbiAgICBpY29uOiBEYXRhYmFzZSxcbiAgfSxcbiAge1xuICAgIG5hbWU6ICdBSSBQcm92aWRlcnMnLFxuICAgIGhyZWY6ICcvcHJvdmlkZXJzJyxcbiAgICBpY29uOiBCcmFpbixcbiAgfSxcbiAge1xuICAgIG5hbWU6ICdUb29scycsXG4gICAgaHJlZjogJy90b29scycsXG4gICAgaWNvbjogWmFwLFxuICB9LFxuICB7XG4gICAgbmFtZTogJ1dvcmtmbG93cycsXG4gICAgaHJlZjogJy93b3JrZmxvd3MnLFxuICAgIGljb246IFdvcmtmbG93LFxuICB9LFxuICB7XG4gICAgbmFtZTogJ0FuYWx5dGljcycsXG4gICAgaHJlZjogJy9hbmFseXRpY3MnLFxuICAgIGljb246IEFjdGl2aXR5LFxuICB9LFxuICB7XG4gICAgbmFtZTogJ1NlY3VyaXR5JyxcbiAgICBocmVmOiAnL3NlY3VyaXR5JyxcbiAgICBpY29uOiBTaGllbGQsXG4gIH0sXG4gIHtcbiAgICBuYW1lOiAnU2V0dGluZ3MnLFxuICAgIGhyZWY6ICcvc2V0dGluZ3MnLFxuICAgIGljb246IFNldHRpbmdzLFxuICB9LFxuXVxuXG5leHBvcnQgZnVuY3Rpb24gU2lkZWJhcigpIHtcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaC1mdWxsIHctNjQgZmxleC1jb2wgYmctZ3JheS05MDBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLTE2IGl0ZW1zLWNlbnRlciBweC02XCI+XG4gICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+QXhpZW50IE1DUDwvaDE+XG4gICAgICA8L2Rpdj5cbiAgICAgIDxuYXYgY2xhc3NOYW1lPVwiZmxleC0xIHNwYWNlLXktMSBweC0zIHB5LTRcIj5cbiAgICAgICAge25hdmlnYXRpb24ubWFwKChpdGVtKSA9PiB7XG4gICAgICAgICAgY29uc3QgaXNBY3RpdmUgPSBwYXRobmFtZSA9PT0gaXRlbS5ocmVmXG4gICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgIGtleT17aXRlbS5uYW1lfVxuICAgICAgICAgICAgICBocmVmPXtpdGVtLmhyZWZ9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgJ2dyb3VwIGZsZXggaXRlbXMtY2VudGVyIHJvdW5kZWQtbWQgcHgtMyBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMnLFxuICAgICAgICAgICAgICAgIGlzQWN0aXZlXG4gICAgICAgICAgICAgICAgICA/ICdiZy1ncmF5LTgwMCB0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTMwMCBob3ZlcjpiZy1ncmF5LTcwMCBob3Zlcjp0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8aXRlbS5pY29uXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICdtci0zIGgtNSB3LTUgZmxleC1zaHJpbmstMCcsXG4gICAgICAgICAgICAgICAgICBpc0FjdGl2ZSA/ICd0ZXh0LXdoaXRlJyA6ICd0ZXh0LWdyYXktNDAwIGdyb3VwLWhvdmVyOnRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAge2l0ZW0ubmFtZX1cbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICApXG4gICAgICAgIH0pfVxuICAgICAgPC9uYXY+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci10IGJvcmRlci1ncmF5LTcwMCBwLTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC04IHctOCByb3VuZGVkLWZ1bGwgYmctZ3JheS02MDBcIj48L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTNcIj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZVwiPkFkbWluIFVzZXI8L3A+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5hZG1pbkBheGllbnQuYWk8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJMaW5rIiwidXNlUGF0aG5hbWUiLCJjbiIsIkxheW91dERhc2hib2FyZCIsIkJvdCIsIk1lc3NhZ2VTcXVhcmUiLCJEYXRhYmFzZSIsIlNldHRpbmdzIiwiV29ya2Zsb3ciLCJTaGllbGQiLCJaYXAiLCJCcmFpbiIsIkFjdGl2aXR5IiwibmF2aWdhdGlvbiIsIm5hbWUiLCJocmVmIiwiaWNvbiIsIlNpZGViYXIiLCJwYXRobmFtZSIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwibmF2IiwibWFwIiwiaXRlbSIsImlzQWN0aXZlIiwicCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\",\n            success: \"border-transparent bg-green-100 text-green-800 hover:bg-green-200\",\n            warning: \"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200\",\n            error: \"border-transparent bg-red-100 text-red-800 hover:bg-red-200\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useApi: () => (/* binding */ useApi),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAdminAuth: () => (/* binding */ withAdminAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth,withAdminAuth,useApi auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tenant, setTenant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isAuthenticated = !!user && !!token;\n    const isAdmin = !!user && user.role === 'admin';\n    // Initialize auth state from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initAuth = {\n                \"AuthProvider.useEffect.initAuth\": async ()=>{\n                    try {\n                        const storedToken = localStorage.getItem('auth_token');\n                        const storedUser = localStorage.getItem('user_data');\n                        const storedTenant = localStorage.getItem('tenant_data');\n                        if (storedToken && storedUser) {\n                            setToken(storedToken);\n                            setUser(JSON.parse(storedUser));\n                            if (storedTenant) {\n                                setTenant(JSON.parse(storedTenant));\n                            }\n                            // Verify token is still valid\n                            await refreshUser();\n                        }\n                    } catch (error) {\n                        console.error('Auth initialization error:', error);\n                        clearAuthData();\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initAuth\"];\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (email, password, tenantSubdomain)=>{\n        try {\n            const response = await fetch('http://localhost:8000/api/auth/login', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email,\n                    password,\n                    tenant_subdomain: tenantSubdomain\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                const { user: userData, tenant: tenantData, token: authToken } = data.data;\n                setUser(userData);\n                setTenant(tenantData);\n                setToken(authToken);\n                // Store in localStorage\n                localStorage.setItem('auth_token', authToken);\n                localStorage.setItem('user_data', JSON.stringify(userData));\n                if (tenantData) {\n                    localStorage.setItem('tenant_data', JSON.stringify(tenantData));\n                }\n                return true;\n            } else {\n                throw new Error(data.error || 'Login failed');\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n            return false;\n        }\n    };\n    const logout = async ()=>{\n        try {\n            if (token) {\n                await fetch('http://localhost:8000/api/auth/logout', {\n                    method: 'POST',\n                    headers: {\n                        'Authorization': `Bearer ${token}`,\n                        'Content-Type': 'application/json'\n                    }\n                });\n            }\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            clearAuthData();\n            router.push('/login');\n        }\n    };\n    const updateProfile = async (data)=>{\n        try {\n            if (!token) return false;\n            const response = await fetch('http://localhost:8000/api/auth/profile', {\n                method: 'PUT',\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(data)\n            });\n            const result = await response.json();\n            if (result.success) {\n                const updatedUser = {\n                    ...user,\n                    ...result.data.user\n                };\n                setUser(updatedUser);\n                localStorage.setItem('user_data', JSON.stringify(updatedUser));\n                return true;\n            } else {\n                throw new Error(result.error || 'Profile update failed');\n            }\n        } catch (error) {\n            console.error('Profile update error:', error);\n            return false;\n        }\n    };\n    const refreshUser = async ()=>{\n        try {\n            if (!token) return;\n            const response = await fetch('http://localhost:8000/api/auth/me', {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                const { user: userData, tenant: tenantData } = data.data;\n                setUser(userData);\n                setTenant(tenantData);\n                // Update localStorage\n                localStorage.setItem('user_data', JSON.stringify(userData));\n                if (tenantData) {\n                    localStorage.setItem('tenant_data', JSON.stringify(tenantData));\n                }\n            } else {\n                // Token is invalid\n                clearAuthData();\n                router.push('/login');\n            }\n        } catch (error) {\n            console.error('Refresh user error:', error);\n            clearAuthData();\n            router.push('/login');\n        }\n    };\n    const clearAuthData = ()=>{\n        setUser(null);\n        setTenant(null);\n        setToken(null);\n        localStorage.removeItem('auth_token');\n        localStorage.removeItem('user_data');\n        localStorage.removeItem('tenant_data');\n    };\n    // API helper function with auth\n    const apiCall = async (endpoint, options = {})=>{\n        const headers = {\n            'Content-Type': 'application/json',\n            ...token && {\n                'Authorization': `Bearer ${token}`\n            },\n            ...tenant && {\n                'X-Tenant-ID': tenant.id\n            },\n            ...options.headers\n        };\n        const response = await fetch(`http://localhost:8000/api${endpoint}`, {\n            ...options,\n            headers\n        });\n        if (response.status === 401) {\n            // Token expired or invalid\n            clearAuthData();\n            router.push('/login');\n            throw new Error('Authentication required');\n        }\n        return response;\n    };\n    const value = {\n        user,\n        tenant,\n        token,\n        isLoading,\n        isAuthenticated,\n        isAdmin,\n        login,\n        logout,\n        updateProfile,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 252,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n// Higher-order component for protected routes\nfunction withAuth(Component) {\n    return function AuthenticatedComponent(props) {\n        const { isAuthenticated, isLoading } = useAuth();\n        const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"withAuth.AuthenticatedComponent.useEffect\": ()=>{\n                if (!isLoading && !isAuthenticated) {\n                    router.push('/login');\n                }\n            }\n        }[\"withAuth.AuthenticatedComponent.useEffect\"], [\n            isAuthenticated,\n            isLoading,\n            router\n        ]);\n        if (isLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, this);\n        }\n        if (!isAuthenticated) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 290,\n            columnNumber: 12\n        }, this);\n    };\n}\n// Higher-order component for admin-only routes\nfunction withAdminAuth(Component) {\n    return function AdminAuthenticatedComponent(props) {\n        const { isAuthenticated, isAdmin, isLoading } = useAuth();\n        const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"withAdminAuth.AdminAuthenticatedComponent.useEffect\": ()=>{\n                if (!isLoading) {\n                    if (!isAuthenticated) {\n                        router.push('/login');\n                    } else if (!isAdmin) {\n                        router.push('/unauthorized');\n                    }\n                }\n            }\n        }[\"withAdminAuth.AdminAuthenticatedComponent.useEffect\"], [\n            isAuthenticated,\n            isAdmin,\n            isLoading,\n            router\n        ]);\n        if (isLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 312,\n                columnNumber: 9\n            }, this);\n        }\n        if (!isAuthenticated || !isAdmin) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 322,\n            columnNumber: 12\n        }, this);\n    };\n}\n// Hook for making authenticated API calls\nfunction useApi() {\n    const { token, tenant } = useAuth();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const apiCall = async (endpoint, options = {})=>{\n        const headers = {\n            'Content-Type': 'application/json',\n            ...token && {\n                'Authorization': `Bearer ${token}`\n            },\n            ...tenant && {\n                'X-Tenant-ID': tenant.id\n            },\n            ...options.headers\n        };\n        const response = await fetch(`http://localhost:8000/api${endpoint}`, {\n            ...options,\n            headers\n        });\n        if (response.status === 401) {\n            router.push('/login');\n            throw new Error('Authentication required');\n        }\n        return response;\n    };\n    return {\n        apiCall\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useClientTime.ts":
/*!************************************!*\
  !*** ./src/hooks/useClientTime.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useClientTime: () => (/* binding */ useClientTime)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * Custom hook to handle time display without hydration mismatch\n * Returns null on server-side and actual time on client-side\n */ function useClientTime(initialTime) {\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useClientTime.useEffect\": ()=>{\n            setIsClient(true);\n            setTime(initialTime || new Date());\n        }\n    }[\"useClientTime.useEffect\"], [\n        initialTime\n    ]);\n    const updateTime = (newTime)=>{\n        if (isClient) {\n            setTime(newTime || new Date());\n        }\n    };\n    const formatTime = (options)=>{\n        if (!isClient || !time) {\n            return '--:--:--';\n        }\n        return time.toLocaleTimeString(undefined, options);\n    };\n    const formatDate = (options)=>{\n        if (!isClient || !time) {\n            return '--/--/----';\n        }\n        return time.toLocaleDateString(undefined, options);\n    };\n    const formatDateTime = (options)=>{\n        if (!isClient || !time) {\n            return '--/--/---- --:--:--';\n        }\n        return time.toLocaleString(undefined, options);\n    };\n    return {\n        time,\n        isClient,\n        updateTime,\n        formatTime,\n        formatDate,\n        formatDateTime\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useClientTime.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';\nclass ApiClient {\n    constructor(baseUrl = API_BASE_URL){\n        this.baseUrl = baseUrl;\n    }\n    async request(endpoint, options = {}) {\n        try {\n            const url = `${this.baseUrl}${endpoint}`;\n            const response = await fetch(url, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    ...options.headers\n                },\n                ...options\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                return {\n                    success: false,\n                    error: data.message || `HTTP ${response.status}`\n                };\n            }\n            return {\n                success: true,\n                data\n            };\n        } catch (error) {\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    // System endpoints\n    async getSystemStatus() {\n        return this.request('/test/system-status');\n    }\n    async getHealthCheck() {\n        return this.request('/health');\n    }\n    // Agent endpoints\n    async getAgents() {\n        return this.request('/mcp/agents');\n    }\n    async testAgent(agentName) {\n        return this.request(`/test/agent/${agentName}`);\n    }\n    async testMCPPipeline() {\n        return this.request('/test/mcp-pipeline');\n    }\n    async testFullPipeline() {\n        return this.request('/test/full-pipeline');\n    }\n    // MCP processing\n    async processMCPRequest(request) {\n        return this.request('/mcp/process', {\n            method: 'POST',\n            body: JSON.stringify(request)\n        });\n    }\n    async getExecutionHistory(sessionId) {\n        return this.request(`/mcp/history/${sessionId}`);\n    }\n    // Placeholder methods for future implementation\n    async getConversations() {\n        // This would be implemented when the Laravel API has conversation endpoints\n        return {\n            success: true,\n            data: []\n        };\n    }\n    async getConversation(id) {\n        // This would be implemented when the Laravel API has conversation endpoints\n        return {\n            success: true,\n            data: {\n                id,\n                session_id: '',\n                title: '',\n                message_count: 0,\n                last_activity_at: '',\n                created_at: '',\n                messages: []\n            }\n        };\n    }\n    async getKnowledgeBases() {\n        // This would be implemented when the Laravel API has knowledge base endpoints\n        return {\n            success: true,\n            data: []\n        };\n    }\n    async getWorkflows() {\n        // This would be implemented when the Laravel API has workflow endpoints\n        return {\n            success: true,\n            data: []\n        };\n    }\n    async getTools() {\n        // This would be implemented when the Laravel API has tool endpoints\n        return {\n            success: true,\n            data: []\n        };\n    }\n    // Admin endpoints\n    async getAdminDashboard() {\n        return this.request('/admin/dashboard');\n    }\n    async getAdminUsers(params) {\n        const queryString = params ? '?' + new URLSearchParams(params).toString() : '';\n        return this.request(`/admin/users${queryString}`);\n    }\n    async createAdminUser(userData) {\n        return this.request('/admin/users', {\n            method: 'POST',\n            body: JSON.stringify(userData)\n        });\n    }\n    async updateAdminUser(userId, userData) {\n        return this.request(`/admin/users/${userId}`, {\n            method: 'PUT',\n            body: JSON.stringify(userData)\n        });\n    }\n    async deleteAdminUser(userId) {\n        return this.request(`/admin/users/${userId}`, {\n            method: 'DELETE'\n        });\n    }\n    async getAdminSettings() {\n        return this.request('/admin/settings');\n    }\n}\nconst apiClient = new ApiClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatBytes: () => (/* binding */ formatBytes),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDuration: () => (/* binding */ formatDuration),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    try {\n        return new Intl.DateTimeFormat('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        }).format(new Date(date));\n    } catch (error) {\n        // Fallback for invalid dates or SSR issues\n        return 'Invalid Date';\n    }\n}\nfunction formatDuration(ms) {\n    if (ms < 1000) return `${ms}ms`;\n    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;\n    return `${(ms / 60000).toFixed(1)}m`;\n}\nfunction formatBytes(bytes) {\n    const units = [\n        'B',\n        'KB',\n        'MB',\n        'GB'\n    ];\n    let size = bytes;\n    let unitIndex = 0;\n    while(size >= 1024 && unitIndex < units.length - 1){\n        size /= 1024;\n        unitIndex++;\n    }\n    return `${size.toFixed(1)} ${units[unitIndex]}`;\n}\nfunction getStatusColor(status) {\n    switch(status.toLowerCase()){\n        case 'active':\n        case 'completed':\n        case 'success':\n            return 'text-green-600 bg-green-100';\n        case 'inactive':\n        case 'failed':\n        case 'error':\n            return 'text-red-600 bg-red-100';\n        case 'running':\n        case 'pending':\n            return 'text-yellow-600 bg-yellow-100';\n        default:\n            return 'text-gray-600 bg-gray-100';\n    }\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5CaugmentMCP%5Cnextjs-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5CaugmentMCP%5Cnextjs-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();