<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return response()->json([
                'error' => 'Authentication required',
                'message' => 'You must be logged in to access this resource'
            ], 401);
        }

        $user = Auth::user();

        // Check if user is active
        if (!$user->isActive()) {
            return response()->json([
                'error' => 'Account inactive',
                'message' => 'Your account has been deactivated'
            ], 403);
        }

        // Check if user has admin role
        if (!$user->isAdmin()) {
            return response()->json([
                'error' => 'Insufficient privileges',
                'message' => 'Admin access required for this resource'
            ], 403);
        }

        return $next($request);
    }
}
