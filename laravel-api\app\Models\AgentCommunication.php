<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AgentCommunication extends Model
{
    protected $fillable = [
        'from_agent_id',
        'to_agent_id',
        'session_id',
        'message_type',
        'payload',
    ];

    protected $casts = [
        'payload' => 'array',
    ];

    /**
     * Get the agent that sent the message
     */
    public function fromAgent(): BelongsTo
    {
        return $this->belongsTo(MCPAgent::class, 'from_agent_id');
    }

    /**
     * Get the agent that received the message
     */
    public function toAgent(): BelongsTo
    {
        return $this->belongsTo(MCPAgent::class, 'to_agent_id');
    }

    /**
     * Scope for a specific session
     */
    public function scopeForSession($query, string $sessionId)
    {
        return $query->where('session_id', $sessionId);
    }

    /**
     * Scope for request messages
     */
    public function scopeRequests($query)
    {
        return $query->where('message_type', 'request');
    }

    /**
     * Scope for response messages
     */
    public function scopeResponses($query)
    {
        return $query->where('message_type', 'response');
    }

    /**
     * Scope for event messages
     */
    public function scopeEvents($query)
    {
        return $query->where('message_type', 'event');
    }
}
