<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Widget;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class WidgetController extends Controller
{
    /**
     * Get all widgets for the current tenant
     */
    public function index(): JsonResponse
    {
        try {
            $widgets = Widget::with(['analytics'])
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($widget) {
                    return [
                        'id' => $widget->id,
                        'widget_id' => $widget->widget_id,
                        'name' => $widget->name,
                        'description' => $widget->description,
                        'status' => $widget->status,
                        'usage_count' => $widget->usage_count,
                        'last_used_at' => $widget->last_used_at,
                        'created_at' => $widget->created_at,
                        'configuration' => $widget->configuration,
                        'styling' => $widget->styling,
                        'behavior' => $widget->behavior
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $widgets
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new widget
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'nullable|string|max:1000',
                'configuration' => 'nullable|array',
                'styling' => 'nullable|array',
                'behavior' => 'nullable|array',
                'status' => ['nullable', Rule::in(['active', 'inactive', 'draft'])]
            ]);

            $widget = Widget::create($validated);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $widget->id,
                    'widget_id' => $widget->widget_id,
                    'name' => $widget->name,
                    'description' => $widget->description,
                    'status' => $widget->status,
                    'embed_code' => $widget->embed_code,
                    'configuration' => $widget->configuration,
                    'styling' => $widget->styling,
                    'behavior' => $widget->behavior,
                    'created_at' => $widget->created_at
                ],
                'message' => 'Widget created successfully'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific widget
     */
    public function show(Widget $widget): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $widget->id,
                    'widget_id' => $widget->widget_id,
                    'name' => $widget->name,
                    'description' => $widget->description,
                    'status' => $widget->status,
                    'embed_code' => $widget->embed_code,
                    'configuration' => $widget->configuration,
                    'styling' => $widget->styling,
                    'behavior' => $widget->behavior,
                    'usage_count' => $widget->usage_count,
                    'last_used_at' => $widget->last_used_at,
                    'analytics' => $widget->getAnalytics(),
                    'created_at' => $widget->created_at,
                    'updated_at' => $widget->updated_at
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a widget
     */
    public function update(Request $request, Widget $widget): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'sometimes|string|max:255',
                'description' => 'nullable|string|max:1000',
                'configuration' => 'nullable|array',
                'styling' => 'nullable|array',
                'behavior' => 'nullable|array',
                'status' => ['nullable', Rule::in(['active', 'inactive', 'draft'])]
            ]);

            $widget->update($validated);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $widget->id,
                    'widget_id' => $widget->widget_id,
                    'name' => $widget->name,
                    'description' => $widget->description,
                    'status' => $widget->status,
                    'embed_code' => $widget->embed_code,
                    'configuration' => $widget->configuration,
                    'styling' => $widget->styling,
                    'behavior' => $widget->behavior,
                    'updated_at' => $widget->updated_at
                ],
                'message' => 'Widget updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a widget
     */
    public function destroy(Widget $widget): JsonResponse
    {
        try {
            $widget->delete();

            return response()->json([
                'success' => true,
                'message' => 'Widget deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get widget embed code
     */
    public function getEmbedCode(Widget $widget): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'data' => [
                    'widget_id' => $widget->widget_id,
                    'embed_code' => $widget->embed_code,
                    'preview_url' => config('app.url') . "/widget/preview/{$widget->widget_id}"
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Preview widget
     */
    public function preview(Widget $widget): JsonResponse
    {
        try {
            $config = array_merge($widget->getDefaultConfiguration(), $widget->configuration ?? []);
            $styling = array_merge($widget->getDefaultStyling(), $widget->styling ?? []);
            $behavior = array_merge($widget->getDefaultBehavior(), $widget->behavior ?? []);

            return response()->json([
                'success' => true,
                'data' => [
                    'widget_id' => $widget->widget_id,
                    'name' => $widget->name,
                    'config' => $config,
                    'styling' => $styling,
                    'behavior' => $behavior,
                    'api_url' => config('app.url') . '/api'
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get widget analytics
     */
    public function analytics(Widget $widget): JsonResponse
    {
        try {
            $analytics = $widget->getAnalytics();

            return response()->json([
                'success' => true,
                'data' => $analytics
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Duplicate a widget
     */
    public function duplicate(Widget $widget): JsonResponse
    {
        try {
            $newWidget = $widget->replicate();
            $newWidget->name = $widget->name . ' (Copy)';
            $newWidget->widget_id = null; // Will be auto-generated
            $newWidget->embed_code = null; // Will be auto-generated
            $newWidget->usage_count = 0;
            $newWidget->last_used_at = null;
            $newWidget->save();

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $newWidget->id,
                    'widget_id' => $newWidget->widget_id,
                    'name' => $newWidget->name,
                    'embed_code' => $newWidget->embed_code
                ],
                'message' => 'Widget duplicated successfully'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
