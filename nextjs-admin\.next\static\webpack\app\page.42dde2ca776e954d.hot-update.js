"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/dashboard-layout */ \"(app-pages-browser)/./src/components/layout/dashboard-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/workflow.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,Bot,CheckCircle,DollarSign,FileText,MessageSquare,Plus,Shield,TrendingDown,TrendingUp,Users,Workflow,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { apiCall } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useApi)();\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchDashboardData = async ()=>{\n        setIsLoading(true);\n        try {\n            const response = await apiCall('/admin/dashboard');\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setDashboardData(data.data);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to fetch dashboard data:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            fetchDashboardData();\n            // Refresh data every 30 seconds\n            const interval = setInterval(fetchDashboardData, 30000);\n            return ({\n                \"DashboardPage.useEffect\": ()=>clearInterval(interval)\n            })[\"DashboardPage.useEffect\"];\n        }\n    }[\"DashboardPage.useEffect\"], []);\n    const formatNumber = (num)=>{\n        return new Intl.NumberFormat().format(num);\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD'\n        }).format(amount);\n    };\n    const getTrendIcon = (rate)=>{\n        if (rate > 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-4 w-4 text-green-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 102,\n            columnNumber: 26\n        }, this);\n        if (rate < 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-4 w-4 text-red-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 103,\n            columnNumber: 26\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-4 w-4 text-gray-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 104,\n            columnNumber: 12\n        }, this);\n    };\n    const getStatusColor = (status)=>{\n        switch(status.toLowerCase()){\n            case 'healthy':\n            case 'active':\n            case 'success':\n                return 'text-green-600';\n            case 'warning':\n            case 'degraded':\n                return 'text-yellow-600';\n            case 'error':\n            case 'failed':\n            case 'critical':\n                return 'text-red-600';\n            default:\n                return 'text-gray-600';\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status.toLowerCase()){\n            case 'healthy':\n            case 'active':\n            case 'success':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 16\n                }, this);\n            case 'warning':\n            case 'degraded':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n            case 'failed':\n            case 'critical':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_2__.DashboardLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-1/4 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8\",\n                            children: [\n                                ...Array(8)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-32 bg-gray-200 rounded\"\n                                }, i, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_2__.DashboardLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Welcome back! Here's what's happening with your Axient MCP++ platform.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"View Analytics\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Create Workflow\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this),\n                (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.system_health) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"System Health\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: dashboardData.system_health.overall_health === 'healthy' ? 'success' : 'destructive',\n                                        className: \"ml-auto\",\n                                        children: dashboardData.system_health.overall_health.toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"API Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    getStatusIcon(dashboardData.system_health.api_status),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm \".concat(getStatusColor(dashboardData.system_health.api_status)),\n                                                        children: dashboardData.system_health.api_status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Database\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    getStatusIcon(dashboardData.system_health.database_status),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm \".concat(getStatusColor(dashboardData.system_health.database_status)),\n                                                        children: dashboardData.system_health.database_status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"AI Providers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    getStatusIcon(dashboardData.system_health.ai_providers_status),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm \".concat(getStatusColor(dashboardData.system_health.ai_providers_status)),\n                                                        children: dashboardData.system_health.ai_providers_status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 11\n                }, this),\n                (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.overview) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Users\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatNumber(dashboardData.overview.total_users)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-xs text-muted-foreground\",\n                                            children: [\n                                                getTrendIcon(dashboardData.overview.user_growth_rate),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-1\",\n                                                    children: [\n                                                        dashboardData.overview.user_growth_rate >= 0 ? '+' : '',\n                                                        dashboardData.overview.user_growth_rate.toFixed(1),\n                                                        \"% from last month\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Active Users\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatNumber(dashboardData.overview.active_users)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                Math.round(dashboardData.overview.active_users / dashboardData.overview.total_users * 100),\n                                                \"% of total users\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Documents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatNumber(dashboardData.overview.total_documents)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Knowledge base documents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Monthly Revenue\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatCurrency(dashboardData.overview.monthly_revenue)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Recurring revenue\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Workflows\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatNumber(dashboardData.overview.total_workflows)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Automation workflows\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Executions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: formatNumber(dashboardData.overview.total_executions)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Workflow executions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Success Rate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: [\n                                                Math.round(dashboardData.overview.execution_success_rate),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Execution success rate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Today's Activity\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: dashboardData.quick_stats ? formatNumber(dashboardData.quick_stats.today_executions) : '0'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Executions today\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"cursor-pointer hover:shadow-md transition-shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Create Workflow\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-4\",\n                                            children: \"Build automated workflows with our drag-and-drop builder\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            className: \"w-full\",\n                                            children: [\n                                                \"Get Started\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"cursor-pointer hover:shadow-md transition-shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Upload Documents\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-4\",\n                                            children: \"Add documents to your knowledge base for AI processing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            className: \"w-full\",\n                                            variant: \"outline\",\n                                            children: [\n                                                \"Upload Files\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"cursor-pointer hover:shadow-md transition-shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-5 w-5 text-purple-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Create Widget\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-4\",\n                                            children: \"Embed AI chat widgets on your website\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            className: \"w-full\",\n                                            variant: \"outline\",\n                                            children: [\n                                                \"Create Widget\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this),\n                (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.recent_activity) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: \"Recent Activity\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    dashboardData.recent_activity.slice(0, 5).map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        getStatusIcon(activity.status),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: activity.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: new Date(activity.timestamp).toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: activity.status === 'success' ? 'success' : 'secondary',\n                                                    children: activity.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, activity.id, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 19\n                                        }, this)),\n                                    dashboardData.recent_activity.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_Bot_CheckCircle_DollarSign_FileText_MessageSquare_Plus_Shield_TrendingDown_TrendingUp_Users_Workflow_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No recent activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"20Ur2ACxIumlHAyf4fS8XkcGsfs=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useApi\n    ];\n});\n_c = DashboardPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_c1 = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.withAdminAuth)(DashboardPage));\nvar _c, _c1;\n$RefreshReg$(_c, \"DashboardPage\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});