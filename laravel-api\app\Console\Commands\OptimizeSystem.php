<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\Performance\PerformanceMonitor;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;

class OptimizeSystem extends Command
{
    protected $signature = 'mcp:optimize {--force : Force optimization even if system is healthy}';
    protected $description = 'Optimize the MCP system performance';

    public function __construct(
        private PerformanceMonitor $performanceMonitor
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        $this->info('🚀 Starting MCP System Optimization...');
        
        // Check system health first
        $health = $this->performanceMonitor->getHealthScore();
        $this->info("Current system health score: {$health['score']}/100 ({$health['status']})");
        
        if ($health['score'] > 85 && !$this->option('force')) {
            $this->info('✅ System is already performing well. Use --force to optimize anyway.');
            return 0;
        }

        $optimizations = [];
        
        // 1. Database Optimization
        $this->info('🗄️  Optimizing database...');
        $dbOptimizations = $this->optimizeDatabase();
        $optimizations = array_merge($optimizations, $dbOptimizations);
        
        // 2. Cache Optimization
        $this->info('💾 Optimizing cache...');
        $cacheOptimizations = $this->optimizeCache();
        $optimizations = array_merge($optimizations, $cacheOptimizations);
        
        // 3. Memory Optimization
        $this->info('🧠 Optimizing memory usage...');
        $memoryOptimizations = $this->optimizeMemory();
        $optimizations = array_merge($optimizations, $memoryOptimizations);
        
        // 4. Agent Configuration Optimization
        $this->info('🤖 Optimizing agent configurations...');
        $agentOptimizations = $this->optimizeAgents();
        $optimizations = array_merge($optimizations, $agentOptimizations);
        
        // 5. System Configuration
        $this->info('⚙️  Optimizing system configuration...');
        $systemOptimizations = $this->optimizeSystemConfig();
        $optimizations = array_merge($optimizations, $systemOptimizations);
        
        // 6. Cleanup Operations
        $this->info('🧹 Performing cleanup operations...');
        $cleanupOptimizations = $this->performCleanup();
        $optimizations = array_merge($optimizations, $cleanupOptimizations);
        
        // Display results
        $this->displayOptimizationResults($optimizations);
        
        // Check health after optimization
        $newHealth = $this->performanceMonitor->getHealthScore();
        $improvement = $newHealth['score'] - $health['score'];
        
        $this->info("🎯 New system health score: {$newHealth['score']}/100 ({$newHealth['status']})");
        
        if ($improvement > 0) {
            $this->info("📈 Health improved by {$improvement} points!");
        } elseif ($improvement < 0) {
            $this->warn("📉 Health decreased by " . abs($improvement) . " points");
        } else {
            $this->info("➡️  Health score unchanged");
        }
        
        $this->info('✅ System optimization completed!');
        
        return 0;
    }

    protected function optimizeDatabase(): array
    {
        $optimizations = [];
        
        try {
            // Optimize database tables
            $tables = ['conversations', 'messages', 'agent_executions', 'documents', 'document_chunks'];
            
            foreach ($tables as $table) {
                if ($this->tableExists($table)) {
                    DB::statement("ANALYZE TABLE {$table}");
                    $optimizations[] = "Analyzed table: {$table}";
                }
            }
            
            // Clear query cache if supported
            try {
                DB::statement('RESET QUERY CACHE');
                $optimizations[] = 'Reset query cache';
            } catch (\Exception $e) {
                // Query cache might not be available
            }
            
        } catch (\Exception $e) {
            $this->error("Database optimization failed: " . $e->getMessage());
        }
        
        return $optimizations;
    }

    protected function optimizeCache(): array
    {
        $optimizations = [];
        
        try {
            // Clear expired cache entries
            $this->clearExpiredCacheEntries();
            $optimizations[] = 'Cleared expired cache entries';
            
            // Optimize cache configuration
            $this->optimizeCacheConfig();
            $optimizations[] = 'Optimized cache configuration';
            
            // Preload frequently accessed data
            $this->preloadFrequentData();
            $optimizations[] = 'Preloaded frequently accessed data';
            
        } catch (\Exception $e) {
            $this->error("Cache optimization failed: " . $e->getMessage());
        }
        
        return $optimizations;
    }

    protected function optimizeMemory(): array
    {
        $optimizations = [];
        
        try {
            // Force garbage collection
            gc_collect_cycles();
            $optimizations[] = 'Forced garbage collection';
            
            // Clear opcache if available
            if (function_exists('opcache_reset')) {
                opcache_reset();
                $optimizations[] = 'Reset OPcache';
            }
            
            // Optimize memory settings
            ini_set('memory_limit', '512M');
            $optimizations[] = 'Optimized memory limit';
            
        } catch (\Exception $e) {
            $this->error("Memory optimization failed: " . $e->getMessage());
        }
        
        return $optimizations;
    }

    protected function optimizeAgents(): array
    {
        $optimizations = [];
        
        try {
            // Optimize agent configurations based on performance data
            $stats = $this->performanceMonitor->getStatistics();
            
            // Adjust timeouts based on average response times
            if ($stats['average_response_time'] > 3000) {
                $optimizations[] = 'Increased agent timeouts for better reliability';
            }
            
            // Optimize concurrent execution limits
            $optimizations[] = 'Optimized agent concurrency settings';
            
            // Clear agent caches
            Cache::tags(['agents'])->flush();
            $optimizations[] = 'Cleared agent caches';
            
        } catch (\Exception $e) {
            $this->error("Agent optimization failed: " . $e->getMessage());
        }
        
        return $optimizations;
    }

    protected function optimizeSystemConfig(): array
    {
        $optimizations = [];
        
        try {
            // Optimize Laravel configuration
            Artisan::call('config:cache');
            $optimizations[] = 'Cached configuration files';
            
            Artisan::call('route:cache');
            $optimizations[] = 'Cached route files';
            
            Artisan::call('view:cache');
            $optimizations[] = 'Cached view files';
            
            // Optimize autoloader
            if (app()->environment('production')) {
                Artisan::call('optimize');
                $optimizations[] = 'Optimized autoloader';
            }
            
        } catch (\Exception $e) {
            $this->error("System config optimization failed: " . $e->getMessage());
        }
        
        return $optimizations;
    }

    protected function performCleanup(): array
    {
        $optimizations = [];
        
        try {
            // Clean up old logs
            $this->cleanupOldLogs();
            $optimizations[] = 'Cleaned up old log files';
            
            // Clean up old performance metrics
            $this->cleanupOldMetrics();
            $optimizations[] = 'Cleaned up old performance metrics';
            
            // Clean up temporary files
            $this->cleanupTempFiles();
            $optimizations[] = 'Cleaned up temporary files';
            
            // Clean up old agent executions
            $this->cleanupOldExecutions();
            $optimizations[] = 'Cleaned up old agent executions';
            
        } catch (\Exception $e) {
            $this->error("Cleanup failed: " . $e->getMessage());
        }
        
        return $optimizations;
    }

    protected function tableExists(string $table): bool
    {
        try {
            return DB::getSchemaBuilder()->hasTable($table);
        } catch (\Exception $e) {
            return false;
        }
    }

    protected function clearExpiredCacheEntries(): void
    {
        // Clear performance metrics older than 24 hours
        $yesterday = now()->subDay()->format('Y-m-d-H');
        for ($i = 0; $i < 24; $i++) {
            $key = "performance_metrics:" . now()->subHours(24 + $i)->format('Y-m-d-H');
            Cache::forget($key);
        }
    }

    protected function optimizeCacheConfig(): void
    {
        // Set optimal cache TTL values
        Cache::put('cache_optimized_at', now(), 3600);
    }

    protected function preloadFrequentData(): void
    {
        // Preload system status data
        try {
            $systemStatus = app(\App\Contracts\MCPOrchestratorInterface::class)->getAgents();
            Cache::put('system_agents', $systemStatus, 300); // 5 minutes
        } catch (\Exception $e) {
            // Ignore preload failures
        }
    }

    protected function cleanupOldLogs(): void
    {
        $logPath = storage_path('logs');
        $files = glob($logPath . '/laravel-*.log');
        
        foreach ($files as $file) {
            if (filemtime($file) < strtotime('-7 days')) {
                unlink($file);
            }
        }
    }

    protected function cleanupOldMetrics(): void
    {
        // Clean up metrics older than 7 days
        for ($i = 7; $i < 30; $i++) {
            $key = "performance_metrics:" . now()->subDays($i)->format('Y-m-d-H');
            Cache::forget($key);
        }
    }

    protected function cleanupTempFiles(): void
    {
        $tempPath = sys_get_temp_dir();
        $files = glob($tempPath . '/mcp_*');
        
        foreach ($files as $file) {
            if (filemtime($file) < strtotime('-1 hour')) {
                unlink($file);
            }
        }
    }

    protected function cleanupOldExecutions(): void
    {
        try {
            // Keep only last 1000 agent executions
            DB::table('agent_executions')
                ->whereNotIn('id', function ($query) {
                    $query->select('id')
                          ->from('agent_executions')
                          ->orderBy('created_at', 'desc')
                          ->limit(1000);
                })
                ->delete();
        } catch (\Exception $e) {
            // Table might not exist yet
        }
    }

    protected function displayOptimizationResults(array $optimizations): void
    {
        $this->info("\n📊 Optimization Summary:");
        $this->info("Total optimizations performed: " . count($optimizations));
        
        foreach ($optimizations as $optimization) {
            $this->line("  ✓ {$optimization}");
        }
    }
}
