<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Services\BillingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BillingController extends Controller
{
    protected BillingService $billingService;

    public function __construct(BillingService $billingService)
    {
        $this->billingService = $billingService;
    }

    /**
     * Get current subscription details
     */
    public function getSubscription(): JsonResponse
    {
        try {
            $tenantId = tenant('id');
            $subscription = Subscription::where('tenant_id', $tenantId)
                ->with(['plan'])
                ->first();

            if (!$subscription) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'subscription' => null,
                        'has_subscription' => false,
                        'trial_available' => true
                    ]
                ]);
            }

            $usage = $subscription->getCurrentUsage();
            $usagePercentages = $subscription->getUsagePercentages();

            return response()->json([
                'success' => true,
                'data' => [
                    'subscription' => [
                        'id' => $subscription->id,
                        'plan' => $subscription->plan,
                        'status' => $subscription->status,
                        'status_label' => $subscription->getStatusLabel(),
                        'status_color' => $subscription->getStatusColor(),
                        'current_period_start' => $subscription->current_period_start,
                        'current_period_end' => $subscription->current_period_end,
                        'trial_ends_at' => $subscription->trial_ends_at,
                        'canceled_at' => $subscription->canceled_at,
                        'is_active' => $subscription->isActive(),
                        'on_trial' => $subscription->onTrial(),
                        'is_canceled' => $subscription->isCanceled(),
                        'days_remaining' => $subscription->getDaysRemaining(),
                        'usage' => $usage,
                        'usage_percentages' => $usagePercentages,
                        'has_exceeded_limits' => $subscription->hasExceededLimits()
                    ],
                    'has_subscription' => true,
                    'trial_available' => false
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available plans
     */
    public function getPlans(): JsonResponse
    {
        try {
            $plans = $this->billingService->getAvailablePlans();

            return response()->json([
                'success' => true,
                'data' => $plans
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new subscription
     */
    public function createSubscription(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'plan_id' => 'required|string',
                'payment_method_id' => 'required|string',
                'trial_days' => 'nullable|integer|min:0|max:30'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenantId = tenant('id');
            $result = $this->billingService->createSubscription(
                $tenantId,
                $request->plan_id,
                $request->payment_method_id,
                $request->trial_days ?? 0
            );

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Subscription created successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update subscription plan
     */
    public function updateSubscription(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'plan_id' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenantId = tenant('id');
            $result = $this->billingService->updateSubscription(
                $tenantId,
                $request->plan_id
            );

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Subscription updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel subscription
     */
    public function cancelSubscription(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'cancel_at_period_end' => 'nullable|boolean',
                'reason' => 'nullable|string|max:500'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenantId = tenant('id');
            $result = $this->billingService->cancelSubscription(
                $tenantId,
                $request->boolean('cancel_at_period_end', true),
                $request->reason
            );

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Subscription canceled successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Resume canceled subscription
     */
    public function resumeSubscription(): JsonResponse
    {
        try {
            $tenantId = tenant('id');
            $result = $this->billingService->resumeSubscription($tenantId);

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Subscription resumed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get billing history
     */
    public function getBillingHistory(Request $request): JsonResponse
    {
        try {
            $limit = $request->integer('limit', 10);
            $offset = $request->integer('offset', 0);

            $tenantId = tenant('id');
            $history = $this->billingService->getBillingHistory($tenantId, $limit, $offset);

            return response()->json([
                'success' => true,
                'data' => $history
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get usage statistics
     */
    public function getUsageStats(Request $request): JsonResponse
    {
        try {
            $period = $request->input('period', 'current'); // current, last_month, last_3_months
            
            $tenantId = tenant('id');
            $stats = $this->billingService->getUsageStats($tenantId, $period);

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment methods
     */
    public function getPaymentMethods(): JsonResponse
    {
        try {
            $tenantId = tenant('id');
            $paymentMethods = $this->billingService->getPaymentMethods($tenantId);

            return response()->json([
                'success' => true,
                'data' => $paymentMethods
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add payment method
     */
    public function addPaymentMethod(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'payment_method_id' => 'required|string',
                'set_as_default' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $tenantId = tenant('id');
            $result = $this->billingService->addPaymentMethod(
                $tenantId,
                $request->payment_method_id,
                $request->boolean('set_as_default', false)
            );

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Payment method added successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove payment method
     */
    public function removePaymentMethod(Request $request, string $paymentMethodId): JsonResponse
    {
        try {
            $tenantId = tenant('id');
            $result = $this->billingService->removePaymentMethod($tenantId, $paymentMethodId);

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Payment method removed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create setup intent for adding payment method
     */
    public function createSetupIntent(): JsonResponse
    {
        try {
            $tenantId = tenant('id');
            $setupIntent = $this->billingService->createSetupIntent($tenantId);

            return response()->json([
                'success' => true,
                'data' => [
                    'client_secret' => $setupIntent->client_secret,
                    'setup_intent_id' => $setupIntent->id
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle Stripe webhook
     */
    public function handleWebhook(Request $request): JsonResponse
    {
        try {
            $payload = $request->getContent();
            $signature = $request->header('Stripe-Signature');

            $this->billingService->handleWebhook($payload, $signature);

            return response()->json(['success' => true]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get billing dashboard data
     */
    public function getDashboard(): JsonResponse
    {
        try {
            $tenantId = tenant('id');
            $dashboard = $this->billingService->getDashboardData($tenantId);

            return response()->json([
                'success' => true,
                'data' => $dashboard
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
