# Stripe Production Configuration Guide

## 🚀 **STRIPE PRODUCTION SETUP FOR AXIENT MCP++**

### **1. Stripe Account Setup**

#### **Create Production Stripe Account**
1. Go to [Stripe Dashboard](https://dashboard.stripe.com)
2. Switch to **Live mode** (toggle in top-left)
3. Complete account verification:
   - Business information
   - Bank account details
   - Tax information
   - Identity verification

#### **API Keys Configuration**
1. Navigate to **Developers > API keys**
2. Copy the **Publishable key** (starts with `pk_live_`)
3. Copy the **Secret key** (starts with `sk_live_`)
4. Update your `.env.production` file:
   ```bash
   STRIPE_KEY=pk_live_YOUR_PUBLISHABLE_KEY
   STRIPE_SECRET=sk_live_YOUR_SECRET_KEY
   ```

### **2. Product and Price Setup**

#### **Create Products in Stripe Dashboard**

**Starter Plan:**
```bash
# Create Product
curl https://api.stripe.com/v1/products \
  -u sk_live_YOUR_SECRET_KEY: \
  -d name="Axient MCP++ Starter" \
  -d description="Perfect for small teams getting started" \
  -d metadata[plan_id]="starter"

# Create Price
curl https://api.stripe.com/v1/prices \
  -u sk_live_YOUR_SECRET_KEY: \
  -d product="prod_PRODUCT_ID" \
  -d unit_amount=2900 \
  -d currency=usd \
  -d recurring[interval]=month \
  -d metadata[plan_id]="starter"
```

**Professional Plan:**
```bash
# Create Product
curl https://api.stripe.com/v1/products \
  -u sk_live_YOUR_SECRET_KEY: \
  -d name="Axient MCP++ Professional" \
  -d description="For growing businesses with advanced needs" \
  -d metadata[plan_id]="professional"

# Create Price
curl https://api.stripe.com/v1/prices \
  -u sk_live_YOUR_SECRET_KEY: \
  -d product="prod_PRODUCT_ID" \
  -d unit_amount=9900 \
  -d currency=usd \
  -d recurring[interval]=month \
  -d metadata[plan_id]="professional"
```

**Enterprise Plan:**
```bash
# Create Product
curl https://api.stripe.com/v1/products \
  -u sk_live_YOUR_SECRET_KEY: \
  -d name="Axient MCP++ Enterprise" \
  -d description="For large organizations with custom requirements" \
  -d metadata[plan_id]="enterprise"

# Create Price
curl https://api.stripe.com/v1/prices \
  -u sk_live_YOUR_SECRET_KEY: \
  -d product="prod_PRODUCT_ID" \
  -d unit_amount=29900 \
  -d currency=usd \
  -d recurring[interval]=month \
  -d metadata[plan_id]="enterprise"
```

#### **Update Environment Variables**
```bash
# Add price IDs to .env.production
STRIPE_PRICE_STARTER=price_STARTER_PRICE_ID
STRIPE_PRICE_PROFESSIONAL=price_PROFESSIONAL_PRICE_ID
STRIPE_PRICE_ENTERPRISE=price_ENTERPRISE_PRICE_ID
```

### **3. Webhook Configuration**

#### **Create Webhook Endpoint**
1. Go to **Developers > Webhooks**
2. Click **Add endpoint**
3. Set endpoint URL: `https://api.axientmcp.com/api/billing/webhook`
4. Select events to listen for:
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
   - `customer.created`
   - `customer.updated`
   - `payment_method.attached`

#### **Configure Webhook Secret**
1. Copy the webhook signing secret (starts with `whsec_`)
2. Update `.env.production`:
   ```bash
   STRIPE_WEBHOOK_SECRET=whsec_YOUR_WEBHOOK_SECRET
   ```

### **4. Tax Configuration**

#### **Enable Stripe Tax (Recommended)**
1. Go to **Products > Tax**
2. Enable **Stripe Tax**
3. Configure tax settings:
   - Business address
   - Tax registration numbers
   - Product tax codes

#### **Tax Code Configuration**
```php
// In your Laravel application
'tax_code' => 'txcd_10103001', // Software as a Service
```

### **5. Security Configuration**

#### **Restricted API Keys (Recommended)**
1. Go to **Developers > API keys**
2. Create **Restricted keys** with minimal permissions:
   - Read/Write: Customers, Subscriptions, Payment Methods
   - Read only: Products, Prices
   - Write only: Webhook endpoints

#### **IP Allowlisting**
1. Configure IP allowlist for webhook endpoints
2. Add your production server IPs

### **6. Testing Production Setup**

#### **Test Webhook Endpoint**
```bash
# Test webhook endpoint
curl -X POST https://api.axientmcp.com/api/billing/webhook \
  -H "Content-Type: application/json" \
  -H "Stripe-Signature: WEBHOOK_SIGNATURE" \
  -d '{"type": "customer.subscription.created"}'
```

#### **Test Subscription Creation**
```bash
# Create test customer and subscription
php artisan tinker

# In tinker:
$billingService = app(\App\Services\BillingService::class);
$result = $billingService->createSubscription(
    'tenant-id',
    'starter',
    'pm_card_visa', // Test payment method
    7 // 7-day trial
);
```

### **7. Monitoring and Alerts**

#### **Stripe Dashboard Monitoring**
1. Set up **Dashboard alerts** for:
   - Failed payments
   - High chargeback rates
   - Unusual activity

#### **Custom Monitoring**
```php
// Add to your monitoring service
class StripeMonitoring
{
    public function checkFailedPayments()
    {
        $failedPayments = Subscription::where('status', 'past_due')
            ->where('updated_at', '>=', now()->subHours(1))
            ->count();
            
        if ($failedPayments > 10) {
            // Alert team
        }
    }
}
```

### **8. Compliance and Legal**

#### **Required Legal Pages**
- Terms of Service
- Privacy Policy
- Refund Policy
- Billing Terms

#### **PCI Compliance**
- Use Stripe Elements for card collection
- Never store card data
- Implement proper security headers

### **9. Production Checklist**

#### **Pre-Launch Checklist**
- [ ] Live API keys configured
- [ ] Products and prices created
- [ ] Webhooks configured and tested
- [ ] Tax settings configured
- [ ] Legal pages published
- [ ] Monitoring alerts set up
- [ ] Backup payment processor configured (optional)

#### **Post-Launch Monitoring**
- [ ] Monitor webhook delivery
- [ ] Track subscription metrics
- [ ] Monitor failed payments
- [ ] Review chargeback rates
- [ ] Analyze revenue trends

### **10. Troubleshooting**

#### **Common Issues**
1. **Webhook failures**: Check endpoint URL and SSL certificate
2. **Payment failures**: Verify payment method and customer details
3. **Tax calculation errors**: Check business address and tax codes

#### **Debug Commands**
```bash
# Check webhook logs
php artisan log:stripe-webhooks

# Verify subscription status
php artisan stripe:sync-subscriptions

# Test billing service
php artisan billing:test-connection
```

### **11. Support and Resources**

#### **Stripe Resources**
- [Stripe Documentation](https://stripe.com/docs)
- [Stripe Support](https://support.stripe.com)
- [Stripe Status Page](https://status.stripe.com)

#### **Emergency Contacts**
- Stripe Support: <EMAIL>
- Technical Issues: Emergency escalation process
- Business Critical: Priority support channel

---

## 🔒 **SECURITY NOTES**

1. **Never commit API keys to version control**
2. **Use environment variables for all secrets**
3. **Implement proper webhook signature verification**
4. **Monitor for suspicious activity**
5. **Regularly rotate API keys**
6. **Use HTTPS for all endpoints**
7. **Implement proper error handling**
8. **Log all payment-related activities**

---

## 📊 **MONITORING DASHBOARD**

Create a Stripe monitoring dashboard to track:
- Monthly Recurring Revenue (MRR)
- Churn rate
- Failed payment rate
- Customer lifetime value
- Subscription growth rate
- Payment method distribution
