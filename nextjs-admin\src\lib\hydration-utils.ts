/**
 * Hydration-safe utilities to prevent SSR/client mismatches
 */

/**
 * Safe date formatting that prevents hydration mismatches
 */
export function formatDateSafe(
  date: string | Date | null | undefined,
  options?: Intl.DateTimeFormatOptions,
  fallback: string = '--'
): string {
  if (!date) return fallback
  
  try {
    // Check if we're on the client side
    if (typeof window === 'undefined') {
      return fallback
    }
    
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      ...options
    }).format(new Date(date))
  } catch (error) {
    return fallback
  }
}

/**
 * Safe time formatting that prevents hydration mismatches
 */
export function formatTimeSafe(
  date: string | Date | null | undefined,
  options?: Intl.DateTimeFormatOptions,
  fallback: string = '--:--:--'
): string {
  if (!date) return fallback
  
  try {
    // Check if we're on the client side
    if (typeof window === 'undefined') {
      return fallback
    }
    
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      ...options
    }).format(new Date(date))
  } catch (error) {
    return fallback
  }
}

/**
 * Safe relative time formatting
 */
export function formatRelativeTimeSafe(
  date: string | Date | null | undefined,
  fallback: string = 'Unknown'
): string {
  if (!date) return fallback
  
  try {
    // Check if we're on the client side
    if (typeof window === 'undefined') {
      return fallback
    }
    
    const now = new Date()
    const targetDate = new Date(date)
    const diffMs = now.getTime() - targetDate.getTime()
    
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffMinutes < 1) return 'Just now'
    if (diffMinutes < 60) return `${diffMinutes}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    if (diffDays < 7) return `${diffDays}d ago`
    
    return targetDate.toLocaleDateString()
  } catch (error) {
    return fallback
  }
}

/**
 * Safe number formatting
 */
export function formatNumberSafe(
  value: number | null | undefined,
  options?: Intl.NumberFormatOptions,
  fallback: string = '0'
): string {
  if (value === null || value === undefined || isNaN(value)) return fallback
  
  try {
    // Check if we're on the client side
    if (typeof window === 'undefined') {
      return fallback
    }
    
    return new Intl.NumberFormat('en-US', options).format(value)
  } catch (error) {
    return fallback
  }
}

/**
 * Safe currency formatting
 */
export function formatCurrencySafe(
  amount: number | null | undefined,
  currency: string = 'USD',
  fallback: string = '$0.00'
): string {
  if (amount === null || amount === undefined || isNaN(amount)) return fallback
  
  try {
    // Check if we're on the client side
    if (typeof window === 'undefined') {
      return fallback
    }
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount)
  } catch (error) {
    return fallback
  }
}

/**
 * Safe percentage formatting
 */
export function formatPercentageSafe(
  value: number | null | undefined,
  decimals: number = 1,
  fallback: string = '0%'
): string {
  if (value === null || value === undefined || isNaN(value)) return fallback
  
  try {
    // Check if we're on the client side
    if (typeof window === 'undefined') {
      return fallback
    }
    
    return new Intl.NumberFormat('en-US', {
      style: 'percent',
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(value / 100)
  } catch (error) {
    return fallback
  }
}

/**
 * Check if we're running on the client side
 */
export function isClient(): boolean {
  return typeof window !== 'undefined'
}

/**
 * Safe localStorage access
 */
export function getLocalStorageSafe(key: string, fallback: string = ''): string {
  if (!isClient()) return fallback
  
  try {
    return localStorage.getItem(key) || fallback
  } catch (error) {
    return fallback
  }
}

/**
 * Safe localStorage setter
 */
export function setLocalStorageSafe(key: string, value: string): boolean {
  if (!isClient()) return false
  
  try {
    localStorage.setItem(key, value)
    return true
  } catch (error) {
    return false
  }
}

/**
 * Safe sessionStorage access
 */
export function getSessionStorageSafe(key: string, fallback: string = ''): string {
  if (!isClient()) return fallback
  
  try {
    return sessionStorage.getItem(key) || fallback
  } catch (error) {
    return fallback
  }
}

/**
 * Safe sessionStorage setter
 */
export function setSessionStorageSafe(key: string, value: string): boolean {
  if (!isClient()) return false
  
  try {
    sessionStorage.setItem(key, value)
    return true
  } catch (error) {
    return false
  }
}

/**
 * Safe window dimension access
 */
export function getWindowDimensionsSafe(): { width: number; height: number } {
  if (!isClient()) {
    return { width: 1200, height: 800 } // Default fallback
  }
  
  try {
    return {
      width: window.innerWidth,
      height: window.innerHeight
    }
  } catch (error) {
    return { width: 1200, height: 800 }
  }
}

/**
 * Safe user agent access
 */
export function getUserAgentSafe(): string {
  if (!isClient()) return ''
  
  try {
    return navigator.userAgent
  } catch (error) {
    return ''
  }
}

/**
 * Safe timezone detection
 */
export function getTimezoneSafe(): string {
  if (!isClient()) return 'UTC'
  
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone
  } catch (error) {
    return 'UTC'
  }
}

/**
 * Safe locale detection
 */
export function getLocaleSafe(): string {
  if (!isClient()) return 'en-US'
  
  try {
    return navigator.language || 'en-US'
  } catch (error) {
    return 'en-US'
  }
}
