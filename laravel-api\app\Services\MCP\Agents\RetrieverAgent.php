<?php

declare(strict_types=1);

namespace App\Services\MCP\Agents;

use App\Services\MCP\AbstractMCPAgent;
use App\Models\KnowledgeBase;
use App\Models\Document;
use App\Models\DocumentChunk;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;

class RetrieverAgent extends AbstractMCPAgent
{
    protected string $embeddingProvider = 'openai';
    protected string $embeddingModel = 'text-embedding-ada-002';
    protected int $maxResults = 5;
    protected float $similarityThreshold = 0.7;

    public function getName(): string
    {
        return 'retriever';
    }

    public function getType(): string
    {
        return 'primary';
    }

    public function getExecutionOrder(): int
    {
        return 2; // After Intent Agent
    }

    protected function process(array $input, string $sessionId): array
    {
        // Skip if knowledge base retrieval is not required
        if (!($input['requires_knowledge_base'] ?? false)) {
            Log::info('Retriever Agent skipped - no knowledge base required', [
                'session_id' => $sessionId
            ]);
            
            return array_merge($input, [
                'retrieved_documents' => [],
                'retrieval_metadata' => [
                    'skipped' => true,
                    'reason' => 'not_required'
                ]
            ]);
        }

        $message = $input['message'] ?? '';
        $intent = $input['intent'] ?? 'general';

        Log::info('Retriever Agent processing query', [
            'session_id' => $sessionId,
            'intent' => $intent,
            'message_length' => strlen($message)
        ]);

        try {
            // Generate embedding for the query
            $queryEmbedding = $this->generateEmbedding($message);
            
            // Retrieve relevant documents
            $documents = $this->retrieveDocuments($queryEmbedding, $intent);
            
            // Rank and filter results
            $rankedDocuments = $this->rankDocuments($documents, $message, $intent);
            
            $result = array_merge($input, [
                'retrieved_documents' => $rankedDocuments,
                'retrieval_metadata' => [
                    'total_found' => count($documents),
                    'returned' => count($rankedDocuments),
                    'embedding_model' => $this->embeddingModel,
                    'similarity_threshold' => $this->similarityThreshold,
                    'processing_time' => microtime(true) - ($_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true))
                ]
            ]);

            Log::info('Retriever Agent completed retrieval', [
                'session_id' => $sessionId,
                'documents_found' => count($documents),
                'documents_returned' => count($rankedDocuments)
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('Retriever Agent failed', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);

            return array_merge($input, [
                'retrieved_documents' => [],
                'retrieval_metadata' => [
                    'error' => $e->getMessage(),
                    'fallback_used' => true
                ]
            ]);
        }
    }

    protected function generateEmbedding(string $text): array
    {
        $config = $this->getConfig();
        $apiKey = $config['openai_api_key'] ?? env('OPENAI_API_KEY');

        if (!$apiKey) {
            throw new \Exception('OpenAI API key not configured');
        }

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $apiKey,
            'Content-Type' => 'application/json',
        ])->post('https://api.openai.com/v1/embeddings', [
            'model' => $this->embeddingModel,
            'input' => $text,
        ]);

        if (!$response->successful()) {
            throw new \Exception('Failed to generate embedding: ' . $response->body());
        }

        $data = $response->json();
        return $data['data'][0]['embedding'] ?? [];
    }

    protected function retrieveDocuments(array $queryEmbedding, string $intent): array
    {
        if (empty($queryEmbedding)) {
            return [];
        }

        // Convert embedding to PostgreSQL vector format
        $embeddingString = '[' . implode(',', $queryEmbedding) . ']';

        // Get active knowledge bases
        $knowledgeBases = KnowledgeBase::active()->pluck('id');

        if ($knowledgeBases->isEmpty()) {
            return [];
        }

        // Perform vector similarity search
        $query = DocumentChunk::select([
            'document_chunks.*',
            'documents.title',
            'documents.source_type',
            'documents.source_path',
            'knowledge_bases.name as kb_name',
            DB::raw("(embedding <=> '{$embeddingString}') as distance")
        ])
        ->join('documents', 'document_chunks.document_id', '=', 'documents.id')
        ->join('knowledge_bases', 'documents.knowledge_base_id', '=', 'knowledge_bases.id')
        ->whereIn('documents.knowledge_base_id', $knowledgeBases)
        ->whereNotNull('document_chunks.embedding')
        ->orderBy('distance')
        ->limit($this->maxResults * 2); // Get more for filtering

        $results = $query->get();

        // Filter by similarity threshold
        return $results->filter(function ($chunk) {
            $similarity = 1 - $chunk->distance; // Convert distance to similarity
            return $similarity >= $this->similarityThreshold;
        })->take($this->maxResults)->toArray();
    }

    protected function rankDocuments(array $documents, string $query, string $intent): array
    {
        if (empty($documents)) {
            return [];
        }

        $queryWords = str_word_count(strtolower($query), 1);
        
        foreach ($documents as &$doc) {
            $score = 1 - $doc['distance']; // Base similarity score
            
            // Boost score based on content relevance
            $contentWords = str_word_count(strtolower($doc['content']), 1);
            $commonWords = array_intersect($queryWords, $contentWords);
            $wordBoost = count($commonWords) / max(count($queryWords), 1) * 0.2;
            
            // Boost score based on document type relevance to intent
            $typeBoost = $this->getTypeBoost($doc['source_type'], $intent);
            
            // Boost score based on recency (if metadata available)
            $recencyBoost = $this->getRecencyBoost($doc);
            
            $doc['relevance_score'] = min($score + $wordBoost + $typeBoost + $recencyBoost, 1.0);
            $doc['similarity'] = $score;
        }

        // Sort by relevance score
        usort($documents, function ($a, $b) {
            return $b['relevance_score'] <=> $a['relevance_score'];
        });

        return array_slice($documents, 0, $this->maxResults);
    }

    protected function getTypeBoost(string $sourceType, string $intent): float
    {
        $boosts = [
            'question' => [
                'manual' => 0.1,
                'file' => 0.05,
                'url' => 0.0
            ],
            'search' => [
                'file' => 0.1,
                'url' => 0.05,
                'manual' => 0.0
            ],
            'request' => [
                'manual' => 0.1,
                'file' => 0.05,
                'url' => 0.0
            ]
        ];

        return $boosts[$intent][$sourceType] ?? 0.0;
    }

    protected function getRecencyBoost(array $document): float
    {
        // Simple recency boost - can be enhanced with actual timestamps
        return 0.0;
    }

    public function validateInput(array $input): bool
    {
        return isset($input['message']) && 
               isset($input['intent']) && 
               is_string($input['message']) && 
               !empty(trim($input['message']));
    }
}
