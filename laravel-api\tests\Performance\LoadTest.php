<?php

namespace Tests\Performance;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Contracts\MCPOrchestratorInterface;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class LoadTest extends TestCase
{
    protected MCPOrchestratorInterface $orchestrator;

    protected function setUp(): void
    {
        parent::setUp();
        $this->orchestrator = app(MCPOrchestratorInterface::class);
    }

    /**
     * Test concurrent request handling
     */
    public function test_concurrent_request_handling()
    {
        $concurrentRequests = 10;
        $testMessages = [
            'What is artificial intelligence?',
            'How does machine learning work?',
            'Explain neural networks',
            'What is the weather today?',
            'Help me create a workflow',
            'Search for information about PHP',
            'Send an email to my team',
            'Schedule a meeting for tomorrow',
            'What are the latest AI trends?',
            'How can I optimize my code?'
        ];

        $startTime = microtime(true);
        $results = [];
        $errors = [];

        // Simulate concurrent requests
        for ($i = 0; $i < $concurrentRequests; $i++) {
            try {
                $sessionId = Str::uuid()->toString();
                $input = [
                    'message' => $testMessages[$i % count($testMessages)],
                    'context' => [],
                    'user_id' => $i + 1,
                    'timestamp' => now()->toISOString(),
                ];

                $requestStart = microtime(true);
                $result = $this->orchestrator->processRequest($input, $sessionId);
                $requestEnd = microtime(true);

                $results[] = [
                    'request_id' => $i,
                    'session_id' => $sessionId,
                    'execution_time' => ($requestEnd - $requestStart) * 1000,
                    'success' => true,
                    'agents_executed' => $this->getExecutedAgents($result)
                ];

            } catch (\Exception $e) {
                $errors[] = [
                    'request_id' => $i,
                    'error' => $e->getMessage(),
                    'execution_time' => (microtime(true) - $requestStart) * 1000
                ];
            }
        }

        $totalTime = (microtime(true) - $startTime) * 1000;

        // Analyze results
        $successfulRequests = count($results);
        $failedRequests = count($errors);
        $executionTimes = array_column($results, 'execution_time');
        
        $stats = [
            'total_requests' => $concurrentRequests,
            'successful_requests' => $successfulRequests,
            'failed_requests' => $failedRequests,
            'success_rate' => ($successfulRequests / $concurrentRequests) * 100,
            'total_execution_time' => $totalTime,
            'average_response_time' => !empty($executionTimes) ? array_sum($executionTimes) / count($executionTimes) : 0,
            'min_response_time' => !empty($executionTimes) ? min($executionTimes) : 0,
            'max_response_time' => !empty($executionTimes) ? max($executionTimes) : 0,
            'requests_per_second' => $concurrentRequests / ($totalTime / 1000)
        ];

        // Log performance results
        Log::info('Load test completed', $stats);

        // Assertions
        $this->assertGreaterThanOrEqual(80, $stats['success_rate'], 'Success rate should be at least 80%');
        $this->assertLessThan(5000, $stats['average_response_time'], 'Average response time should be under 5 seconds');
        $this->assertGreaterThan(1, $stats['requests_per_second'], 'Should handle at least 1 request per second');

        return $stats;
    }

    /**
     * Test memory usage under load
     */
    public function test_memory_usage_under_load()
    {
        $initialMemory = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);
        
        $requestCount = 50;
        $memorySnapshots = [];

        for ($i = 0; $i < $requestCount; $i++) {
            $sessionId = Str::uuid()->toString();
            $input = [
                'message' => "Memory test request number {$i}",
                'context' => [],
                'user_id' => 1,
                'timestamp' => now()->toISOString(),
            ];

            $this->orchestrator->processRequest($input, $sessionId);
            
            $memorySnapshots[] = [
                'request' => $i,
                'memory_usage' => memory_get_usage(true),
                'peak_memory' => memory_get_peak_usage(true)
            ];

            // Force garbage collection every 10 requests
            if ($i % 10 === 0) {
                gc_collect_cycles();
            }
        }

        $finalMemory = memory_get_usage(true);
        $finalPeak = memory_get_peak_usage(true);
        
        $memoryIncrease = $finalMemory - $initialMemory;
        $memoryPerRequest = $memoryIncrease / $requestCount;

        $stats = [
            'initial_memory' => $this->formatBytes($initialMemory),
            'final_memory' => $this->formatBytes($finalMemory),
            'memory_increase' => $this->formatBytes($memoryIncrease),
            'memory_per_request' => $this->formatBytes($memoryPerRequest),
            'peak_memory' => $this->formatBytes($finalPeak),
            'request_count' => $requestCount
        ];

        Log::info('Memory usage test completed', $stats);

        // Memory should not increase excessively
        $this->assertLessThan(100 * 1024 * 1024, $memoryIncrease, 'Memory increase should be less than 100MB');
        $this->assertLessThan(2 * 1024 * 1024, $memoryPerRequest, 'Memory per request should be less than 2MB');

        return $stats;
    }

    /**
     * Test database performance under load
     */
    public function test_database_performance_under_load()
    {
        $requestCount = 30;
        $queryTimes = [];
        
        for ($i = 0; $i < $requestCount; $i++) {
            $queryStart = microtime(true);
            
            // Simulate database-heavy operations
            $sessionId = Str::uuid()->toString();
            $input = [
                'message' => "Database test request {$i}",
                'context' => [],
                'user_id' => 1,
                'timestamp' => now()->toISOString(),
            ];

            $this->orchestrator->processRequest($input, $sessionId);
            
            $queryEnd = microtime(true);
            $queryTimes[] = ($queryEnd - $queryStart) * 1000;
        }

        $stats = [
            'total_requests' => $requestCount,
            'average_query_time' => array_sum($queryTimes) / count($queryTimes),
            'min_query_time' => min($queryTimes),
            'max_query_time' => max($queryTimes),
            'total_query_time' => array_sum($queryTimes)
        ];

        Log::info('Database performance test completed', $stats);

        // Database performance assertions
        $this->assertLessThan(2000, $stats['average_query_time'], 'Average query time should be under 2 seconds');
        $this->assertLessThan(5000, $stats['max_query_time'], 'Max query time should be under 5 seconds');

        return $stats;
    }

    /**
     * Test error handling under stress
     */
    public function test_error_handling_under_stress()
    {
        $stressTestCases = [
            ['message' => '', 'expected_error' => true], // Empty message
            ['message' => str_repeat('a', 50000), 'expected_error' => false], // Very long message
            ['message' => 'Normal message', 'expected_error' => false], // Normal case
            ['message' => '🚀🎉💻🔥⚡', 'expected_error' => false], // Unicode/emoji
            ['message' => '<script>alert("xss")</script>', 'expected_error' => false], // Potential XSS
        ];

        $results = [];
        $errorCount = 0;

        foreach ($stressTestCases as $index => $testCase) {
            try {
                $sessionId = Str::uuid()->toString();
                $input = [
                    'message' => $testCase['message'],
                    'context' => [],
                    'user_id' => 1,
                    'timestamp' => now()->toISOString(),
                ];

                $startTime = microtime(true);
                $result = $this->orchestrator->processRequest($input, $sessionId);
                $endTime = microtime(true);

                $results[] = [
                    'test_case' => $index,
                    'success' => true,
                    'execution_time' => ($endTime - $startTime) * 1000,
                    'expected_error' => $testCase['expected_error']
                ];

            } catch (\Exception $e) {
                $errorCount++;
                $results[] = [
                    'test_case' => $index,
                    'success' => false,
                    'error' => $e->getMessage(),
                    'expected_error' => $testCase['expected_error']
                ];
            }
        }

        $stats = [
            'total_test_cases' => count($stressTestCases),
            'successful_cases' => count($results) - $errorCount,
            'error_cases' => $errorCount,
            'error_rate' => ($errorCount / count($stressTestCases)) * 100
        ];

        Log::info('Error handling stress test completed', $stats);

        // Should handle errors gracefully
        $this->assertLessThan(50, $stats['error_rate'], 'Error rate should be less than 50%');

        return $stats;
    }

    private function getExecutedAgents(array $result): array
    {
        $executed = [];
        
        if (isset($result['intent'])) $executed[] = 'intent';
        if (isset($result['retrieved_documents'])) $executed[] = 'retriever';
        if (isset($result['llm_response'])) $executed[] = 'llm';
        if (isset($result['conversation_id'])) $executed[] = 'memory';
        if (isset($result['tool_executions'])) $executed[] = 'tool';
        if (isset($result['workflow_execution'])) $executed[] = 'workflow';
        if (isset($result['formatted_response'])) $executed[] = 'formatter';
        if (isset($result['guardrail_report'])) $executed[] = 'guardrail';
        
        return $executed;
    }

    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
