<?php

declare(strict_types=1);

namespace App\Services\MCP;

use App\Contracts\MCPAgentInterface;
use App\Models\MCPAgent;
use App\Models\AgentExecution;
use Illuminate\Support\Facades\Log;

abstract class AbstractMCPAgent implements MCPAgentInterface
{
    protected array $config = [];
    protected bool $isActive = true;
    protected int $executionOrder = 0;
    protected MCPAgent $model;

    public function __construct(array $config = [])
    {
        $this->config = $config;
        // Skip database loading during boot to avoid connection issues
        // $this->loadFromDatabase();
    }

    /**
     * Load agent configuration from database
     */
    protected function loadFromDatabase(): void
    {
        $this->model = MCPAgent::where('name', $this->getName())->first();

        if ($this->model) {
            $this->config = array_merge($this->config, $this->model->config);
            $this->isActive = $this->model->is_active;
            $this->executionOrder = $this->model->execution_order;
        }
    }

    /**
     * Execute the agent with logging and error handling
     */
    public function execute(array $input, string $sessionId): array
    {
        $startTime = microtime(true);

        $execution = AgentExecution::create([
            'agent_id' => $this->model?->id,
            'session_id' => $sessionId,
            'input_data' => $input,
            'status' => 'running'
        ]);

        try {
            $this->validateInput($input);
            $output = $this->process($input, $sessionId);

            $execution->update([
                'output_data' => $output,
                'status' => 'completed',
                'execution_time_ms' => (int)((microtime(true) - $startTime) * 1000)
            ]);

            Log::info("Agent {$this->getName()} executed successfully", [
                'session_id' => $sessionId,
                'execution_time_ms' => $execution->execution_time_ms
            ]);

            return $output;

        } catch (\Exception $e) {
            $execution->update([
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'execution_time_ms' => (int)((microtime(true) - $startTime) * 1000)
            ]);

            Log::error("Agent {$this->getName()} execution failed", [
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Abstract method for agent-specific processing
     */
    abstract protected function process(array $input, string $sessionId): array;

    /**
     * Default input validation
     */
    public function validateInput(array $input): bool
    {
        return !empty($input);
    }

    public function getConfig(): array
    {
        return $this->config;
    }

    public function setConfig(array $config): void
    {
        $this->config = $config;

        if ($this->model) {
            $this->model->update(['config' => $config]);
        }
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }

    public function getExecutionOrder(): int
    {
        return $this->executionOrder;
    }
}
