<?php

declare(strict_types=1);

namespace App\Services\MCP;

use App\Contracts\MCPOrchestratorInterface;
use App\Services\AnalyticsService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class MCPPipeline
{
    protected MCPOrchestratorInterface $orchestrator;
    protected AnalyticsService $analytics;

    public function __construct(
        MCPOrchestratorInterface $orchestrator,
        AnalyticsService $analytics
    ) {
        $this->orchestrator = $orchestrator;
        $this->analytics = $analytics;
    }

    /**
     * Process a user request through the complete MCP pipeline
     */
    public function process(array $input): array
    {
        $startTime = microtime(true);
        $sessionId = $input['session_id'] ?? $this->generateSessionId();
        
        try {
            Log::info('MCP Pipeline processing started', [
                'session_id' => $sessionId,
                'input_message' => $input['message'] ?? 'No message',
                'input_keys' => array_keys($input)
            ]);

            // Track the request
            $this->analytics->trackEvent(
                'mcp_request',
                'pipeline_started',
                'mcp_pipeline',
                $sessionId,
                [
                    'message_length' => strlen($input['message'] ?? ''),
                    'has_context' => isset($input['context']),
                    'has_files' => isset($input['files'])
                ]
            );

            // Prepare input for processing
            $processedInput = $this->prepareInput($input, $sessionId);

            // Execute the MCP agent pipeline
            $result = $this->orchestrator->processRequest($processedInput, $sessionId);

            // Post-process the result
            $finalResult = $this->postProcessResult($result, $sessionId);

            $endTime = microtime(true);
            $processingTime = ($endTime - $startTime) * 1000; // Convert to milliseconds

            // Track successful completion
            $this->analytics->trackEvent(
                'mcp_request',
                'pipeline_completed',
                'mcp_pipeline',
                $sessionId,
                [
                    'processing_time_ms' => $processingTime,
                    'agents_executed' => $result['pipeline_metadata']['agents_executed'] ?? 0,
                    'success' => true
                ],
                [],
                (int) $processingTime
            );

            Log::info('MCP Pipeline processing completed', [
                'session_id' => $sessionId,
                'processing_time_ms' => $processingTime,
                'agents_executed' => $result['pipeline_metadata']['agents_executed'] ?? 0
            ]);

            return array_merge($finalResult, [
                'success' => true,
                'session_id' => $sessionId,
                'processing_time_ms' => $processingTime,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            $endTime = microtime(true);
            $processingTime = ($endTime - $startTime) * 1000;

            // Track error
            $this->analytics->trackEvent(
                'mcp_request',
                'pipeline_failed',
                'mcp_pipeline',
                $sessionId,
                [
                    'error_message' => $e->getMessage(),
                    'error_type' => get_class($e),
                    'processing_time_ms' => $processingTime,
                    'success' => false
                ],
                [],
                (int) $processingTime
            );

            Log::error('MCP Pipeline processing failed', [
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'session_id' => $sessionId,
                'processing_time_ms' => $processingTime,
                'timestamp' => now()->toISOString(),
                'llm_response' => 'I apologize, but I encountered an error while processing your request. Please try again or contact support if the issue persists.'
            ];
        }
    }

    /**
     * Prepare input for MCP processing
     */
    protected function prepareInput(array $input, string $sessionId): array
    {
        $prepared = [
            'session_id' => $sessionId,
            'message' => $input['message'] ?? '',
            'context' => $input['context'] ?? [],
            'files' => $input['files'] ?? [],
            'user_id' => $input['user_id'] ?? null,
            'tenant_id' => $input['tenant_id'] ?? null,
            'timestamp' => now()->toISOString(),
            'pipeline_metadata' => [
                'started_at' => now()->toISOString(),
                'agents_executed' => 0,
                'execution_log' => []
            ]
        ];

        // Add conversation history if available
        if (isset($input['conversation_history'])) {
            $prepared['conversation_history'] = $input['conversation_history'];
        }

        // Add user preferences if available
        if (isset($input['user_preferences'])) {
            $prepared['user_preferences'] = $input['user_preferences'];
        }

        return $prepared;
    }

    /**
     * Post-process the pipeline result
     */
    protected function postProcessResult(array $result, string $sessionId): array
    {
        // Ensure we have a proper response
        if (!isset($result['llm_response']) || empty($result['llm_response'])) {
            $result['llm_response'] = 'I apologize, but I was unable to generate a proper response. Please try rephrasing your question.';
        }

        // Clean up sensitive information
        $result = $this->sanitizeResult($result);

        // Add response metadata
        $result['response_metadata'] = [
            'session_id' => $sessionId,
            'generated_at' => now()->toISOString(),
            'pipeline_version' => '1.0',
            'agents_used' => $this->extractAgentsUsed($result),
            'confidence_score' => $result['confidence'] ?? 0.8,
            'response_type' => $this->determineResponseType($result)
        ];

        return $result;
    }

    /**
     * Sanitize result to remove sensitive information
     */
    protected function sanitizeResult(array $result): array
    {
        // Remove internal processing details
        unset($result['internal_state']);
        unset($result['debug_info']);
        
        // Remove sensitive context
        if (isset($result['context']['api_keys'])) {
            unset($result['context']['api_keys']);
        }

        if (isset($result['context']['credentials'])) {
            unset($result['context']['credentials']);
        }

        return $result;
    }

    /**
     * Extract list of agents that were used
     */
    protected function extractAgentsUsed(array $result): array
    {
        $agents = [];

        if (isset($result['intent'])) {
            $agents[] = 'intent';
        }

        if (isset($result['conversation_history'])) {
            $agents[] = 'memory';
        }

        if (isset($result['guardrail_checks'])) {
            $agents[] = 'guardrail';
        }

        if (isset($result['retrieved_documents'])) {
            $agents[] = 'retriever';
        }

        if (isset($result['llm_response'])) {
            $agents[] = 'llm';
        }

        if (isset($result['tool_results'])) {
            $agents[] = 'tool';
        }

        if (isset($result['workflow_execution'])) {
            $agents[] = 'workflow';
        }

        if (isset($result['formatted_response'])) {
            $agents[] = 'formatter';
        }

        return $agents;
    }

    /**
     * Determine the type of response generated
     */
    protected function determineResponseType(array $result): string
    {
        if (isset($result['workflow_execution'])) {
            return 'workflow_execution';
        }

        if (isset($result['tool_results']) && !empty($result['tool_results'])) {
            return 'tool_assisted';
        }

        if (isset($result['retrieved_documents']) && !empty($result['retrieved_documents'])) {
            return 'knowledge_based';
        }

        if (isset($result['intent']) && $result['intent'] === 'search') {
            return 'search_result';
        }

        return 'conversational';
    }

    /**
     * Generate a unique session ID
     */
    protected function generateSessionId(): string
    {
        return 'mcp_' . Str::uuid()->toString();
    }

    /**
     * Get pipeline statistics
     */
    public function getStatistics(): array
    {
        return [
            'registered_agents' => count($this->orchestrator->getAgents()),
            'agent_names' => array_keys($this->orchestrator->getAgents()),
            'pipeline_version' => '1.0',
            'supported_features' => [
                'intent_classification',
                'conversation_memory',
                'content_filtering',
                'knowledge_retrieval',
                'llm_generation',
                'tool_execution',
                'workflow_automation',
                'response_formatting'
            ]
        ];
    }

    /**
     * Test the pipeline with a simple message
     */
    public function test(string $message = 'Hello, how are you?'): array
    {
        return $this->process([
            'message' => $message,
            'context' => ['test_mode' => true]
        ]);
    }

    /**
     * Get execution history for a session
     */
    public function getExecutionHistory(string $sessionId): array
    {
        return $this->orchestrator->getExecutionHistory($sessionId);
    }

    /**
     * Clear session data
     */
    public function clearSession(string $sessionId): void
    {
        // Clear memory agent session data
        $memoryAgent = $this->orchestrator->getAgent('memory');
        if ($memoryAgent && method_exists($memoryAgent, 'clearSession')) {
            $memoryAgent->clearSession($sessionId);
        }

        Log::info('MCP Pipeline session cleared', ['session_id' => $sessionId]);
    }
}
