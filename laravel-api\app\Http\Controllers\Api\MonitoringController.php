<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\Performance\PerformanceMonitor;
use App\Models\AgentExecution;
use App\Models\Conversation;
use App\Models\Message;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class MonitoringController extends Controller
{
    public function __construct(
        private PerformanceMonitor $performanceMonitor
    ) {}

    /**
     * Get comprehensive system metrics
     */
    public function getSystemMetrics(): JsonResponse
    {
        try {
            $metrics = [
                'performance' => $this->performanceMonitor->getStatistics(),
                'health' => $this->performanceMonitor->getHealthScore(),
                'system' => $this->getSystemMetrics(),
                'agents' => $this->getAgentMetrics(),
                'conversations' => $this->getConversationMetrics(),
                'database' => $this->getDatabaseMetrics(),
                'timestamp' => now()->toISOString()
            ];

            return response()->json([
                'success' => true,
                'data' => $metrics
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get real-time performance statistics
     */
    public function getPerformanceStats(Request $request): JsonResponse
    {
        $period = $request->get('period', '1h');
        
        try {
            $stats = $this->performanceMonitor->getStatistics($period);
            
            return response()->json([
                'success' => true,
                'data' => $stats,
                'period' => $period
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get system health score
     */
    public function getHealthScore(): JsonResponse
    {
        try {
            $health = $this->performanceMonitor->getHealthScore();
            
            return response()->json([
                'success' => true,
                'data' => $health
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get agent performance metrics
     */
    public function getAgentMetrics(): JsonResponse
    {
        try {
            $metrics = $this->getAgentMetrics();
            
            return response()->json([
                'success' => true,
                'data' => $metrics
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Optimize system performance
     */
    public function optimizePerformance(): JsonResponse
    {
        try {
            $optimizations = $this->performanceMonitor->optimizePerformance();
            
            return response()->json([
                'success' => true,
                'optimizations' => $optimizations,
                'message' => 'Performance optimization completed'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get error logs and analysis
     */
    public function getErrorAnalysis(): JsonResponse
    {
        try {
            $errors = $this->getRecentErrors();
            $analysis = $this->analyzeErrors($errors);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'recent_errors' => $errors,
                    'analysis' => $analysis
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    protected function getSystemMetrics(): array
    {
        return [
            'uptime' => $this->getSystemUptime(),
            'memory' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true),
                'limit' => $this->parseMemoryLimit(ini_get('memory_limit'))
            ],
            'cpu' => [
                'load_average' => sys_getloadavg(),
                'process_count' => $this->getProcessCount()
            ],
            'disk' => [
                'free_space' => disk_free_space('.'),
                'total_space' => disk_total_space('.')
            ]
        ];
    }

    protected function getAgentMetrics(): array
    {
        // Get recent agent executions
        $recentExecutions = AgentExecution::where('created_at', '>=', now()->subHour())
            ->get();

        $agentStats = [];
        
        foreach ($recentExecutions->groupBy('agent_id') as $agentId => $executions) {
            $successful = $executions->where('status', 'completed')->count();
            $failed = $executions->where('status', 'failed')->count();
            $total = $executions->count();
            
            $executionTimes = $executions->where('execution_time_ms', '>', 0)
                ->pluck('execution_time_ms');
            
            $agentStats[$agentId] = [
                'total_executions' => $total,
                'successful_executions' => $successful,
                'failed_executions' => $failed,
                'success_rate' => $total > 0 ? ($successful / $total) * 100 : 0,
                'average_execution_time' => $executionTimes->avg() ?? 0,
                'min_execution_time' => $executionTimes->min() ?? 0,
                'max_execution_time' => $executionTimes->max() ?? 0
            ];
        }

        return $agentStats;
    }

    protected function getConversationMetrics(): array
    {
        $today = now()->startOfDay();
        $yesterday = now()->subDay()->startOfDay();
        
        return [
            'total_conversations' => Conversation::count(),
            'conversations_today' => Conversation::where('created_at', '>=', $today)->count(),
            'conversations_yesterday' => Conversation::whereBetween('created_at', [$yesterday, $today])->count(),
            'total_messages' => Message::count(),
            'messages_today' => Message::where('created_at', '>=', $today)->count(),
            'average_messages_per_conversation' => $this->getAverageMessagesPerConversation(),
            'active_sessions' => $this->getActiveSessionCount()
        ];
    }

    protected function getDatabaseMetrics(): array
    {
        return [
            'connection_count' => $this->getDatabaseConnectionCount(),
            'query_cache_hit_rate' => $this->getQueryCacheHitRate(),
            'slow_query_count' => $this->getSlowQueryCount(),
            'table_sizes' => $this->getTableSizes()
        ];
    }

    protected function getRecentErrors(): array
    {
        // Get recent failed agent executions
        $failedExecutions = AgentExecution::where('status', 'failed')
            ->where('created_at', '>=', now()->subHours(24))
            ->orderBy('created_at', 'desc')
            ->limit(50)
            ->get();

        return $failedExecutions->map(function ($execution) {
            return [
                'id' => $execution->id,
                'agent_id' => $execution->agent_id,
                'session_id' => $execution->session_id,
                'error_message' => $execution->error_message,
                'created_at' => $execution->created_at->toISOString(),
                'execution_time_ms' => $execution->execution_time_ms
            ];
        })->toArray();
    }

    protected function analyzeErrors(array $errors): array
    {
        $errorsByAgent = collect($errors)->groupBy('agent_id');
        $errorsByType = collect($errors)->groupBy('error_message');
        
        return [
            'total_errors' => count($errors),
            'errors_by_agent' => $errorsByAgent->map->count()->toArray(),
            'most_common_errors' => $errorsByType->map->count()
                ->sortDesc()
                ->take(5)
                ->toArray(),
            'error_rate_trend' => $this->getErrorRateTrend(),
            'recommendations' => $this->getErrorRecommendations($errors)
        ];
    }

    protected function getSystemUptime(): int
    {
        // Simple uptime calculation - in production, this would be more sophisticated
        return time() - filemtime(base_path());
    }

    protected function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $limit = (int) $limit;
        
        switch ($last) {
            case 'g': $limit *= 1024;
            case 'm': $limit *= 1024;
            case 'k': $limit *= 1024;
        }
        
        return $limit;
    }

    protected function getProcessCount(): int
    {
        // Simplified process count - would be platform-specific in production
        return 1;
    }

    protected function getAverageMessagesPerConversation(): float
    {
        $result = DB::table('conversations')
            ->join('messages', 'conversations.id', '=', 'messages.conversation_id')
            ->selectRaw('AVG(message_count) as avg_messages')
            ->first();
            
        return $result->avg_messages ?? 0;
    }

    protected function getActiveSessionCount(): int
    {
        return Conversation::where('last_activity_at', '>=', now()->subMinutes(30))->count();
    }

    protected function getDatabaseConnectionCount(): int
    {
        // This would query the database for active connections
        return 1;
    }

    protected function getQueryCacheHitRate(): float
    {
        // This would calculate query cache hit rate
        return 95.5;
    }

    protected function getSlowQueryCount(): int
    {
        // This would count slow queries from logs
        return 0;
    }

    protected function getTableSizes(): array
    {
        // This would return table sizes
        return [
            'conversations' => '1.2MB',
            'messages' => '5.8MB',
            'agent_executions' => '3.4MB'
        ];
    }

    protected function getErrorRateTrend(): array
    {
        // Calculate error rate trend over the last 24 hours
        $hours = [];
        for ($i = 23; $i >= 0; $i--) {
            $hour = now()->subHours($i);
            $errors = AgentExecution::where('status', 'failed')
                ->whereBetween('created_at', [$hour, $hour->copy()->addHour()])
                ->count();
            
            $hours[] = [
                'hour' => $hour->format('H:00'),
                'error_count' => $errors
            ];
        }
        
        return $hours;
    }

    protected function getErrorRecommendations(array $errors): array
    {
        $recommendations = [];
        
        $errorsByAgent = collect($errors)->groupBy('agent_id');
        
        foreach ($errorsByAgent as $agentId => $agentErrors) {
            if (count($agentErrors) > 10) {
                $recommendations[] = "Agent {$agentId} has high error rate - review configuration";
            }
        }
        
        if (count($errors) > 50) {
            $recommendations[] = "High overall error rate - consider system optimization";
        }
        
        return $recommendations;
    }
}
