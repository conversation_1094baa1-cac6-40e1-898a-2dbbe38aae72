<?php

declare(strict_types=1);

namespace App\Services\Performance;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class PerformanceMonitor
{
    protected array $metrics = [];
    protected array $timers = [];

    /**
     * Start timing an operation
     */
    public function startTimer(string $operation): void
    {
        $this->timers[$operation] = microtime(true);
    }

    /**
     * End timing an operation and record the metric
     */
    public function endTimer(string $operation): float
    {
        if (!isset($this->timers[$operation])) {
            return 0.0;
        }

        $duration = microtime(true) - $this->timers[$operation];
        $this->recordMetric($operation, $duration * 1000); // Convert to milliseconds
        unset($this->timers[$operation]);

        return $duration;
    }

    /**
     * Record a performance metric
     */
    public function recordMetric(string $name, float $value, array $tags = []): void
    {
        $metric = [
            'name' => $name,
            'value' => $value,
            'timestamp' => microtime(true),
            'tags' => $tags
        ];

        $this->metrics[] = $metric;

        // Store in cache for real-time monitoring
        $cacheKey = "performance_metrics:" . date('Y-m-d-H');
        $existingMetrics = Cache::get($cacheKey, []);
        $existingMetrics[] = $metric;
        Cache::put($cacheKey, $existingMetrics, 3600); // Store for 1 hour

        // Log performance issues
        if ($this->isPerformanceIssue($name, $value)) {
            Log::warning("Performance issue detected", [
                'metric' => $name,
                'value' => $value,
                'threshold' => $this->getThreshold($name)
            ]);
        }
    }

    /**
     * Get performance statistics
     */
    public function getStatistics(string $period = '1h'): array
    {
        $cacheKey = "performance_metrics:" . date('Y-m-d-H');
        $metrics = Cache::get($cacheKey, []);

        if (empty($metrics)) {
            return [
                'total_requests' => 0,
                'average_response_time' => 0,
                'slowest_operations' => [],
                'error_rate' => 0
            ];
        }

        $responseTimeMetrics = array_filter($metrics, fn($m) => str_contains($m['name'], 'response_time'));
        $responseTimes = array_column($responseTimeMetrics, 'value');

        $slowestOperations = collect($metrics)
            ->groupBy('name')
            ->map(function ($group, $name) {
                $values = array_column($group->toArray(), 'value');
                return [
                    'operation' => $name,
                    'avg_time' => array_sum($values) / count($values),
                    'max_time' => max($values),
                    'count' => count($values)
                ];
            })
            ->sortByDesc('avg_time')
            ->take(10)
            ->values()
            ->toArray();

        return [
            'total_requests' => count($responseTimeMetrics),
            'average_response_time' => !empty($responseTimes) ? array_sum($responseTimes) / count($responseTimes) : 0,
            'slowest_operations' => $slowestOperations,
            'error_rate' => $this->calculateErrorRate($metrics),
            'memory_usage' => $this->getMemoryUsage(),
            'database_performance' => $this->getDatabasePerformance()
        ];
    }

    /**
     * Monitor database performance
     */
    public function monitorDatabaseQuery(string $query, float $executionTime): void
    {
        $this->recordMetric('database_query_time', $executionTime, [
            'query_type' => $this->getQueryType($query)
        ]);

        // Log slow queries
        if ($executionTime > 1000) { // Slower than 1 second
            Log::warning("Slow database query detected", [
                'query' => $query,
                'execution_time' => $executionTime
            ]);
        }
    }

    /**
     * Monitor memory usage
     */
    public function monitorMemoryUsage(): void
    {
        $memoryUsage = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);

        $this->recordMetric('memory_usage', $memoryUsage);
        $this->recordMetric('peak_memory_usage', $peakMemory);

        // Alert on high memory usage (>128MB)
        if ($memoryUsage > 128 * 1024 * 1024) {
            Log::warning("High memory usage detected", [
                'current_usage' => $this->formatBytes($memoryUsage),
                'peak_usage' => $this->formatBytes($peakMemory)
            ]);
        }
    }

    /**
     * Monitor agent execution performance
     */
    public function monitorAgentExecution(string $agentName, float $executionTime, bool $success): void
    {
        $this->recordMetric("agent_{$agentName}_execution_time", $executionTime);
        $this->recordMetric("agent_{$agentName}_success_rate", $success ? 1 : 0);

        if (!$success) {
            Log::error("Agent execution failed", [
                'agent' => $agentName,
                'execution_time' => $executionTime
            ]);
        }
    }

    /**
     * Get system health score
     */
    public function getHealthScore(): array
    {
        $stats = $this->getStatistics();
        $score = 100;
        $issues = [];

        // Response time health (target: <2000ms)
        if ($stats['average_response_time'] > 2000) {
            $score -= 20;
            $issues[] = 'High response times';
        }

        // Error rate health (target: <5%)
        if ($stats['error_rate'] > 5) {
            $score -= 30;
            $issues[] = 'High error rate';
        }

        // Memory usage health
        $memoryUsage = memory_get_usage(true);
        if ($memoryUsage > 256 * 1024 * 1024) { // >256MB
            $score -= 25;
            $issues[] = 'High memory usage';
        }

        // Database performance health
        $dbPerf = $stats['database_performance'];
        if ($dbPerf['avg_query_time'] > 500) { // >500ms
            $score -= 15;
            $issues[] = 'Slow database queries';
        }

        return [
            'score' => max(0, $score),
            'status' => $this->getHealthStatus($score),
            'issues' => $issues,
            'recommendations' => $this->getRecommendations($issues)
        ];
    }

    /**
     * Optimize system performance
     */
    public function optimizePerformance(): array
    {
        $optimizations = [];

        // Clear expired cache entries
        $this->clearExpiredCache();
        $optimizations[] = 'Cleared expired cache entries';

        // Optimize database connections
        $this->optimizeDatabaseConnections();
        $optimizations[] = 'Optimized database connections';

        // Clean up old metrics
        $this->cleanupOldMetrics();
        $optimizations[] = 'Cleaned up old performance metrics';

        return $optimizations;
    }

    protected function isPerformanceIssue(string $name, float $value): bool
    {
        $thresholds = [
            'response_time' => 3000, // 3 seconds
            'database_query_time' => 1000, // 1 second
            'agent_execution_time' => 2000, // 2 seconds
            'memory_usage' => 256 * 1024 * 1024, // 256MB
        ];

        foreach ($thresholds as $metric => $threshold) {
            if (str_contains($name, $metric) && $value > $threshold) {
                return true;
            }
        }

        return false;
    }

    protected function getThreshold(string $name): float
    {
        if (str_contains($name, 'response_time')) return 3000;
        if (str_contains($name, 'database_query_time')) return 1000;
        if (str_contains($name, 'agent_execution_time')) return 2000;
        if (str_contains($name, 'memory_usage')) return 256 * 1024 * 1024;
        
        return 0;
    }

    protected function calculateErrorRate(array $metrics): float
    {
        $errorMetrics = array_filter($metrics, fn($m) => str_contains($m['name'], 'error') || str_contains($m['name'], 'failed'));
        $totalMetrics = count($metrics);
        
        return $totalMetrics > 0 ? (count($errorMetrics) / $totalMetrics) * 100 : 0;
    }

    protected function getMemoryUsage(): array
    {
        return [
            'current' => memory_get_usage(true),
            'peak' => memory_get_peak_usage(true),
            'limit' => ini_get('memory_limit')
        ];
    }

    protected function getDatabasePerformance(): array
    {
        // Get recent query performance from cache
        $cacheKey = "performance_metrics:" . date('Y-m-d-H');
        $metrics = Cache::get($cacheKey, []);
        
        $dbMetrics = array_filter($metrics, fn($m) => str_contains($m['name'], 'database_query_time'));
        $queryTimes = array_column($dbMetrics, 'value');
        
        return [
            'total_queries' => count($dbMetrics),
            'avg_query_time' => !empty($queryTimes) ? array_sum($queryTimes) / count($queryTimes) : 0,
            'slowest_query' => !empty($queryTimes) ? max($queryTimes) : 0
        ];
    }

    protected function getQueryType(string $query): string
    {
        $query = strtoupper(trim($query));
        if (str_starts_with($query, 'SELECT')) return 'SELECT';
        if (str_starts_with($query, 'INSERT')) return 'INSERT';
        if (str_starts_with($query, 'UPDATE')) return 'UPDATE';
        if (str_starts_with($query, 'DELETE')) return 'DELETE';
        return 'OTHER';
    }

    protected function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    protected function getHealthStatus(int $score): string
    {
        if ($score >= 90) return 'excellent';
        if ($score >= 75) return 'good';
        if ($score >= 60) return 'fair';
        if ($score >= 40) return 'poor';
        return 'critical';
    }

    protected function getRecommendations(array $issues): array
    {
        $recommendations = [];
        
        foreach ($issues as $issue) {
            switch ($issue) {
                case 'High response times':
                    $recommendations[] = 'Consider implementing caching or optimizing slow operations';
                    break;
                case 'High error rate':
                    $recommendations[] = 'Review error logs and implement better error handling';
                    break;
                case 'High memory usage':
                    $recommendations[] = 'Optimize memory usage or increase server resources';
                    break;
                case 'Slow database queries':
                    $recommendations[] = 'Add database indexes or optimize query structure';
                    break;
            }
        }
        
        return $recommendations;
    }

    protected function clearExpiredCache(): void
    {
        // Implementation would clear expired cache entries
    }

    protected function optimizeDatabaseConnections(): void
    {
        // Implementation would optimize database connection pool
    }

    protected function cleanupOldMetrics(): void
    {
        // Implementation would clean up old performance metrics
    }
}
