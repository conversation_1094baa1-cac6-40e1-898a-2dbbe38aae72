"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d2266bf609b5\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcbGFyYWdvblxcd3d3XFxhdWdtZW50TUNQXFxuZXh0anMtYWRtaW5cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImQyMjY2YmY2MDliNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useApi: () => (/* binding */ useApi),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth,useApi auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tenant, setTenant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isAuthenticated = !!user && !!token;\n    // Initialize auth state from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initAuth = {\n                \"AuthProvider.useEffect.initAuth\": async ()=>{\n                    try {\n                        const storedToken = localStorage.getItem('auth_token');\n                        const storedUser = localStorage.getItem('user_data');\n                        const storedTenant = localStorage.getItem('tenant_data');\n                        if (storedToken && storedUser) {\n                            setToken(storedToken);\n                            setUser(JSON.parse(storedUser));\n                            if (storedTenant) {\n                                setTenant(JSON.parse(storedTenant));\n                            }\n                            // Verify token is still valid\n                            await refreshUser();\n                        }\n                    } catch (error) {\n                        console.error('Auth initialization error:', error);\n                        clearAuthData();\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initAuth\"];\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (email, password, tenantSubdomain)=>{\n        try {\n            const response = await fetch('http://localhost:8000/api/auth/login', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email,\n                    password,\n                    tenant_subdomain: tenantSubdomain\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                const { user: userData, tenant: tenantData, token: authToken } = data.data;\n                setUser(userData);\n                setTenant(tenantData);\n                setToken(authToken);\n                // Store in localStorage\n                localStorage.setItem('auth_token', authToken);\n                localStorage.setItem('user_data', JSON.stringify(userData));\n                if (tenantData) {\n                    localStorage.setItem('tenant_data', JSON.stringify(tenantData));\n                }\n                return true;\n            } else {\n                throw new Error(data.error || 'Login failed');\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n            return false;\n        }\n    };\n    const logout = async ()=>{\n        try {\n            if (token) {\n                await fetch('http://localhost:8000/api/auth/logout', {\n                    method: 'POST',\n                    headers: {\n                        'Authorization': \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n            }\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            clearAuthData();\n            router.push('/login');\n        }\n    };\n    const updateProfile = async (data)=>{\n        try {\n            if (!token) return false;\n            const response = await fetch('http://localhost:8000/api/auth/profile', {\n                method: 'PUT',\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(data)\n            });\n            const result = await response.json();\n            if (result.success) {\n                const updatedUser = {\n                    ...user,\n                    ...result.data.user\n                };\n                setUser(updatedUser);\n                localStorage.setItem('user_data', JSON.stringify(updatedUser));\n                return true;\n            } else {\n                throw new Error(result.error || 'Profile update failed');\n            }\n        } catch (error) {\n            console.error('Profile update error:', error);\n            return false;\n        }\n    };\n    const refreshUser = async ()=>{\n        try {\n            if (!token) return;\n            const response = await fetch('http://localhost:8000/api/auth/me', {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                const { user: userData, tenant: tenantData } = data.data;\n                setUser(userData);\n                setTenant(tenantData);\n                // Update localStorage\n                localStorage.setItem('user_data', JSON.stringify(userData));\n                if (tenantData) {\n                    localStorage.setItem('tenant_data', JSON.stringify(tenantData));\n                }\n            } else {\n                // Token is invalid\n                clearAuthData();\n                router.push('/login');\n            }\n        } catch (error) {\n            console.error('Refresh user error:', error);\n            clearAuthData();\n            router.push('/login');\n        }\n    };\n    const clearAuthData = ()=>{\n        setUser(null);\n        setTenant(null);\n        setToken(null);\n        localStorage.removeItem('auth_token');\n        localStorage.removeItem('user_data');\n        localStorage.removeItem('tenant_data');\n    };\n    // API helper function with auth\n    const apiCall = async function(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const headers = {\n            'Content-Type': 'application/json',\n            ...token && {\n                'Authorization': \"Bearer \".concat(token)\n            },\n            ...tenant && {\n                'X-Tenant-ID': tenant.id\n            },\n            ...options.headers\n        };\n        const response = await fetch(\"http://localhost:8000/api\".concat(endpoint), {\n            ...options,\n            headers\n        });\n        if (response.status === 401) {\n            // Token expired or invalid\n            clearAuthData();\n            router.push('/login');\n            throw new Error('Authentication required');\n        }\n        return response;\n    };\n    const value = {\n        user,\n        tenant,\n        token,\n        isLoading,\n        isAuthenticated,\n        login,\n        logout,\n        updateProfile,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 250,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"LQsZIWx/yCbPHNdYfWhZFvNVfuA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Higher-order component for protected routes\nfunction withAuth(Component) {\n    var _s = $RefreshSig$();\n    return _s(function AuthenticatedComponent(props) {\n        _s();\n        const { isAuthenticated, isLoading } = useAuth();\n        const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"withAuth.AuthenticatedComponent.useEffect\": ()=>{\n                if (!isLoading && !isAuthenticated) {\n                    router.push('/login');\n                }\n            }\n        }[\"withAuth.AuthenticatedComponent.useEffect\"], [\n            isAuthenticated,\n            isLoading,\n            router\n        ]);\n        if (isLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 278,\n                columnNumber: 9\n            }, this);\n        }\n        if (!isAuthenticated) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\augmentMCP\\\\nextjs-admin\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 288,\n            columnNumber: 12\n        }, this);\n    }, \"mEH+GTDGNx6l1kiic8iucxoBZHI=\", false, function() {\n        return [\n            useAuth,\n            next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n        ];\n    });\n}\n// Hook for making authenticated API calls\nfunction useApi() {\n    _s2();\n    const { token, tenant } = useAuth();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const apiCall = async function(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const headers = {\n            'Content-Type': 'application/json',\n            ...token && {\n                'Authorization': \"Bearer \".concat(token)\n            },\n            ...tenant && {\n                'X-Tenant-ID': tenant.id\n            },\n            ...options.headers\n        };\n        const response = await fetch(\"http://localhost:8000/api\".concat(endpoint), {\n            ...options,\n            headers\n        });\n        if (response.status === 401) {\n            router.push('/login');\n            throw new Error('Authentication required');\n        }\n        return response;\n    };\n    return {\n        apiCall\n    };\n}\n_s2(useApi, \"10kWhbfrcpP8NLrtg6YhXrYFKow=\", false, function() {\n    return [\n        useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ })

});