<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\SecurityService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SecurityController extends Controller
{
    protected SecurityService $securityService;

    public function __construct(SecurityService $securityService)
    {
        $this->securityService = $securityService;
    }

    /**
     * Validate password strength
     */
    public function validatePassword(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'password' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $result = $this->securityService->validatePasswordStrength($request->password);

            return response()->json([
                'success' => true,
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Scan content for malicious patterns
     */
    public function scanContent(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'content' => 'required|string',
                'type' => 'nullable|string|in:text,html,sql,javascript'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $result = $this->securityService->scanContent(
                $request->content,
                $request->type ?? 'text'
            );

            // Log if malicious content detected
            if ($result['is_malicious']) {
                $this->securityService->logSecurityEvent(
                    'malicious_content_detected',
                    'Malicious content scan detected threats',
                    [
                        'threats' => $result['threats'],
                        'risk_level' => $result['risk_level'],
                        'content_type' => $request->type ?? 'text'
                    ],
                    $result['risk_level'] === 'high' ? 'high' : 'medium'
                );
            }

            return response()->json([
                'success' => true,
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check for suspicious login activity
     */
    public function checkSuspiciousLogin(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email',
                'ip_address' => 'nullable|ip'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $ipAddress = $request->ip_address ?? $request->ip();
            $result = $this->securityService->checkSuspiciousLogin($request->email, $ipAddress);

            // Log suspicious activity
            if ($result['is_suspicious']) {
                $this->securityService->logSecurityEvent(
                    'suspicious_login_detected',
                    'Suspicious login activity detected',
                    [
                        'email' => $request->email,
                        'ip_address' => $ipAddress,
                        'risk_level' => $result['risk_level'],
                        'factors' => $result['factors']
                    ],
                    $result['risk_level'] === 'high' ? 'high' : 'medium'
                );
            }

            return response()->json([
                'success' => true,
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate API key
     */
    public function generateApiKey(): JsonResponse
    {
        try {
            $apiKey = $this->securityService->generateApiKey();

            $this->securityService->logSecurityEvent(
                'api_key_generated',
                'New API key generated',
                ['key_prefix' => substr($apiKey, 0, 8)],
                'low'
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'api_key' => $apiKey,
                    'created_at' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate API key
     */
    public function validateApiKey(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'api_key' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $isValid = $this->securityService->validateApiKey($request->api_key);

            if (!$isValid) {
                $this->securityService->logSecurityEvent(
                    'invalid_api_key_used',
                    'Invalid API key validation attempt',
                    ['key_prefix' => substr($request->api_key, 0, 8)],
                    'medium'
                );
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'is_valid' => $isValid,
                    'validated_at' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get security dashboard
     */
    public function getSecurityDashboard(): JsonResponse
    {
        try {
            // Get recent security events
            $recentEvents = \App\Models\AnalyticsEvent::where('event_type', 'security_event')
                ->where('occurred_at', '>=', now()->subDays(7))
                ->orderBy('occurred_at', 'desc')
                ->limit(50)
                ->get()
                ->map(function ($event) {
                    return [
                        'id' => $event->id,
                        'event_name' => $event->event_name,
                        'severity' => $event->properties['severity'] ?? 'medium',
                        'description' => $event->properties['description'] ?? '',
                        'ip_address' => $event->properties['ip_address'] ?? 'unknown',
                        'occurred_at' => $event->occurred_at
                    ];
                });

            // Calculate security metrics
            $totalEvents = \App\Models\AnalyticsEvent::where('event_type', 'security_event')
                ->where('occurred_at', '>=', now()->subDays(30))
                ->count();

            $highSeverityEvents = \App\Models\AnalyticsEvent::where('event_type', 'security_event')
                ->where('occurred_at', '>=', now()->subDays(30))
                ->whereJsonContains('properties->severity', 'high')
                ->count();

            $threatsByType = \App\Models\AnalyticsEvent::where('event_type', 'security_event')
                ->where('occurred_at', '>=', now()->subDays(30))
                ->selectRaw('event_name, COUNT(*) as count')
                ->groupBy('event_name')
                ->orderByDesc('count')
                ->get()
                ->toArray();

            return response()->json([
                'success' => true,
                'data' => [
                    'recent_events' => $recentEvents,
                    'metrics' => [
                        'total_events_30d' => $totalEvents,
                        'high_severity_events_30d' => $highSeverityEvents,
                        'threat_level' => $this->calculateThreatLevel($highSeverityEvents, $totalEvents),
                        'threats_by_type' => $threatsByType
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Record failed login attempt
     */
    public function recordFailedAttempt(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'identifier' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $this->securityService->recordFailedAttempt($request->identifier);

            return response()->json([
                'success' => true,
                'message' => 'Failed attempt recorded'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear failed attempts
     */
    public function clearFailedAttempts(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'identifier' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $this->securityService->clearFailedAttempts($request->identifier);

            return response()->json([
                'success' => true,
                'message' => 'Failed attempts cleared'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check rate limit status
     */
    public function checkRateLimit(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'key' => 'required|string',
                'max_attempts' => 'nullable|integer|min:1|max:1000',
                'decay_minutes' => 'nullable|integer|min:1|max:60'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $isLimited = $this->securityService->checkRateLimit(
                $request->key,
                $request->max_attempts ?? 60,
                $request->decay_minutes ?? 1
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'is_rate_limited' => $isLimited,
                    'key' => $request->key,
                    'checked_at' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate threat level
     */
    protected function calculateThreatLevel(int $highSeverityEvents, int $totalEvents): string
    {
        if ($totalEvents === 0) return 'low';
        
        $ratio = $highSeverityEvents / $totalEvents;
        
        if ($ratio >= 0.3) return 'critical';
        if ($ratio >= 0.15) return 'high';
        if ($ratio >= 0.05) return 'medium';
        
        return 'low';
    }
}
