<?php

declare(strict_types=1);

namespace App\Services\MCP\Agents;

use App\Services\MCP\AbstractMCPAgent;
use Illuminate\Support\Facades\Log;

class IntentAgent extends AbstractMCPAgent
{
    protected array $intentPatterns = [
        'question' => [
            'patterns' => ['/^(what|how|when|where|why|who|which)/i', '/\?$/'],
            'keywords' => ['explain', 'tell me', 'describe', 'define'],
            'confidence_threshold' => 0.7
        ],
        'request' => [
            'patterns' => ['/^(can you|could you|please|help me)/i'],
            'keywords' => ['create', 'generate', 'make', 'build', 'write'],
            'confidence_threshold' => 0.8
        ],
        'search' => [
            'patterns' => ['/^(find|search|look for|show me)/i'],
            'keywords' => ['find', 'search', 'lookup', 'locate'],
            'confidence_threshold' => 0.7
        ],
        'conversation' => [
            'patterns' => ['/^(hi|hello|hey|thanks|thank you|bye)/i'],
            'keywords' => ['greeting', 'goodbye', 'thanks'],
            'confidence_threshold' => 0.9
        ],
        'workflow' => [
            'patterns' => ['/^(execute|run|start|trigger)/i'],
            'keywords' => ['workflow', 'process', 'automation', 'sequence'],
            'confidence_threshold' => 0.8
        ]
    ];

    public function getName(): string
    {
        return 'intent';
    }

    public function getType(): string
    {
        return 'primary';
    }

    public function getExecutionOrder(): int
    {
        return 1; // First in the pipeline
    }

    protected function process(array $input, string $sessionId): array
    {
        $message = $input['message'] ?? '';
        $context = $input['context'] ?? [];

        Log::info('Intent Agent processing message', [
            'session_id' => $sessionId,
            'message_length' => strlen($message)
        ]);

        // Analyze intent
        $intentAnalysis = $this->analyzeIntent($message, $context);
        
        // Extract entities
        $entities = $this->extractEntities($message);
        
        // Determine confidence and next actions
        $confidence = $intentAnalysis['confidence'];
        $intent = $intentAnalysis['intent'];
        
        $result = [
            'intent' => $intent,
            'confidence' => $confidence,
            'entities' => $entities,
            'requires_knowledge_base' => $this->requiresKnowledgeBase($intent),
            'requires_llm' => $this->requiresLLM($intent),
            'requires_workflow' => $this->requiresWorkflow($intent),
            'message' => $message,
            'context' => $context,
            'metadata' => [
                'processing_time' => microtime(true) - ($_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true)),
                'agent' => $this->getName()
            ]
        ];

        Log::info('Intent Agent completed analysis', [
            'session_id' => $sessionId,
            'intent' => $intent,
            'confidence' => $confidence
        ]);

        return $result;
    }

    protected function analyzeIntent(string $message, array $context): array
    {
        $message = strtolower(trim($message));
        $scores = [];

        foreach ($this->intentPatterns as $intent => $config) {
            $score = 0;
            $matches = 0;

            // Pattern matching
            foreach ($config['patterns'] as $pattern) {
                if (preg_match($pattern, $message)) {
                    $score += 0.4;
                    $matches++;
                }
            }

            // Keyword matching
            foreach ($config['keywords'] as $keyword) {
                if (strpos($message, $keyword) !== false) {
                    $score += 0.3;
                    $matches++;
                }
            }

            // Context-based scoring
            if (!empty($context['previous_intent']) && $context['previous_intent'] === $intent) {
                $score += 0.2; // Continuity bonus
            }

            // Normalize score
            if ($matches > 0) {
                $score = min($score, 1.0);
                $scores[$intent] = $score;
            }
        }

        // Find the highest scoring intent
        if (empty($scores)) {
            return ['intent' => 'general', 'confidence' => 0.5];
        }

        arsort($scores);
        $topIntent = array_key_first($scores);
        $confidence = $scores[$topIntent];

        // Apply confidence threshold
        $threshold = $this->intentPatterns[$topIntent]['confidence_threshold'] ?? 0.7;
        if ($confidence < $threshold) {
            return ['intent' => 'general', 'confidence' => $confidence];
        }

        return ['intent' => $topIntent, 'confidence' => $confidence];
    }

    protected function extractEntities(string $message): array
    {
        $entities = [];

        // Extract dates
        if (preg_match_all('/\b(\d{1,2}\/\d{1,2}\/\d{4}|\d{4}-\d{2}-\d{2}|today|tomorrow|yesterday)\b/i', $message, $matches)) {
            $entities['dates'] = $matches[0];
        }

        // Extract numbers
        if (preg_match_all('/\b\d+\b/', $message, $matches)) {
            $entities['numbers'] = array_map('intval', $matches[0]);
        }

        // Extract emails
        if (preg_match_all('/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/', $message, $matches)) {
            $entities['emails'] = $matches[0];
        }

        // Extract URLs
        if (preg_match_all('/https?:\/\/[^\s]+/', $message, $matches)) {
            $entities['urls'] = $matches[0];
        }

        return $entities;
    }

    protected function requiresKnowledgeBase(string $intent): bool
    {
        return in_array($intent, ['question', 'search', 'request']);
    }

    protected function requiresLLM(string $intent): bool
    {
        return in_array($intent, ['question', 'request', 'conversation', 'general']);
    }

    protected function requiresWorkflow(string $intent): bool
    {
        return $intent === 'workflow';
    }

    public function validateInput(array $input): bool
    {
        return isset($input['message']) && is_string($input['message']) && !empty(trim($input['message']));
    }
}
