'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { useApi } from '@/contexts/AuthContext'
import {
  Plus,
  Play,
  Pause,
  Settings,
  Copy,
  Trash2,
  GitBranch,
  Clock,
  CheckCircle,
  AlertCircle,
  Activity
} from 'lucide-react'

interface WorkflowItem {
  id: number
  name: string
  description: string
  status: 'draft' | 'active' | 'inactive' | 'archived'
  trigger_type: string
  execution_count: number
  last_executed_at: string | null
  created_at: string
  statistics: {
    total_executions: number
    successful_executions: number
    failed_executions: number
    average_duration: number
    success_rate: number
  }
  node_count: number
}

export default function WorkflowsPage() {
  const { apiCall } = useApi()
  const [workflows, setWorkflows] = useState<WorkflowItem[]>([])
  const [showBuilder, setShowBuilder] = useState(false)
  const [selectedWorkflow, setSelectedWorkflow] = useState<WorkflowItem | null>(null) 
  const [isLoading, setIsLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)

  const fetchWorkflows = async () => {
    setIsLoading(true)
    try {
      const response = await apiCall('/workflows')
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setWorkflows(data.data)
        }
      }
    } catch (error) {
      console.error('Failed to fetch workflows:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const createWorkflow = () => {
    setShowBuilder(true)
    setSelectedWorkflow(null)
  }

  const editWorkflow = (workflow: WorkflowItem) => {
    setSelectedWorkflow(workflow)
    setShowBuilder(true)
  }

  const duplicateWorkflow = (workflow: WorkflowItem) => {
    const newWorkflow = {
      ...workflow,
      id: Date.now(),
      name: workflow.name + ' (Copy)',
      status: 'draft' as const,
      execution_count: 0,
      last_executed_at: null,
      created_at: new Date().toISOString()
    }
    setWorkflows(prev => [newWorkflow, ...prev])
  }

  const toggleWorkflowStatus = (workflow: WorkflowItem) => {
    const newStatus = workflow.status === 'active' ? 'inactive' : 'active'
    setWorkflows(prev => prev.map(w =>
      w.id === workflow.id ? { ...w, status: newStatus } : w
    ))
  }

  const deleteWorkflow = (workflow: WorkflowItem) => {
    if (!confirm(`Are you sure you want to delete "${workflow.name}"?`)) {
      return
    }
    setWorkflows(prev => prev.filter(w => w.id !== workflow.id))
  }

  useEffect(() => {
    fetchWorkflows()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success'
      case 'inactive': return 'secondary'
      case 'draft': return 'warning'
      default: return 'secondary'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-4 w-4" />
      case 'inactive': return <Pause className="h-4 w-4" />
      case 'draft': return <AlertCircle className="h-4 w-4" />
      default: return <Activity className="h-4 w-4" />
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="space-y-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-48 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Workflows</h1>
            <p className="text-gray-600">
              Design and manage multi-step AI workflows
            </p>
          </div>
          <Button onClick={createWorkflow}>
            <Plus className="h-4 w-4 mr-2" />
            Create Workflow
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid gap-6 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Workflows</CardTitle>
              <GitBranch className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{workflows.length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Workflows</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {workflows.filter(w => w.status === 'active').length}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Executions</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {workflows.reduce((sum, w) => sum + w.execution_count, 0)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Draft Workflows</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {workflows.filter(w => w.status === 'draft').length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Workflows Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {workflows.map((workflow) => (
            <Card key={workflow.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{workflow.name}</CardTitle>
                    <p className="text-sm text-gray-600 mt-1">
                      {workflow.description}
                    </p>
                  </div>
                  <Badge variant={getStatusColor(workflow.status)} className="flex items-center gap-1">
                    {getStatusIcon(workflow.status)}
                    {workflow.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Trigger:</span>
                    <div className="font-medium">{workflow.trigger_type.replace('_', ' ')}</div>
                  </div>
                  <div>
                    <span className="text-gray-500">Steps:</span>
                    <div className="font-medium">{workflow.node_count}</div>
                  </div>
                  <div>
                    <span className="text-gray-500">Executions:</span>
                    <div className="font-medium">{workflow.execution_count}</div>
                  </div>
                  <div>
                    <span className="text-gray-500">Created:</span>
                    <div className="font-medium">{formatDate(workflow.created_at)}</div>
                  </div>
                </div>

                {workflow.last_executed_at && (
                  <div className="text-sm">
                    <span className="text-gray-500">Last executed:</span>
                    <div className="font-medium">{formatDateTime(workflow.last_executed_at)}</div>
                  </div>
                )}

                <div className="flex flex-wrap gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => editWorkflow(workflow)}
                  >
                    <Settings className="h-3 w-3 mr-1" />
                    Edit
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => toggleWorkflowStatus(workflow)}
                  >
                    {workflow.status === 'active' ? (
                      <>
                        <Pause className="h-3 w-3 mr-1" />
                        Pause
                      </>
                    ) : (
                      <>
                        <Play className="h-3 w-3 mr-1" />
                        Activate
                      </>
                    )}
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => duplicateWorkflow(workflow)}
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    Copy
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => deleteWorkflow(workflow)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    Delete
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {workflows.length === 0 && !isLoading && (
          <Card>
            <CardContent className="text-center py-12">
              <div className="text-gray-500 mb-4">
                <GitBranch className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg">No workflows created yet</p>
                <p className="text-sm">Create your first workflow to automate AI processes</p>
              </div>
              <Button onClick={createWorkflow}>
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Workflow
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Workflow Builder Modal */}
        {showBuilder && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <h3 className="text-lg font-semibold mb-4">
                {selectedWorkflow ? `Edit Workflow: ${selectedWorkflow.name}` : 'Create New Workflow'}
              </h3>
              <p className="text-sm text-gray-600 mb-6">
                Drag-and-drop workflow builder will be implemented here with:
              </p>
              <div className="grid md:grid-cols-2 gap-6 mb-6">
                <div>
                  <h4 className="font-medium mb-2">Available Triggers:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Intent detected</li>
                    <li>• Keyword match</li>
                    <li>• Confidence threshold</li>
                    <li>• Time-based triggers</li>
                    <li>• External webhook</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Available Actions:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Send email notification</li>
                    <li>• Create support ticket</li>
                    <li>• Add to CRM</li>
                    <li>• Schedule meeting</li>
                    <li>• Execute custom API call</li>
                  </ul>
                </div>
              </div>
              <div className="bg-gray-100 p-8 rounded-lg mb-6 text-center">
                <GitBranch className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                <p className="text-gray-600">Visual workflow builder interface will be here</p>
                <p className="text-sm text-gray-500">Drag and drop components to build your workflow</p>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowBuilder(false)}>
                  Cancel
                </Button>
                <Button>
                  {selectedWorkflow ? 'Update Workflow' : 'Create Workflow'}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
