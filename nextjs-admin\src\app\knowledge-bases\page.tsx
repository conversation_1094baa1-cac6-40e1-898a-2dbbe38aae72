'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { useApi } from '@/contexts/AuthContext'
import {
  Plus,
  RefreshCw,
  Database,
  FileText,
  Zap,
  Search,
  Settings,
  Upload,
  BarChart3,
  AlertCircle,
  CheckCircle,
  Clock,
  Trash2
} from 'lucide-react'

interface KnowledgeBase {
  id: number
  name: string
  description: string
  status: 'active' | 'inactive' | 'processing'
  document_count: number
  chunk_count: number
  total_tokens: number
  last_updated_at: string | null
  created_at: string
  processing_stats: {
    total_documents: number
    pending_documents: number
    processing_documents: number
    completed_documents: number
    failed_documents: number
    total_chunks: number
    embedded_chunks: number
    pending_chunks: number
    failed_chunks: number
    total_tokens: number
    storage_size: number
  }
  embedding_stats: {
    total_chunks: number
    embedded_chunks: number
    pending_chunks: number
    processing_chunks: number
    failed_chunks: number
    embedding_progress: number
  }
}

export default function KnowledgeBasesPage() {
  const { apiCall } = useApi()
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedKB, setSelectedKB] = useState<KnowledgeBase | null>(null)

  const fetchKnowledgeBases = async () => {
    setIsLoading(true)
    try {
      const response = await apiCall('/knowledge-bases')
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setKnowledgeBases(data.data)
        }
      }
    } catch (error) {
      console.error('Failed to fetch knowledge bases:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchKnowledgeBases()
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'processing': return <Clock className="h-4 w-4 text-yellow-500" />
      default: return <AlertCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success'
      case 'processing': return 'warning'
      default: return 'secondary'
    }
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never'
    return new Date(dateString).toLocaleDateString()
  }

  const getTotalStats = () => {
    return knowledgeBases.reduce((total, kb) => ({
      documents: total.documents + kb.document_count,
      chunks: total.chunks + kb.chunk_count,
      tokens: total.tokens + kb.total_tokens,
      storage: total.storage + (kb.processing_stats?.storage_size || 0)
    }), { documents: 0, chunks: 0, tokens: 0, storage: 0 })
  }

  const totalStats = getTotalStats()

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="space-y-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded"></div>
              ))}
            </div>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Knowledge Bases</h1>
            <p className="text-gray-600">
              Manage your document collections and vector embeddings
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={fetchKnowledgeBases}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button onClick={() => setShowCreateModal(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Knowledge Base
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid gap-6 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Knowledge Bases</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{knowledgeBases.length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Documents</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalStats.documents.toLocaleString()}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Chunks</CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalStats.chunks.toLocaleString()}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Storage Used</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatBytes(totalStats.storage)}</div>
            </CardContent>
          </Card>
        </div>

        {/* Knowledge Bases Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {knowledgeBases.map((kb) => (
            <Card key={kb.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg flex items-center gap-2">
                      {kb.name}
                    </CardTitle>
                    <p className="text-sm text-gray-600 mt-1">
                      {kb.description || 'No description'}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(kb.status)}
                    <Badge variant={getStatusColor(kb.status)}>
                      {kb.status}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Statistics */}
                <div className="grid grid-cols-3 gap-2 text-sm">
                  <div className="text-center">
                    <div className="font-medium">{kb.document_count}</div>
                    <div className="text-gray-500 text-xs">Documents</div>
                  </div>
                  <div className="text-center">
                    <div className="font-medium">{kb.chunk_count.toLocaleString()}</div>
                    <div className="text-gray-500 text-xs">Chunks</div>
                  </div>
                  <div className="text-center">
                    <div className="font-medium">{formatBytes(kb.processing_stats?.storage_size || 0)}</div>
                    <div className="text-gray-500 text-xs">Storage</div>
                  </div>
                </div>

                {/* Embedding Progress */}
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Embedding Progress</span>
                    <span>{Math.round(kb.embedding_stats?.embedding_progress || 0)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${kb.embedding_stats?.embedding_progress || 0}%` }}
                    ></div>
                  </div>
                </div>

                {/* Processing Status */}
                <div className="text-xs text-gray-500 space-y-1">
                  <div>Last updated: {formatDate(kb.last_updated_at)}</div>
                  {kb.processing_stats && (
                    <div className="flex justify-between">
                      <span>Processing: {kb.processing_stats.processing_documents}</span>
                      <span>Failed: {kb.processing_stats.failed_documents}</span>
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex flex-wrap gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => window.open(`/knowledge-bases/${kb.id}`, '_blank')}
                  >
                    <Settings className="h-3 w-3 mr-1" />
                    Manage
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => window.location.href = `/knowledge-bases/${kb.id}/upload`}
                  >
                    <Upload className="h-3 w-3 mr-1" />
                    Upload
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => window.open(`/knowledge-bases/${kb.id}/search`, '_blank')}
                  >
                    <Search className="h-3 w-3 mr-1" />
                    Search
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setSelectedKB(kb)}
                  >
                    <BarChart3 className="h-3 w-3 mr-1" />
                    Analytics
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {knowledgeBases.length === 0 && !isLoading && (
          <Card>
            <CardContent className="text-center py-12">
              <div className="text-gray-500 mb-4">
                <Database className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg">No knowledge bases found</p>
                <p className="text-sm">Create your first knowledge base to get started</p>
              </div>
              <Button onClick={() => setShowCreateModal(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Knowledge Base
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Create Modal */}
        {showCreateModal && (
          <CreateKnowledgeBaseModal
            onClose={() => setShowCreateModal(false)}
            onSuccess={() => {
              setShowCreateModal(false)
              fetchKnowledgeBases()
            }}
          />
        )}

        {/* Analytics Modal */}
        {selectedKB && (
          <KnowledgeBaseAnalyticsModal
            knowledgeBase={selectedKB}
            onClose={() => setSelectedKB(null)}
          />
        )}
      </div>
    </DashboardLayout>
  )
}

// Create Knowledge Base Modal Component
function CreateKnowledgeBaseModal({ onClose, onSuccess }: {
  onClose: () => void
  onSuccess: () => void
}) {
  const { apiCall } = useApi()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  })
  const [errors, setErrors] = useState<any>({})

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setErrors({})

    try {
      const response = await apiCall('/knowledge-bases', {
        method: 'POST',
        body: JSON.stringify(formData)
      })

      const data = await response.json()

      if (data.success) {
        onSuccess()
      } else {
        setErrors(data.errors || { general: data.error || 'Creation failed' })
      }
    } catch (error) {
      setErrors({ general: 'Network error. Please try again.' })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 className="text-lg font-semibold mb-4">Create Knowledge Base</h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          {errors.general && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{errors.general}</p>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter knowledge base name"
              required
            />
            {errors.name && (
              <p className="text-sm text-red-600 mt-1">{errors.name[0]}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter description (optional)"
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Knowledge Base'
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}

// Analytics Modal Component (placeholder)
function KnowledgeBaseAnalyticsModal({ knowledgeBase, onClose }: {
  knowledgeBase: KnowledgeBase
  onClose: () => void
}) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <h3 className="text-lg font-semibold mb-4">{knowledgeBase.name} Analytics</h3>
        <p className="text-sm text-gray-600 mb-6">
          Detailed analytics interface will be implemented in the next step.
        </p>
        <div className="flex justify-end">
          <Button onClick={onClose}>Close</Button>
        </div>
      </div>
    </div>
  )
}
