<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('ai_providers', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // openai, anthropic, groq, etc.
            $table->string('display_name');
            $table->text('description')->nullable();
            $table->boolean('is_enabled')->default(false);
            $table->boolean('is_primary')->default(false);
            $table->integer('priority')->default(1); // For fallback order
            $table->json('configuration'); // API keys, endpoints, etc.
            $table->json('capabilities'); // Supported features
            $table->json('limits')->nullable(); // Rate limits, token limits
            $table->json('usage_stats')->nullable(); // Usage tracking
            $table->timestamp('last_tested_at')->nullable();
            $table->string('status')->default('inactive'); // active, inactive, error
            $table->text('status_message')->nullable();
            $table->timestamps();
            
            $table->index(['is_enabled', 'priority']);
            $table->index(['status', 'is_enabled']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('ai_providers');
    }
};
