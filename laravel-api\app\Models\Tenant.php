<?php

declare(strict_types=1);

namespace App\Models;

use Stancl\Tenancy\Database\Models\Tenant as BaseTenant;
use Stancl\Tenancy\Contracts\TenantWithDatabase;
use Stancl\Tenancy\Database\Concerns\HasDatabase;
use Stancl\Tenancy\Database\Concerns\HasDomains;

class Tenant extends BaseTenant implements TenantWithDatabase
{
    use HasDatabase, HasDomains;

    protected $fillable = [
        'id',
        'name',
        'slug',
        'domain',
        'subdomain',
        'database',
        'data',
        'settings',
        'ai_providers',
        'branding',
        'subscription_plan',
        'subscription_expires_at',
        'is_active'
    ];

    protected $casts = [
        'data' => 'array',
        'settings' => 'array',
        'ai_providers' => 'array',
        'branding' => 'array',
        'subscription_expires_at' => 'datetime',
        'is_active' => 'boolean'
    ];

    public static function getCustomColumns(): array
    {
        return [
            'id',
            'name',
            'domain',
            'database',
            'data',
        ];
    }

    /**
     * Get the tenant's primary domain
     */
    public function getPrimaryDomain(): ?string
    {
        return $this->domains()->first()?->domain;
    }

    /**
     * Get tenant configuration data
     */
    public function getConfigData(string $key, $default = null)
    {
        return data_get($this->data, $key, $default);
    }

    /**
     * Set tenant configuration data
     */
    public function setConfigData(string $key, $value): void
    {
        $data = $this->data ?? [];
        data_set($data, $key, $value);
        $this->update(['data' => $data]);
    }

    /**
     * Check if tenant has active subscription
     */
    public function hasActiveSubscription(): bool
    {
        if ($this->subscription_plan === 'free') {
            return true;
        }

        return $this->subscription_expires_at && $this->subscription_expires_at->isFuture();
    }

    /**
     * Get AI provider configuration
     */
    public function getAiProvider(string $provider): ?array
    {
        return $this->ai_providers[$provider] ?? null;
    }

    /**
     * Set AI provider configuration
     */
    public function setAiProvider(string $provider, array $config): void
    {
        $providers = $this->ai_providers ?? [];
        $providers[$provider] = $config;
        $this->ai_providers = $providers;
        $this->save();
    }

    /**
     * Get tenant setting
     */
    public function getSetting(string $key, $default = null)
    {
        return data_get($this->settings, $key, $default);
    }

    /**
     * Set tenant setting
     */
    public function setSetting(string $key, $value): void
    {
        $settings = $this->settings ?? [];
        data_set($settings, $key, $value);
        $this->settings = $settings;
        $this->save();
    }

    /**
     * Get branding configuration
     */
    public function getBranding(): array
    {
        return $this->branding ?? [
            'primary_color' => '#3B82F6',
            'secondary_color' => '#1F2937',
            'logo_url' => null,
            'company_name' => $this->name,
            'widget_position' => 'bottom-right',
            'widget_theme' => 'light'
        ];
    }

    /**
     * Update branding configuration
     */
    public function updateBranding(array $branding): void
    {
        $this->branding = array_merge($this->getBranding(), $branding);
        $this->save();
    }
}
