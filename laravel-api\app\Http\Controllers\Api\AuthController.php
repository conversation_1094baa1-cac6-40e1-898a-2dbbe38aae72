<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Tenant;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Laravel\Sanctum\HasApiTokens;

class AuthController extends Controller
{
    /**
     * Register a new user and tenant
     */
    public function register(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users',
                'password' => 'required|string|min:8|confirmed',
                'company_name' => 'required|string|max:255',
                'subdomain' => 'required|string|max:50|unique:tenants,subdomain|regex:/^[a-z0-9-]+$/',
                'plan' => 'nullable|string|in:free,starter,professional,enterprise'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            // Create tenant first
            $tenant = Tenant::create([
                'name' => $request->company_name,
                'slug' => $request->subdomain,
                'subdomain' => $request->subdomain,
                'domain' => $request->subdomain . '.' . config('app.domain', 'localhost'),
                'subscription_plan' => $request->plan ?? 'free',
                'subscription_expires_at' => $this->getSubscriptionExpiry($request->plan ?? 'free'),
                'is_active' => true,
                'settings' => $this->getDefaultTenantSettings(),
                'branding' => $this->getDefaultBranding($request->company_name)
            ]);

            // Create tenant database/schema
            $tenant->createDatabase();

            // Create admin user for the tenant
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'tenant_id' => $tenant->id,
                'role' => 'admin',
                'is_active' => true,
                'email_verified_at' => now() // Auto-verify for demo
            ]);

            // Generate API token
            $token = $user->createToken('auth-token')->plainTextToken;

            return response()->json([
                'success' => true,
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'role' => $user->role,
                        'tenant_id' => $user->tenant_id
                    ],
                    'tenant' => [
                        'id' => $tenant->id,
                        'name' => $tenant->name,
                        'subdomain' => $tenant->subdomain,
                        'subscription_plan' => $tenant->subscription_plan
                    ],
                    'token' => $token
                ],
                'message' => 'Registration successful'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Registration failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Login user
     */
    public function login(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email',
                'password' => 'required|string',
                'tenant_subdomain' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            // Find user
            $user = User::where('email', $request->email)->first();

            if (!$user || !Hash::check($request->password, $user->password)) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid credentials'
                ], 401);
            }

            if (!$user->is_active) {
                return response()->json([
                    'success' => false,
                    'error' => 'Account is inactive'
                ], 401);
            }

            // Check tenant if specified
            if ($request->tenant_subdomain) {
                $tenant = Tenant::where('subdomain', $request->tenant_subdomain)->first();
                if (!$tenant || $user->tenant_id !== $tenant->id) {
                    return response()->json([
                        'success' => false,
                        'error' => 'Invalid tenant access'
                    ], 401);
                }
            }

            // Update last login
            $user->updateLastLogin();

            // Generate API token
            $token = $user->createToken('auth-token')->plainTextToken;

            return response()->json([
                'success' => true,
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'role' => $user->role,
                        'tenant_id' => $user->tenant_id,
                        'last_login_at' => $user->last_login_at
                    ],
                    'tenant' => $user->tenant ? [
                        'id' => $user->tenant->id,
                        'name' => $user->tenant->name,
                        'subdomain' => $user->tenant->subdomain,
                        'subscription_plan' => $user->tenant->subscription_plan,
                        'branding' => $user->tenant->getBranding()
                    ] : null,
                    'token' => $token
                ],
                'message' => 'Login successful'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Login failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Logout user
     */
    public function logout(Request $request): JsonResponse
    {
        try {
            $request->user()->currentAccessToken()->delete();

            return response()->json([
                'success' => true,
                'message' => 'Logout successful'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Logout failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current user
     */
    public function me(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            
            return response()->json([
                'success' => true,
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'role' => $user->role,
                        'tenant_id' => $user->tenant_id,
                        'is_active' => $user->is_active,
                        'last_login_at' => $user->last_login_at,
                        'created_at' => $user->created_at
                    ],
                    'tenant' => $user->tenant ? [
                        'id' => $user->tenant->id,
                        'name' => $user->tenant->name,
                        'subdomain' => $user->tenant->subdomain,
                        'subscription_plan' => $user->tenant->subscription_plan,
                        'subscription_expires_at' => $user->tenant->subscription_expires_at,
                        'branding' => $user->tenant->getBranding(),
                        'settings' => $user->tenant->settings
                    ] : null
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get user data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            
            $validator = Validator::make($request->all(), [
                'name' => 'sometimes|string|max:255',
                'email' => 'sometimes|email|max:255|unique:users,email,' . $user->id,
                'current_password' => 'required_with:password',
                'password' => 'sometimes|string|min:8|confirmed'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            // Verify current password if changing password
            if ($request->has('password')) {
                if (!Hash::check($request->current_password, $user->password)) {
                    return response()->json([
                        'success' => false,
                        'error' => 'Current password is incorrect'
                    ], 401);
                }
            }

            // Update user
            $updateData = $request->only(['name', 'email']);
            if ($request->has('password')) {
                $updateData['password'] = Hash::make($request->password);
            }

            $user->update($updateData);

            return response()->json([
                'success' => true,
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'role' => $user->role
                    ]
                ],
                'message' => 'Profile updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Profile update failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get default tenant settings
     */
    protected function getDefaultTenantSettings(): array
    {
        return [
            'ai_providers' => [
                'openai' => ['enabled' => false, 'api_key' => null],
                'anthropic' => ['enabled' => false, 'api_key' => null],
                'groq' => ['enabled' => false, 'api_key' => null]
            ],
            'features' => [
                'widgets_enabled' => true,
                'workflows_enabled' => true,
                'analytics_enabled' => true,
                'api_access_enabled' => true
            ],
            'limits' => [
                'max_widgets' => 5,
                'max_workflows' => 10,
                'monthly_requests' => 1000,
                'max_users' => 3
            ]
        ];
    }

    /**
     * Get default branding
     */
    protected function getDefaultBranding(string $companyName): array
    {
        return [
            'primary_color' => '#3B82F6',
            'secondary_color' => '#1F2937',
            'logo_url' => null,
            'company_name' => $companyName,
            'widget_position' => 'bottom-right',
            'widget_theme' => 'light'
        ];
    }

    /**
     * Get subscription expiry date
     */
    protected function getSubscriptionExpiry(string $plan): ?\Carbon\Carbon
    {
        return match($plan) {
            'free' => null,
            'starter' => now()->addMonth(),
            'professional' => now()->addMonth(),
            'enterprise' => now()->addMonth(),
            default => null
        };
    }
}
