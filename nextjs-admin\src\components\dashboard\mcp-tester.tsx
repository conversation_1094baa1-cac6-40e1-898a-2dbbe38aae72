'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { apiClient } from '@/lib/api'
import { formatDuration } from '@/lib/utils'
import { Play, Loader2, CheckCircle, XCircle } from 'lucide-react'

interface TestResult {
  success: boolean
  session_id: string
  agents_executed?: string[]
  pipeline_summary?: any
  error?: string
  execution_time?: number
}

export function MCPTester() {
  const [isRunning, setIsRunning] = useState(false)
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [customMessage, setCustomMessage] = useState('What is artificial intelligence?')

  const runSingleTest = async () => {
    setIsRunning(true)
    try {
      const response = await apiClient.testMCPPipeline()
      if (response.success && response.data) {
        setTestResults(prev => [response.data, ...prev.slice(0, 4)]) // Keep last 5 results
      }
    } catch (error) {
      console.error('Test failed:', error)
    } finally {
      setIsRunning(false)
    }
  }

  const runFullPipelineTest = async () => {
    setIsRunning(true)
    try {
      const response = await apiClient.testFullPipeline()
      if (response.success && response.data) {
        // Process multiple test results
        const results = response.data.test_results || []
        setTestResults(prev => [...results.slice(0, 5), ...prev.slice(0, 0)]) // Replace with new results
      }
    } catch (error) {
      console.error('Full pipeline test failed:', error)
    } finally {
      setIsRunning(false)
    }
  }

  const runCustomTest = async () => {
    if (!customMessage.trim()) return
    
    setIsRunning(true)
    try {
      const response = await apiClient.processMCPRequest({
        message: customMessage,
        context: {}
      })
      if (response.success && response.data) {
        setTestResults(prev => [{
          success: true,
          session_id: response.data.session_id,
          agents_executed: response.data.agents_executed,
          pipeline_summary: response.data.pipeline_summary
        }, ...prev.slice(0, 4)])
      }
    } catch (error) {
      console.error('Custom test failed:', error)
    } finally {
      setIsRunning(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Test Controls */}
      <Card>
        <CardHeader>
          <CardTitle>MCP Pipeline Testing</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-4">
            <Button 
              onClick={runSingleTest} 
              disabled={isRunning}
              className="flex items-center gap-2"
            >
              {isRunning ? <Loader2 className="h-4 w-4 animate-spin" /> : <Play className="h-4 w-4" />}
              Quick Test
            </Button>
            
            <Button 
              onClick={runFullPipelineTest} 
              disabled={isRunning}
              variant="outline"
              className="flex items-center gap-2"
            >
              {isRunning ? <Loader2 className="h-4 w-4 animate-spin" /> : <Play className="h-4 w-4" />}
              Full Pipeline Test
            </Button>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Custom Message Test</label>
            <div className="flex gap-2">
              <input
                type="text"
                value={customMessage}
                onChange={(e) => setCustomMessage(e.target.value)}
                placeholder="Enter your test message..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isRunning}
              />
              <Button 
                onClick={runCustomTest} 
                disabled={isRunning || !customMessage.trim()}
                variant="outline"
              >
                {isRunning ? <Loader2 className="h-4 w-4 animate-spin" /> : <Play className="h-4 w-4" />}
                Test
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {testResults.map((result, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {result.success ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-500" />
                      )}
                      <span className="font-medium">
                        {result.success ? 'Success' : 'Failed'}
                      </span>
                      <Badge variant="outline">
                        Session: {result.session_id?.slice(-8)}
                      </Badge>
                    </div>
                    {result.execution_time && (
                      <span className="text-sm text-gray-500">
                        {formatDuration(result.execution_time)}
                      </span>
                    )}
                  </div>

                  {result.error && (
                    <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                      {result.error}
                    </div>
                  )}

                  {result.agents_executed && (
                    <div>
                      <span className="text-sm font-medium">Agents Executed:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {result.agents_executed.map((agent) => (
                          <Badge key={agent} variant="secondary" className="text-xs">
                            {agent}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {result.pipeline_summary && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Intent:</span>
                        <div className="text-gray-600">{result.pipeline_summary.intent_detected}</div>
                      </div>
                      <div>
                        <span className="font-medium">Confidence:</span>
                        <div className="text-gray-600">
                          {(result.pipeline_summary.confidence * 100).toFixed(1)}%
                        </div>
                      </div>
                      <div>
                        <span className="font-medium">Documents:</span>
                        <div className="text-gray-600">{result.pipeline_summary.documents_retrieved}</div>
                      </div>
                      <div>
                        <span className="font-medium">Tools:</span>
                        <div className="text-gray-600">{result.pipeline_summary.tools_executed}</div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
